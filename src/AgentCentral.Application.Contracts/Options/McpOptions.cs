namespace AgentCentral.Application.Contracts.Options
{
    /// <summary>
    /// MCP选项配置
    /// </summary>
    public class McpOptions
    {
        /// <summary>
        /// Authorization
        /// </summary>
        public string Authorization { get; set; }

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// API路径配置
        /// </summary>
        public McpApiUrls ApiUrls { get; set; }
    }

    /// <summary>
    /// MCP API路径配置
    /// </summary>
    public class McpApiUrls
    {
        /// <summary>
        /// 市场应用接口
        /// </summary>
        public string Marketplace { get; set; }

        /// <summary>
        /// 更新 MCP 服务接口
        /// </summary>
        public string UpdateMcpServer { get; set; }
    }
} 