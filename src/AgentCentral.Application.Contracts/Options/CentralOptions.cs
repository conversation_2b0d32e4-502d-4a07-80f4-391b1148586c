namespace AgentCentral.Application.Contracts.Options
{
    public class CentralOptions
    {
        public string Issuer { get; set; }

        public string Authority { get; set; }

        public bool RequireHttpsMetadata { get; set; }

        public bool ValidateIssuerSigningKey { get; set; } = true;

        public string Audience { get; set; }

        public string ClientId { get; set; }

        public string ClientSecret { get; set; }
    }
}
