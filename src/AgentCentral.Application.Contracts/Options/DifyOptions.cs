using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Options
{
    public class DifyOptions
    {
        public string BaseUrl { get; set; }

        public DifyUrls ApiUrls { get; set; }

        public IList<AgentUrl> AgentUrls { get; set; }

        public Workflows Workflows { get; set; }

    }

    public class DifyUrls
    {
        public string CreateApp { get; set; }

        public string CopyApp { get; set; }

        public string DeleteApp { get; set; }

        public string UpdateApp { get; set; }

        public string CreateTag { get; set; }
        public string UpdateTag { get; set; }
        public string DeleteTag { get; set; }
        public string RemoveTagBinding { get; set; }
        public string CreateTagBinding { get; set; }

        public string AutoReview { get; set; }
    }

    public class AgentUrl
    {
        public string Name { get; set; }

        public string Url { get; set; }

        public string ApiKey { get; set; }
    }
    public class Workflows
    {
        public string Url { get; set; }

        public string MpAppAutoReviewApiKey { get; set; }

        public string MpMcpAutoReviewApiKey { get; set; }

        public string MpAppPolicyApiKey { get; set; }

        public string RunUrl { get; set; }

        public string RunResultUrl { get; set; }
    }

}
