namespace AgentCentral.Application.Contracts.Options
{
    public class WorkflowOptions
    {
        /// <summary>
        /// 域名
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 写死的值
        /// </summary>
        public string AppSecret { get; set; }

        /// <summary>
        /// 写死的值
        /// </summary>
        public string GrantType { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string ProjectCode { get; set; }

        public string DefaultTenantId { get; set; } = "1";

        /// <summary>
        /// 接口地址
        /// </summary>
        public WorkflowApiUrls ApiUrls { get; set; }

        public class WorkflowApiUrls
        {
            /// <summary>
            /// 得到token
            /// </summary>
            public string GetProjectToken { get; set; }
            /// <summary>
            /// 启动流程
            /// </summary>
            public string Start { get; set; }
            /// <summary>
            /// 更新流程
            /// </summary>
            public string Complete { get; set; }
        }
    }
}
