using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Options
{
    public class KnowledgeBaseProcessOptions
    {
        public List<KnowledgeGroup> ProcessData { get; set; } = [];
    }

    public class KnowledgeGroup
    {
        public string Group { get; set; } = string.Empty;

        public List<KnowledgeProcess> Processes { get; set; } = [];
    }

    public class KnowledgeProcess
    {
        public string CompanyAndDepartment { get; set; } = string.Empty;

        public List<string> Processes { get; set; } = [];
    }
}
