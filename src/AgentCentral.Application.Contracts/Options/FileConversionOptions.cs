using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Options
{
    public class FileConversionOptions
    {
        public string Secret { get; set; }

        public List<string> ConvertFileExt { get; set; } = [];

        public List<string> ChangeFileExt { get; set; } = [];

        public List<string> UploadFileExt { get; set; } = [];

        public List<FileConvertSettints> ConvertSettints { get; set; } = [];

        public List<FileConvertSettints> ChangeExtSettings { get; set; } = [];
    }

    public class FileConvertSettints
    {
        public string SrcFileExt { get; set; }

        public string DstFileExt { get; set; }

        public string DstContentType { get; set; }
    }
}
