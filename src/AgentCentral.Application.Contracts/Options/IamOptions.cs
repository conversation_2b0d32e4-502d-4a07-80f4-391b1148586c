using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Options
{
    public class IamOptions
    {
        public string ApiKey { get; set; }
        public string ClientIdV { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string ClientSecretV { get; set; }

        /// <summary>
        /// Scope
        /// </summary>
        public List<string> Scope { get; set; }

        public string RedirectUri { get; set; }

        public string BaseUrl { get; set; }
        public ApiUrl ApiUrls { get; set; }
    }

    public class ApiUrl
    {
        public string Token { get; set; }
        public string Jwts { get; set; }
        public string User { get; set; }

        public string Oauth2Token { get; set; }
        public string TenantInfo { get; set; }
        public string ActivateUser { get; set; }
        public string DeactivateUser { get; set; }
        public string UpdateUser { get; set; }
    }
}