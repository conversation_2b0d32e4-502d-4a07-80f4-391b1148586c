using AgentCentral.Infrastructure.DataAnnotations;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.ResponseModels.AppReviewComment
{
    /// <summary>
    /// 应用评价响应模型
    /// </summary>
    public class AppReviewCommentResponse
    {
        public long Id { get; set; }

        /// <summary>
        /// 评分（1-5星）
        /// </summary>
        public int RatingStar { get; set; }

        /// <summary>
        /// 评价内容
        /// </summary>
        public string ReviewContent { get; set; }

        /// <summary>
        /// 评价时间
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }


        /// <summary>
        /// 用户名称
        /// </summary>
        [EmailDesensitize]
        public string UserName { get; set; }

        /// <summary>
        /// AttachmentId
        /// </summary>
        public long AttachmentId { get; set; }

        /// <summary>
        /// 访问路径
        /// </summary>
        public string AccessUrl { get; set; }
    }

    /// <summary>
    /// 应用评价统计响应模型
    /// </summary>
    public class AppReviewStatsResponse
    {
        /// <summary>
        /// 平均评分
        /// </summary>
        public double AverageRating { get; set; }

        /// <summary>
        /// 评价总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 各星级评价数量分布
        /// </summary>
        public Dictionary<int, int> RatingDistribution { get; set; }
    }
}