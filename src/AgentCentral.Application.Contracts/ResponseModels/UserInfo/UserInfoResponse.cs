using AgentCentral.Infrastructure.DataAnnotations;
using System;

namespace AgentCentral.Application.Contracts.ResponseModels.UserInfo
{
    /// <summary>
    /// 用户信息响应模型
    /// </summary>
    public class UserInfoResponse
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        [EmailDesensitize]
        public string UserName { get; set; }

        /// <summary>
        /// AttachmentId
        /// </summary>
        public string AccessUrl { get; set; }

        /// <summary>
        /// 用户配置信息
        /// </summary>
        public string UserProfile { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisterTime { get; set; }
    }
}