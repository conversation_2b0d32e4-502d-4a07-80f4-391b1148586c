using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.ResponseModels.UserInfo
{
    /// <summary>
    /// 更新用户完整信息DTO
    /// </summary>
    public class UpdateUserCompleteInfoDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public long Id { get; set; }

        /// <summary>
        /// 名
        /// </summary>
        [StringLength(100, ErrorMessage = "名长度不能超过100个字符")]
        public string FirstName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        [StringLength(100, ErrorMessage = "姓长度不能超过100个字符")]
        public string LastName { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string UserName { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [EmailAddress(ErrorMessage = "电子邮箱格式不正确")]
        [StringLength(100, ErrorMessage = "电子邮箱长度不能超过100个字符")]
        public string Email { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(50, ErrorMessage = "联系电话长度不能超过50个字符")]
        public string ContactNumber { get; set; }

        /// <summary>
        /// 用户类型
        /// </summary>
        public UserTypeEnum UserType { get; set; }

        /// <summary>
        /// 角色ID列表
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();

        /// <summary>
        /// 标签ID列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
} 