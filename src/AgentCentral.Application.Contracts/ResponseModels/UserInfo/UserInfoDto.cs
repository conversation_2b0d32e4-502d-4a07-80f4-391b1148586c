using System;
using System.Collections.Generic;
using AgentCentral.Application.Contracts.Dtos.UserInfo;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.ResponseModels.UserInfo
{
    /// <summary>
    /// 用户信息DTO
    /// </summary>
    public class UserInfoDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 名
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string ContactNumber { get; set; }

        /// <summary>
        /// 用户类型
        /// </summary>
        public UserTypeEnum UserType { get; set; }

        /// <summary>
        /// 用户类型名称
        /// </summary>
        public string UserTypeName { get; set; }

        /// <summary>
        /// 用户来源
        /// </summary>
        public UserSourceEnum UserSource { get; set; }

        /// <summary>
        /// 用户来源名称
        /// </summary>
        public string UserSourceName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string AccessUrl { get; set; }

        /// <summary>
        /// 用户配置信息
        /// </summary>
        public string UserProfile { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 角色列表
        /// </summary>
        public List<UserRoleDto> Roles { get; set; } = new List<UserRoleDto>();

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<UserTagDto> Tags { get; set; } = new List<UserTagDto>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string CreateName { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public long UpdateBy { get; set; }

        /// <summary>
        /// 更新人姓名
        /// </summary>
        public string UpdateName { get; set; }
    }
} 