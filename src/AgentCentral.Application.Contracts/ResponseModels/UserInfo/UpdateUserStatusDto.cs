using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.ResponseModels.UserInfo
{
    /// <summary>
    /// 更新用户状态DTO
    /// </summary>
    public class UpdateUserStatusDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public long UserId { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [Required(ErrorMessage = "状态不能为空")]
        public bool IsActive { get; set; }
    }
} 