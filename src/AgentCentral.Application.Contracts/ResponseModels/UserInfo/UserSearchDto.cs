using System.Collections.Generic;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.ResponseModels.UserInfo
{
    /// <summary>
    /// 用户查询DTO
    /// </summary>
    public class UserSearchDto : BaseSearchPageDto
    {
        /// <summary>
        /// 用户名查询
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 邮箱查询
        /// </summary>
        public string Email { get; set; }
        
        /// <summary>
        /// 租户
        /// </summary>
        public string TenantName { get; set; }

        /// <summary>
        /// 用户类型
        /// </summary>
        public UserTypeEnum? UserType { get; set; }

        /// <summary>
        /// 角色ID列表，支持多选
        /// </summary>
        public List<string> RoleIds { get; set; } = new List<string>();


        /// <summary>
        /// 租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 标签ID列表，支持多选
        /// </summary>
        public List<long> TagIds { get; set; } = new List<long>();

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }
    }
} 