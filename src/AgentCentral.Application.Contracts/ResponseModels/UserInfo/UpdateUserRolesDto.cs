using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.ResponseModels.UserInfo
{
    /// <summary>
    /// 更新用户角色DTO
    /// </summary>
    public class UpdateUserRolesDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public long UserId { get; set; }

        /// <summary>
        /// 角色ID列表
        /// </summary>
        [Required(ErrorMessage = "角色ID列表不能为空")]
        public List<string> Roles { get; set; } = new List<string>();

    }
} 