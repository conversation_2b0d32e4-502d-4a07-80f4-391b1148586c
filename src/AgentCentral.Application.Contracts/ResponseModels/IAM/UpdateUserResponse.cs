using System.Collections.Generic;
using System.Text.Json.Serialization;
using AgentCentral.Application.Contracts.ResponseModels.IAM;

namespace AgentCentral.Application.Contracts.ResponseModels.IAM
{
    /// <summary>
    /// 更新用户响应
    /// </summary>
    public class UpdateUserResponse : BaseResponse
    {
        /// <summary>
        /// 更新用户响应数据
        /// </summary>
        [JsonPropertyName("data")]
        public new UpdateUserData Data { get; set; }
    }

    /// <summary>
    /// 更新用户数据
    /// </summary>
    public class UpdateUserData
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; }

        /// <summary>
        /// 账户ID
        /// </summary>
        [JsonPropertyName("accountId")]
        public string AccountId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [JsonPropertyName("companyCode")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [JsonPropertyName("contactNumber")]
        public string ContactNumber { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [JsonPropertyName("email")]
        public string Email { get; set; }

        /// <summary>
        /// 名
        /// </summary>
        [JsonPropertyName("firstName")]
        public string FirstName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        [JsonPropertyName("lastName")]
        public string LastName { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [JsonPropertyName("userName")]
        public string UserName { get; set; }

        /// <summary>
        /// 用户状态
        /// </summary>
        [JsonPropertyName("userStatus")]
        public string UserStatus { get; set; }

        /// <summary>
        /// 是否主用户
        /// </summary>
        [JsonPropertyName("primaryUser")]
        public bool PrimaryUser { get; set; }

        /// <summary>
        /// 用户角色
        /// </summary>
        [JsonPropertyName("userRoles")]
        public List<UserRole> UserRoles { get; set; }

        /// <summary>
        /// 用户权限
        /// </summary>
        [JsonPropertyName("userPermissions")]
        public List<UserPermission> UserPermissions { get; set; }
    }

    /// <summary>
    /// 用户角色
    /// </summary>
    public class UserRole
    {
        /// <summary>
        /// ID
        /// </summary>
        [JsonPropertyName("id")]
        public long Id { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        [JsonPropertyName("roleId")]
        public long RoleId { get; set; }
    }

    /// <summary>
    /// 用户权限
    /// </summary>
    public class UserPermission
    {
        /// <summary>
        /// ID
        /// </summary>
        [JsonPropertyName("id")]
        public long Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; }

        /// <summary>
        /// 父ID
        /// </summary>
        [JsonPropertyName("parentId")]
        public long ParentId { get; set; }
    }
} 