using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.ResponseModels.IAM
{
    public class TenantInfoResponse : BaseResponse
    {
        /// <summary>
        /// 租户响应数据
        /// </summary>
        [JsonPropertyName("data")]
        public new List<TenantInfoData> Data { get; set; }
    }

    public class TenantInfoData
    {

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("code")]
        public string Code { get; set; }
    }
}
