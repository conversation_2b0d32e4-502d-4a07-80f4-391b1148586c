using System.Collections.Generic;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.ResponseModels.IAM
{
    /// <summary>
    /// 更新用户请求
    /// </summary>
    public class UpdateUserRequest
    {
        /// <summary>
        /// 名
        /// </summary>
        [JsonProperty("firstName")]
        public string FirstName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        [JsonProperty("lastName")]
        public string LastName { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [JsonProperty("contactNumber")]
        public string ContactNumber { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [JsonProperty("email")]
        public string Email { get; set; }

        /// <summary>
        /// 用户标签
        /// </summary>
        [JsonProperty("userTags")]
        public List<string> UserTags { get; set; }

        /// <summary>
        /// 外部信息
        /// </summary>
        [JsonProperty("externalInfo")]
        public Dictionary<string, object> ExternalInfo { get; set; }
    }
} 