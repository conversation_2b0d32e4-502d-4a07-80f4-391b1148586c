using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.DataAnnotations;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.ResponseModels.App
{
    public class AppDetailResponse
    {
        [JsonConverter(typeof(ValueToStringConverter))]
        public long Id { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用编码
        /// </summary>
        public string AppCode { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用模式
        /// </summary>
        public string AppMode { get; set; }

        /// <summary>
        /// 应用图标
        /// </summary>
        public string AppIcon { get; set; }

        /// <summary>
        /// 站点启用状态
        /// </summary>
        public bool EnableSite { get; set; }

        /// <summary>
        /// API启用状态
        /// </summary>
        public bool EnableApi { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 创建人名字
        /// </summary>
        [EmailDesensitize]
        public string CreateName { get; set; }

        /// <summary>
        /// 应用Tag
        /// </summary>
        public string AppTag { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public AppPermissionEnum AppPermission { get; set; }

        public AppStatusEnum AppStatus { get; set; }

        public AppSourceEnum AppSource { get; set; }

        public List<AttachmentOutputDto> Banners { get; set; } = [];

        public AttachmentOutputDto Video { get; set; }

        public List<AppDepartmentDto> Departments { get; set; } = [];

        /// <summary>
        /// 用户公司
        /// </summary>
        public string UserCompany { get; set; }

        /// <summary>
        /// 用户部门
        /// </summary>
        public string UserDepartment { get; set; }

        /// <summary>
        /// 内容质量评分
        /// </summary>
        public string ContentQualityScore { get; set; }

        /// <summary>
        /// 安全检查评分
        /// </summary>
        public string SafetyCheckScore { get; set; }

        /// <summary>
        /// 合规评分
        /// </summary>
        public string ComplianceScore { get; set; }

        /// <summary>
        /// 综合评分
        /// </summary>
        public string OverallScore { get; set; }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 变更描述
        /// </summary>
        public string ChangeDescription { get; set; }

        [JsonConverter(typeof(ValueToStringConverter))]
        public long MainAppId { get; set; }

        public bool CheckUpdate { get; set; }

        public int VersionNumber { get; set; }

        public string CurrentAppId { get; set; }

        public bool CurrentUserUpdate { get; set; }

        public bool DifyPublished { get; set; }

        public string GeneratorId { get; set; }


        /// <summary>
        /// 游客模式
        /// </summary>
        public bool GuestMode { get; set; }

        /// <summary>
        /// 角色ID列表
        /// </summary>
        public List<long> RoleIds { get; set; } = [];

        /// <summary>
        /// 用户标签ID列表
        /// </summary>
        public List<long> UserTagIds { get; set; } = [];
    }

    public class AttachmentOutputDto
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Url { get; set; }
    }
}