using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.ResponseModels.App
{
    public class AppDetailPageResponse : AppDetailResponse
    {

        ///// <summary>
        ///// 最近24小时使用总数(秒)
        ///// </summary>
        //public long Last24HoursSeconds { get; set; }

        /// <summary>
        /// 最近15天每天的使用总数
        /// </summary>
        public Dictionary<string, long> LastDaysSeconds { get; set; }

        /// <summary>
        /// 所有使用次数
        /// </summary>
        public int Runs { get; set; }

        /// <summary>
        /// 所有加入数
        /// </summary>
        public int Joins { get; set; }

        /// <summary>
        /// 星级
        /// </summary>
        public double Star { get; set; }

        public int TotalUsageSecond { get; set; }
    }
}
