using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.JsonConverts;
using System;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.ResponseModels.App
{
    public class AppReviewDetailPageResponse
    {
        public long Id { get; set; }
        public string AppName { get; set; }
        public string CompanyDepartment { get; set; }
        public string CreateName { get; set; }
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }
        public AppStatusEnum AppStatus { get; set; }
        public string ContentQualityScore { get; set; }
        public string SafetyCheckScore { get; set; }
        public string ComplianceScore { get; set; }
        public string OverallScore { get; set; }
        public string RejectReason { get; set; }
        public string AppCode { get; set; }
        public string AppMode { get; set; }
        public AppSourceEnum AppSource { get; set; }
        public string AppId { get; set; }

        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime ApprovalDate { get; set; }

        /// <summary>
        /// �ο�ģʽ��true-����false-������
        /// </summary>
        public bool GuestMode { get; set; }

        public bool GuestModeEdit {  get; set; }

        /// <summary>
        /// ����Permission��ӳ��General �� Private
        /// </summary>
        public string AppType { get; set; }

        /// <summary>
        /// 提交日期
        /// </summary>
        public DateTime? SubmitDate { get; set; }

        public AppPermissionEnum? AppPermission { get; set; }

        public string Reviewer { get; set; }
    }
}