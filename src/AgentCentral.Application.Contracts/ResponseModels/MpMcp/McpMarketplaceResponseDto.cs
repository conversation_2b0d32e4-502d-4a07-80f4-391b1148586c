using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.RequestModels.MpMcp
{
    /// <summary>
    /// MCP市场应用响应模型
    /// </summary>
    public class McpMarketplaceResponseDto
    {
        /// <summary>
        /// 状态码
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public List<McpAppDto> Data { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Msg { get; set; }
    }

    /// <summary>
    /// MCP应用信息
    /// </summary>
    public class McpAppDto
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 服务名称
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// category
        /// </summary>
        public string category { get; set; }

        /// <summary>
        /// 服务代码
        /// </summary>
        public string ServiceCode { get; set; }

        /// <summary>
        /// Logo URL
        /// </summary>
        public string LogoUrl { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public int VisitCount { get; set; }

        /// <summary>
        /// 评分
        /// </summary>
        public double Rating { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public int Source { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public List<string> Tags { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 提供者
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceType { get; set; }

        /// <summary>
        /// 服务值
        /// </summary>
        public string ServiceValue { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// 文档URL
        /// </summary>
        public string DocumentUrl { get; set; }

        /// <summary>
        /// 仓库URL
        /// </summary>
        public string RepositoryUrl { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 工具
        /// </summary>
        public string Tools { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public string UpdateTime { get; set; }
    }
} 