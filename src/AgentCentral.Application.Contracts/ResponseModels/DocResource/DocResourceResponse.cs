using System;
using System.Collections.Generic;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.RequestModels.DocResource;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.ResponseModels.DocResource
{
    public class DocResourceResponse : BaseDto
    {
        public List<AttachmentInfo> Attachments { get; set; }
        public string Name { get; set; }
        public string Company { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string JobDescription { get; set; }
        public List<string> RelatedCustomers { get; set; }
        public List<string> RelatedSuppliers { get; set; }
        public List<CommonProgramDto> CommonPrograms { get; set; }
    }

    public class AttachmentInfo : BaseDto
    {
        /// <summary>
        /// 文件原名
        /// </summary>
        public string RealName { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public string FileType { get; set; }


        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        [JsonConverter(typeof(UsDateFormatConverter))]
        public virtual DateTime CreateTime { get; set; }
    }
}