namespace AgentCentral.Application.Contracts.ResponseModels.Dify
{
    public class AutoReviewResponse
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public Data Data { get; set; }
    }

    public class Data
    {
        public decimal ContentQualityScore { get; set; }
        public decimal SafetyCheckScore { get; set; }
        public decimal ComplianceScore { get; set; }
        public decimal OverallScore { get; set; }
        public bool Status { get; set; }
        public string Reason { get; set; } = string.Empty;
    }
}