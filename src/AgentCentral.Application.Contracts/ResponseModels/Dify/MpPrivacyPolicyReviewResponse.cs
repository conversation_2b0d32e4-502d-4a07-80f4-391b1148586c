using Newtonsoft.Json;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.ResponseModels.Dify
{
    /// <summary>
    /// Mp_App隐私策略审核响应DTO
    /// </summary>
    public class MpPrivacyPolicyReviewResponse
    {
        /// <summary>
        /// 数据收集说明
        /// </summary>
        [JsonProperty("dataCollection")]
        public string DataCollection { get; set; } = string.Empty;

        /// <summary>
        /// 数据分类详情
        /// </summary>
        [JsonProperty("dataCategories")]
        public Dictionary<string, List<string>> DataCategories { get; set; } = new Dictionary<string, List<string>>();

        /// <summary>
        /// 数据共享说明
        /// </summary>
        [JsonProperty("dataSharing")]
        public string DataSharing { get; set; } = string.Empty;

        /// <summary>
        /// 数据共享详情
        /// </summary>
        [JsonProperty("dataSharingDetails")]
        public Dictionary<string, string> DataSharingDetails { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 用户追踪说明
        /// </summary>
        [JsonProperty("userTracking")]
        public string UserTracking { get; set; } = string.Empty;

        /// <summary>
        /// 用户追踪详情
        /// </summary>
        [JsonProperty("userTrackingDetails")]
        public Dictionary<string, string> UserTrackingDetails { get; set; } = new Dictionary<string, string>();
    }
} 