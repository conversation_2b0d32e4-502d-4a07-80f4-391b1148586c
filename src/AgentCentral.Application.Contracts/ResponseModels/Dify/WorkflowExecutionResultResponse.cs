using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.ResponseModels.Dify
{
    /// <summary>
    /// 工作流执行结果响应模型
    /// </summary>
    public class WorkflowExecutionResultResponse
    {
        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 关联的 Workflow ID
        /// </summary>
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 执行状态 running / succeeded / failed / stopped
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 任务输入内容
        /// </summary>
        [JsonProperty("inputs")]
        public object? Inputs { get; set; }

        /// <summary>
        /// 任务输出内容
        /// </summary>
        [JsonProperty("outputs")]
        public object? Outputs { get; set; }

        /// <summary>
        /// 错误原因
        /// </summary>
        [JsonProperty("error")]
        public string? Error { get; set; }

        /// <summary>
        /// 任务执行总步数
        /// </summary>
        [JsonProperty("total_steps")]
        public int TotalSteps { get; set; }

        /// <summary>
        /// 任务执行总 tokens
        /// </summary>
        [JsonProperty("total_tokens")]
        public int TotalTokens { get; set; }

        /// <summary>
        /// 任务开始时间
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }

        /// <summary>
        /// 任务结束时间
        /// </summary>
        [JsonProperty("finished_at")]
        public long? FinishedAt { get; set; }

        /// <summary>
        /// 耗时(s)
        /// </summary>
        [JsonProperty("elapsed_time")]
        public float ElapsedTime { get; set; }
    }
} 