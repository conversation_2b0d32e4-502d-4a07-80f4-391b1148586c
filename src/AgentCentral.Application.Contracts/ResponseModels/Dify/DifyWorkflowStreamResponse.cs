using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace AgentCentral.Application.Contracts.ResponseModels.Dify
{
    /// <summary>
    /// Dify Workflow 流式响应基类
    /// </summary>
    public class DifyWorkflowStreamEventBase
    {

        /// <summary>
        /// 任务 ID，用于请求跟踪和停止响应接口
        /// </summary>
        [JsonProperty("task_id")]
        public string TaskId { get; set; }

        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("workflow_run_id")]
        public string WorkflowRunId { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        [JsonProperty("event")]
        public string Event { get; set; }

        /// <summary>
        /// 创建时间戳
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }

        /// <summary>
        /// 详细内容
        /// </summary>
        [JsonProperty("data")]
        public JObject Data { get; set; }
    }

    /// <summary>
    /// workflow 开始执行事件
    /// </summary>
    public class WorkflowStartedEvent : DifyWorkflowStreamEventBase
    {
        /// <summary>
        /// 详细内容
        /// </summary>
        [JsonProperty("data")]
        public WorkflowStartedData Data { get; set; }
    }

    /// <summary>
    /// workflow 开始执行数据
    /// </summary>
    public class WorkflowStartedData
    {
        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// 关联 Workflow ID
        /// </summary>
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        /// <summary>
        /// 自增序号，App 内自增，从 1 开始
        /// </summary>
        [JsonProperty("sequence_number")]
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }
    }

    /// <summary>
    /// node 开始执行事件
    /// </summary>
    public class NodeStartedEvent : DifyWorkflowStreamEventBase
    {
        /// <summary>
        /// 详细内容
        /// </summary>
        [JsonProperty("data")]
        public NodeStartedData Data { get; set; }
    }

    /// <summary>
    /// node 开始执行数据
    /// </summary>
    public class NodeStartedData
    {
        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// 节点 ID
        /// </summary>
        [JsonProperty("node_id")]
        public string NodeId { get; set; }

        /// <summary>
        /// 节点类型
        /// </summary>
        [JsonProperty("node_type")]
        public string NodeType { get; set; }

        /// <summary>
        /// 节点名称
        /// </summary>
        [JsonProperty("title")]
        public string Title { get; set; }

        /// <summary>
        /// 执行序号，用于展示 Tracing Node 顺序
        /// </summary>
        [JsonProperty("index")]
        public int Index { get; set; }

        /// <summary>
        /// 前置节点 ID，用于画布展示执行路径
        /// </summary>
        [JsonProperty("predecessor_node_id")]
        public string PredecessorNodeId { get; set; }

        /// <summary>
        /// 节点中所有使用到的前置节点变量内容
        /// </summary>
        [JsonProperty("inputs")]
        public Dictionary<string, object> Inputs { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }
    }

    /// <summary>
    /// node 执行结束事件
    /// </summary>
    public class NodeFinishedEvent : DifyWorkflowStreamEventBase
    {
        /// <summary>
        /// 详细内容
        /// </summary>
        [JsonProperty("data")]
        public NodeFinishedData Data { get; set; }
    }

    /// <summary>
    /// node 执行结束数据
    /// </summary>
    public class NodeFinishedData
    {
        /// <summary>
        /// node 执行 ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// 节点 ID
        /// </summary>
        [JsonProperty("node_id")]
        public string NodeId { get; set; }

        /// <summary>
        /// 执行序号，用于展示 Tracing Node 顺序
        /// </summary>
        [JsonProperty("index")]
        public int Index { get; set; }

        /// <summary>
        /// 前置节点 ID，用于画布展示执行路径
        /// </summary>
        [JsonProperty("predecessor_node_id")]
        public string PredecessorNodeId { get; set; }

        /// <summary>
        /// 节点中所有使用到的前置节点变量内容
        /// </summary>
        [JsonProperty("inputs")]
        public Dictionary<string, object> Inputs { get; set; }

        /// <summary>
        /// 节点过程数据
        /// </summary>
        [JsonProperty("process_data")]
        public Dictionary<string, object> ProcessData { get; set; }

        /// <summary>
        /// 输出内容
        /// </summary>
        [JsonProperty("outputs")]
        public Dictionary<string, object> Outputs { get; set; }

        /// <summary>
        /// 执行状态 running / succeeded / failed / stopped
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; }

        /// <summary>
        /// 错误原因
        /// </summary>
        [JsonProperty("error")]
        public string Error { get; set; }

        /// <summary>
        /// 耗时(s)
        /// </summary>
        [JsonProperty("elapsed_time")]
        public float? ElapsedTime { get; set; }

        /// <summary>
        /// 元数据
        /// </summary>
        [JsonProperty("execution_metadata")]
        public ExecutionMetadata ExecutionMetadata { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }
    }

    /// <summary>
    /// 执行元数据
    /// </summary>
    public class ExecutionMetadata
    {
        /// <summary>
        /// 总使用 tokens
        /// </summary>
        [JsonProperty("total_tokens")]
        public int? TotalTokens { get; set; }

        /// <summary>
        /// 总费用
        /// </summary>
        [JsonProperty("total_price")]
        public decimal? TotalPrice { get; set; }

        /// <summary>
        /// 货币，如 USD / RMB
        /// </summary>
        [JsonProperty("currency")]
        public string Currency { get; set; }
    }

    /// <summary>
    /// workflow 执行结束事件
    /// </summary>
    public class WorkflowFinishedEvent : DifyWorkflowStreamEventBase
    {
        /// <summary>
        /// 详细内容
        /// </summary>
        [JsonProperty("data")]
        public WorkflowFinishedData Data { get; set; }
    }

    /// <summary>
    /// workflow 执行结束数据
    /// </summary>
    public class WorkflowFinishedData
    {
        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// 关联 Workflow ID
        /// </summary>
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        /// <summary>
        /// 执行状态 running / succeeded / failed / stopped
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; }

        /// <summary>
        /// 输出内容
        /// </summary>
        [JsonProperty("outputs")]
        public Dictionary<string, object> Outputs { get; set; }

        /// <summary>
        /// 错误原因
        /// </summary>
        [JsonProperty("error")]
        public string Error { get; set; }

        /// <summary>
        /// 耗时(s)
        /// </summary>
        [JsonProperty("elapsed_time")]
        public float? ElapsedTime { get; set; }

        /// <summary>
        /// 总使用 tokens
        /// </summary>
        [JsonProperty("total_tokens")]
        public int? TotalTokens { get; set; }

        /// <summary>
        /// 总步数（冗余），默认 0
        /// </summary>
        [JsonProperty("total_steps")]
        public int TotalSteps { get; set; } = 0;

        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [JsonProperty("finished_at")]
        public long FinishedAt { get; set; }
    }

    /// <summary>
    /// TTS 音频流事件
    /// </summary>
    public class TtsMessageEvent : DifyWorkflowStreamEventBase
    {
        /// <summary>
        /// 消息唯一 ID
        /// </summary>
        [JsonProperty("message_id")]
        public string MessageId { get; set; }

        /// <summary>
        /// 语音合成之后的音频块使用 Base64 编码之后的文本内容
        /// </summary>
        [JsonProperty("audio")]
        public string Audio { get; set; }

        /// <summary>
        /// 创建时间戳
        /// </summary>
        [JsonProperty("created_at")]
        public new long CreatedAt { get; set; }
    }

    /// <summary>
    /// TTS 音频流结束事件
    /// </summary>
    public class TtsMessageEndEvent : DifyWorkflowStreamEventBase
    {
        /// <summary>
        /// 消息唯一 ID
        /// </summary>
        [JsonProperty("message_id")]
        public string MessageId { get; set; }

        /// <summary>
        /// 结束事件是没有音频的，所以这里是空字符串
        /// </summary>
        [JsonProperty("audio")]
        public string Audio { get; set; } = "";

        /// <summary>
        /// 创建时间戳
        /// </summary>
        [JsonProperty("created_at")]
        public new long CreatedAt { get; set; }
    }

    /// <summary>
    /// Ping 事件，每 10s 一次的 ping 事件，保持连接存活
    /// </summary>
    public class PingEvent : DifyWorkflowStreamEventBase
    {
        // Ping 事件只需要基类的属性
    }

    /// <summary>
    /// 流式事件类型常量
    /// </summary>
    public static class DifyStreamEventTypes
    {
        /// <summary>
        /// workflow 开始执行
        /// </summary>
        public const string WorkflowStarted = "workflow_started";

        /// <summary>
        /// node 开始执行
        /// </summary>
        public const string NodeStarted = "node_started";

        /// <summary>
        /// node 执行结束
        /// </summary>
        public const string NodeFinished = "node_finished";

        /// <summary>
        /// workflow 执行结束
        /// </summary>
        public const string WorkflowFinished = "workflow_finished";

        /// <summary>
        /// TTS 音频流事件
        /// </summary>
        public const string TtsMessage = "tts_message";

        /// <summary>
        /// TTS 音频流结束事件
        /// </summary>
        public const string TtsMessageEnd = "tts_message_end";

        /// <summary>
        /// Ping 事件，每 10s 一次的 ping 事件，保持连接存活
        /// </summary>
        public const string Ping = "ping";
    }
}
