using System.Text.Json;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.ResponseModels.Dify
{
    public class MpAutoReviewResponse
    {
        [JsonProperty("contentQualityScore")]
        public decimal ContentQualityScore { get; set; }

        [JsonProperty("safetyCheckScore")]
        public decimal SafetyCheckScore { get; set; }

        [JsonProperty("complianceScore")]
        public decimal ComplianceScore { get; set; }

        [JsonProperty("overallScore")]
        public decimal OverallScore { get; set; }

        [JsonProperty("status")]
        public bool Status { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; } = string.Empty;

        [JsonProperty("privacyAnalysis")]
        public string PrivacyAnalysis { get; set; } = string.Empty;

        public bool IsPrivacyAnalysisPass()
        {
            return !"fail".Equals(PrivacyAnalysis);
        }

        public bool IsPass()
        {
            return IsPrivacyAnalysisPass() && Status;
        }

        public static MpAutoReviewResponse parseFromOutputs(string outputs)
        {
            // 格式处理
            var cleanJson = Regex.Replace(outputs, @"^```json\n|\n```$", "", RegexOptions.Singleline);
            return JsonConvert.DeserializeObject<MpAutoReviewResponse>(cleanJson);
        }
    }
}