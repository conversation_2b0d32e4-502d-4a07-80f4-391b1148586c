using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.ResponseModels.Dify
{
    /// <summary>
    /// Dify Workflow 执行响应模型（blocking 模式）
    /// </summary>
    public class RunDifyWorkflowResponse
    {

        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("workflow_run_id")]
        public string WorkflowRunId { get; set; }

        /// <summary>
        /// 任务 ID，用于请求跟踪和停止响应接口
        /// </summary>
        [JsonProperty("task_id")]
        public string TaskId { get; set; }

        /// <summary>
        /// 详细内容
        /// </summary>
        [JsonProperty("data")]
        public WorkflowRunData Data { get; set; }
    }

    /// <summary>
    /// Workflow 运行数据
    /// </summary>
    public class WorkflowRunData
    {
        /// <summary>
        /// workflow 执行 ID
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// 关联 Workflow ID
        /// </summary>
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        /// <summary>
        /// 执行状态, running / succeeded / failed / stopped
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; }

        /// <summary>
        /// 输出内容
        /// </summary>
        [JsonProperty("outputs")]
        public object Outputs { get; set; }

        /// <summary>
        /// 错误原因
        /// </summary>
        [JsonProperty("error")]
        public string Error { get; set; }

        /// <summary>
        /// 耗时(s)
        /// </summary>
        [JsonProperty("elapsed_time")]
        public float? ElapsedTime { get; set; }

        /// <summary>
        /// 总使用 tokens
        /// </summary>
        [JsonProperty("total_tokens")]
        public int? TotalTokens { get; set; }

        /// <summary>
        /// 总步数（冗余），默认 0
        /// </summary>
        [JsonProperty("total_steps")]
        public int TotalSteps { get; set; } = 0;

        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [JsonProperty("finished_at")]
        public long? FinishedAt { get; set; }
    }

    /// <summary>
    /// 执行状态常量
    /// </summary>
    public static class ExecutionStatus
    {
        /// <summary>
        /// 运行中
        /// </summary>
        public const string Running = "running";

        /// <summary>
        /// 成功
        /// </summary>
        public const string Succeeded = "succeeded";

        /// <summary>
        /// 失败
        /// </summary>
        public const string Failed = "failed";

        /// <summary>
        /// 已停止
        /// </summary>
        public const string Stopped = "stopped";
    }

}
