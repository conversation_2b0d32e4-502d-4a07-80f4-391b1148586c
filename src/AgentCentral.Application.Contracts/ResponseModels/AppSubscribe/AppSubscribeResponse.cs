using AgentCentral.Infrastructure.DataAnnotations;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;

namespace AgentCentral.Application.Contracts.ResponseModels.AppSubscribe
{
    /// <summary>
    /// 应用订阅响应模型
    /// </summary>
    public class AppSubscribeResponse
    {
        /// <summary>
        /// 用户头像Id
        /// </summary>
        public string AccessUrl { get; set; }

        /// <summary>
        /// 订阅时间
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        [EmailDesensitize]
        public string UserName { get; set; }
    }
}