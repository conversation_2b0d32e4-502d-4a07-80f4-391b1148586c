using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.ResponseModels.UserTag
{
    /// <summary>
    /// 用户标签DTO
    /// </summary>
    public class UserTagDto
    {
        /// <summary>
        /// 标签ID
        /// </summary>
        [JsonConverter(typeof(ValueToStringConverter))]
        public long Id { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string TagName { get; set; }

        /// <summary>
        /// 标签编码
        /// </summary>
        public string TagCode { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        public string TagType { get; set; }
    }
}
