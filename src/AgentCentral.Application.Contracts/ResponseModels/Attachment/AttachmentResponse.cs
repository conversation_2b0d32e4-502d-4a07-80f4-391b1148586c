using System;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using MathNet.Numerics.Interpolation;

namespace AgentCentral.Application.Contracts.ResponseModels.Attachment;

public class AttachmentResponse
{
    /// <summary>
    /// Name of the file in the system
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    /// Original name of the file
    /// </summary>
    public string RealName { get; set; }

    /// <summary>
    /// URL to access the attachment
    /// </summary>
    public string AccessUrl { get; set; }

    /// <summary>
    /// Unique identifier for the attachment (duplicate property, consider removing)
    /// </summary>
    public long AttachmentId { get; set; }

    /// <summary>
    /// Type of the file
    /// </summary>
    public string FileType { get; set; }

    /// <summary>
    /// Date and time when the attachment was created
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// Type of storage for the attachment
    /// </summary>
    //public AttachmentStoreTypeEnum StoreType { get; set; }
    public int StoreType { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
}