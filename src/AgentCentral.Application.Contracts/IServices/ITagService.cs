using AgentCentral.Application.Contracts.Dtos.Tag;
using AgentCentral.Application.Contracts.RequestModels.Tag;
using AgentCentral.Application.Contracts.ResponseModels.Tag;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface ITagService
    {
        Task<List<TagResponse>> GetTags(string tagName);

        Task<bool> DeleteTagAsync(long tagId);

        Task<bool> DeleteTagByDifyId(string difyTagId);

        Task<bool> CreateTagRequest(CreateTagRequest request);

        Task<bool> UpdateTagByDify(string difyTagId, UpdateTagRequest request);

        Task<bool> CreateTagAsync(CreateTagDto createDto);

        Task<bool> UpdateTagAsync(long tagId, CreateTagDto createDto);
    }
}
