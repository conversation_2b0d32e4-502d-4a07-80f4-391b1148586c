using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.ResponseModels.IAM;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IService
{
    public interface IIamService : IScopedService
    {
        Task<object> TokenByCodeAsync(string code, string redirectUri);

        Task<(bool isValid, TokenValidatedInfoOutputDto tokenInfo)> ValidateTokenAsync(string token);


        Task<TenantInfoResponse> GetTenantInfoAsync(string token, List<string> tenantIds);

        /// <summary>
        /// 启用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>基础响应</returns>
        Task<BaseResponse> ActivateUserAsync(string userId, string token);

        /// <summary>
        /// 禁用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>基础响应</returns>
        Task<BaseResponse> DeactivateUserAsync(string userId, string token);

        /// <summary>
        /// 更新用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="request">更新用户请求</param>
        /// <returns>更新用户响应</returns>
        Task<UpdateUserResponse> UpdateUserAsync(string userId, UpdateUserRequest request, string token);
    }
}
