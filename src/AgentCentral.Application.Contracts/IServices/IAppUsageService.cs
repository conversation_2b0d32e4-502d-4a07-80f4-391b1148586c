using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.RequestModels.AppUsage;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Application.Contracts.ResponseModels.AppUsage;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IService.App
{
    public interface IAppUsageService : IScopedService
    {
        /// <summary>
        /// 获取应用使用记录
        /// </summary>
        Task<AppUsageResponse> GetByAppIdAsync(string appId);

        /// <summary>
        /// 创建应用使用记录
        /// </summary>
        Task<AppUsageResponse> CreateAsync(CreateAppUsageRequest request);

        /// <summary>
        /// 更新应用使用记录
        /// </summary>
        Task<AppUsageResponse> UpdateAsync(string appId, UpdateAppUsageRequest request);

        /// <summary>
        /// 删除应用使用记录
        /// </summary>
        Task<bool> DeleteAsync(string appId);

        /// <summary>
        /// 获取最近24小时使用总数
        /// </summary>
        Task<long> GetLast24HoursUsageAsync(string appId);

        /// <summary>
        /// 获取最近7天的使用总数
        /// </summary>
        /// <returns>key为日期(yyyy-MM-dd)，value为使用总数</returns>
        Task<Dictionary<string, long>> GetLast15DaysUsageAsync(long mainAppId, DateTime createTime);

    }
}