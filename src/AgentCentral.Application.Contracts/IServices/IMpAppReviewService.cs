using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Application.Contracts.ResponseModels.Dify;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// Mp_App审核服务接口
    /// </summary>
    public interface IMpAppReviewService : IScopedService
    {
        /// <summary>
        /// 获取审核应用分页列表（统一接口，支持多状态查询）
        /// </summary>
        /// <param name="searchDto">搜索条件，通过AppStatus数组区分不同审核状态</param>
        /// <returns>分页结果</returns>
        Task<PageModelDto<MpAppReviewListDto>> GetReviewPageListAsync(MpAppReviewSearchPageDto searchDto);

        /// <summary>
        /// 获取应用详情
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>应用详情</returns>
        Task<MpAppDetailDto> GetAppDetailAsync(long appId);

        /// <summary>
        /// 更新隐私策略审核结果
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="reviewResult">审核结果</param>
        /// <returns>更新是否成功</returns>
        Task<bool> UpdatePrivacyPolicyReviewResultAsync(long appId, MpPrivacyPolicyReviewResponse reviewResult);

        /// <summary>
        /// 清空隐私策略审核结果
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>清空是否成功</returns>
        Task<bool> ClearPrivacyPolicyReviewResultAsync(long appId);

    }
} 