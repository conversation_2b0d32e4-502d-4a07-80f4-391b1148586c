using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.RequestModels.UserInfo;
using AgentCentral.Application.Contracts.ResponseModels.UserInfo;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface IUserInfoService : IScopedService
    {
        /// <summary>
        /// 获取用户信息
        /// </summary>
        Task<UserInfoResponse> GetUserInfoAsync(long? userId);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        Task<bool> UpdateUserInfoAsync(UpdateUserInfoRequest request);

        /// <summary>
        /// 获取用户列表（分页）
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>用户列表</returns>
        Task<PageModelDto<UserInfoDto>> GetUserListAsync(UserSearchDto searchDto);

        /// <summary>
        /// 更新用户角色
        /// </summary>
        /// <param name="updateDto">更新用户角色DTO</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserRolesAsync(UpdateUserRolesDto updateDto);

        /// <summary>
        /// 更新用户状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserStatusAsync(long userId, bool isActive, string accessToken);

        /// <summary>
        /// 更新用户完整信息（包括角色和标签）
        /// </summary>
        /// <param name="updateDto">更新用户完整信息DTO</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserCompleteInfoAsync(UpdateUserCompleteInfoDto updateDto);
    }
}