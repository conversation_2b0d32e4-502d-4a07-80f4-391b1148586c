using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Infrastructure;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// Application parameter service interface
    /// </summary>
    public interface IAppParamService : IBaseAppService, IScopedService
    {
        /// <summary>
        /// Get parameter definitions by application ID
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>List of parameter definitions</returns>
        Task<List<AppParamDto>> GetParamDefinitionsAsync(long appId);

        /// <summary>
        /// Get parameter values by dify application ID
        /// </summary>
        /// <param name="appId">Dify application ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>List of parameter values</returns>
        Task<List<AppParamValueDto>> GetParamValuesAsync(string appId, long userId);

        /// <summary>
        /// Create parameter definitions in batch
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="dtos">List of parameter definitions to create</param>
        /// <returns>List of created parameter definitions</returns>
        Task<List<AppParamDto>> CreateParamDefinitionsAsync(string appId, List<CreateAppParamDto> dtos);

        Task<bool> UpdateParamDefinitionsAsync(string appId, List<CreateAppParamDto> dtos);

        /// <summary>
        /// Save parameter values in batch
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="values">List of parameter values to save</param>
        /// <returns>List of saved parameter values</returns>
        Task<List<AppParamValueDto>> SaveParamValuesAsync(long appId, List<SaveAppParamValueDto> values);

        /// <summary>
        /// Copy application parameter definitions and values
        /// </summary>
        /// <param name="sourceAppId">Source application ID</param>
        /// <param name="targetAppId">Target application ID</param>
        /// <returns>Whether the copy operation was successful</returns>
        Task<bool> CopyAppParamsAndValuesAsync(long sourceAppId, long targetAppId);

        /// <summary>
        /// Check application parameter configuration status
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>Parameter configuration status</returns>
        Task<AppParamStatusDto> CheckParamStatusAsync(long appId);

        Task<Dictionary<string, string>> GetParamDictionaryAsync(List<string> keys);
    }
}
