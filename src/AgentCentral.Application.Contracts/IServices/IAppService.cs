using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface IAppService : IBaseAppService, IScopedService
    {
        /// <summary>
        /// 获取所有应用
        /// </summary>
        Task<List<AppDetailResponse>> GetListAsync();

        /// <summary>
        /// 获取所有应用
        /// </summary>
        Task<List<AppDetailResponse>> GetMyAllAsync();

        /// <summary>
        /// 分页查询应用
        /// </summary>
        Task<PageModelDto<AppDetailPageResponse>> GetPageListAsync(AppSearchPageDto searchDto, AppQueryModeEnum queryMode = AppQueryModeEnum.Public);

        /// <summary>
        /// 获取应用详情
        /// </summary>
        Task<AppDetailResponse> GetByAppIdAsync(string appId);

        /// <summary>
        /// 创建应用
        /// </summary>
        Task<AppDetailResponse> CreateAsync(CreateAppRequest request);

        Task<AppDetailResponse> CreateAppAsync(CreateAppDto createApp);

        Task<AppDetailResponse> CopyAppAsync(string appId, CreateAppDto createApp, long mainAppId = 0);

        /// <summary>
        /// 更新应用
        /// </summary>
        Task<AppDetailResponse> UpdateAsync(string appId, UpdateAppRequest request);

        /// <summary>
        /// 更新应用
        /// </summary>
        Task<AppDetailResponse> UpdateAppAsync(string appId, UpdateAppDto appDto);

        /// <summary>
        /// 删除应用
        /// </summary>
        Task<bool> DeleteAsync(string appId);

        /// <summary>
        /// 获取当前用户的应用列表
        /// </summary>
        /// <param name="searchDto">查询参数</param>
        /// <returns>分页后的应用列表</returns>
        Task<PageModelDto<AppDetailResponse>> GetUserAppsAsync(AppSearchDto searchDto);

        Task<bool> UpdateAppStatusAsync(string appId, AppStatusEnum appStatus);

        Task<bool> UpdateDifyPublishedAsync(string appId, bool difyPublished);

        /// <summary>
        /// 获取应用审核列表
        /// </summary>
        /// <param name="searchDto">查询参数</param>
        /// <returns>分页后的应用审核列表</returns>
        Task<PageModelDto<AppReviewDetailPageResponse>> GetReviewPageListAsync(AppReviewSearchPageDto searchDto);

        /// <summary>
        /// 更新应用权限
        /// </summary>
        /// <param name="dto">更新权限DTO</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateAppPermissionAsync(UpdateAppPermissionDto dto);

        /// <summary>
        /// Dify platform-create application from source app
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<AppDetailResponse> CreateAgentFromSourceAsync(CreateAgentFromSourceRequest request);

        /// <summary>
        /// 处理应用审批通过后的业务逻辑
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>处理结果</returns>
        Task<bool> HandleAppApprovalAsync(long appId);


        /// <summary>
        /// 更新应用的Guest Mode状态
        /// </summary>
        /// <param name="dto">Guest Mode更新请求</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateGuestModeAsync(UpdateGuestModeDto dto);

        /// <summary>
        /// 批量更新应用的Guest Mode状态
        /// </summary>
        /// <param name="dto">批量Guest Mode更新请求</param>
        /// <returns>是否更新成功</returns>
        Task<bool> BatchUpdateGuestModeAsync(BatchUpdateGuestModeDto dto);
    }
}