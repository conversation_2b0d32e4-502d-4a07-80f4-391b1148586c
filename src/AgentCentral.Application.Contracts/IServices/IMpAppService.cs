using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.MpApp;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// 应用市场应用服务接口
    /// </summary>
    public interface IMpAppService
    {
        /// <summary>
        /// 通过表单数据更新应用
        /// </summary>
        /// <param name="request">表单更新请求</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateMpAppFromFormAsync(UpdateMpAppFormRequest request);

    }
} 