using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// Mp_Mcp服务接口
    /// </summary>
    public interface IMpMcpService : IScopedService
    {
        /// <summary>
        /// 获取审核MCP服务分页列表（统一接口，支持多状态查询）
        /// </summary>
        /// <param name="searchDto">搜索条件，通过Status数组区分不同审核状态</param>
        /// <returns>分页结果</returns>
        Task<PageModelDto<MpMcpListDto>> GetReviewPageListAsync(MpMcpSearchPageDto searchDto);

        /// <summary>
        /// 获取MCP服务详情
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <returns>MCP服务详情</returns>
        Task<MpMcpDetailDto> GetMcpDetailAsync(long mcpId);

        /// <summary>
        /// 通过表单数据更新MCP服务
        /// </summary>
        /// <param name="request">表单更新请求</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateMpMcpAsync(UpdateMpMcpDto request);

        Task<List<string>> ListProvieders(string keyword, int pageNumber = 1, int pageSize = 10);
    }
} 