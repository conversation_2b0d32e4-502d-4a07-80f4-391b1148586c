using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// Parameter validation service interface
    /// </summary>
    public interface IAppParamValidationService : IScopedService
    {
        /// <summary>
        /// Validate parameter definition
        /// </summary>
        /// <param name="paramType">Parameter</param>
        void ValidateParamDefinition(CreateAppParamDto createAppParam);

        /// <summary>
        /// Validate parameter value
        /// </summary>
        /// <param name="param">Parameter definition</param>
        /// <param name="value">Parameter value</param>
        /// <returns>Validation result</returns>
        (bool IsValid, string ErrorMessage) ValidateParamValue(AppParamDto param, string value);
    }
}
