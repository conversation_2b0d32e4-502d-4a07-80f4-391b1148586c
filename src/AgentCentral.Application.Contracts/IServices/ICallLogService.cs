using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.CallLog;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// Call log service interface
    /// </summary>
    public interface ICallLogService : IScopedService
    {
        /// <summary>
        /// Get call support statistics
        /// </summary>
        /// <returns>Call support statistics</returns>
        Task<CallSupportStatisticsDto> GetCallSupportStatisticsAsync();

        /// <summary>
        /// Search call logs with pagination
        /// </summary>
        /// <param name="searchParams">Search parameters</param>
        /// <returns>Page response with call log records</returns>
        Task<CallLogPageDto<List<CallLogRecordDto>>> SearchCallLogsAsync(CallLogSearchParamsDto searchParams);
    }
}