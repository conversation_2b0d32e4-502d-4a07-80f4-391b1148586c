using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.ResponseModels.Attachment;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;
using Microsoft.AspNetCore.Http;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface IAttachmentService : IScopedService
    {
        Task<AttachmentResponse> UploadAsync(IFormFile formFile);

        Task<(Stream, string)> GetAttachmentStreamAsync(long attachmentId);

        Task<string> GetAccessUrlAsync(long id, CancellationToken cancellationToken);

        Task<string> GetAccessUrlByAsync(string id, CancellationToken cancellationToken = default);

        Task<string> GetAccessUrlAsync(string fileName);

        Task<(Stream, string)> GetBatchAttachmentsStreamAsync(long processId, AttachmentTypeEnum attachmentType);

        Task<List<AttachmentResponse>> GetAttachmentsAsync(long businessId, AttachmentTypeEnum attachmentType);

        Task<AttachmentResponse> GetAttachmentByIdAsync(long attachmentId);
    }
}