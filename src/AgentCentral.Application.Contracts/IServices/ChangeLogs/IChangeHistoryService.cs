using AgentCentral.Application.Contracts.Dtos.ChangeLog;
using AgentCentral.Infrastructure;
using Item.Internal.ChangeLog.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.IServices.ChangeLogs;

/// <summary>
/// Change History Query Service
/// </summary>
public interface IChangeHistoryService : IScopedService
{
    /// <summary>
    /// Retrieves a paged result of change log data based on the provided search model
    /// </summary>
    /// <param name="searchModel">The search criteria for change logs</param>
    /// <returns>A paged result of change log data</returns>
    Task<PageingResultModel<ChangeLogData>> GetChangeLogsAsync(SearchModel searchModel);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    Task<List<ChangeLogDto>> GetChangeLogsAsync(long businessId);
}
