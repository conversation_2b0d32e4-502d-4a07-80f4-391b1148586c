using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.RequestModels.AppReviewComment;
using AgentCentral.Application.Contracts.ResponseModels.AppReviewComment;
using AgentCentral.Infrastructure;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface IAppReviewCommentService : IScopedService
    {
        /// <summary>
        /// 获取应用评价列表
        /// </summary>
        Task<PageModelDto<AppReviewCommentResponse>> GetReviewCommentsAsync(string appId, BaseSearchPageDto searchPageDto);

        /// <summary>
        /// 获取应用评价列表
        /// </summary>
        Task<List<AppReviewCommentResponse>> GetReviewCommentsListAsync(string appId);

        /// <summary>
        /// 获取应用评价统计信息
        /// </summary>
        Task<AppReviewStatsResponse> GetReviewStatsAsync(string appId);

        /// <summary>
        /// 创建应用评价
        /// </summary>
        Task<bool> CreateReviewAsync(string appId, CreateAppReviewCommentRequest request);

        /// <summary>
        /// 根据Id删除应用评价
        /// </summary>
        Task<bool> DeleteByIdsAsync(List<long> ids);
    }
}