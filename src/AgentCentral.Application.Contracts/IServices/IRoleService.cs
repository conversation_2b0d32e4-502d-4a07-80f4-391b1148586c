using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// 角色服务接口
    /// </summary>
    public interface IRoleService
    {
        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <param name="company">公司/租户</param>
        /// <param name="keyword">关键字查询</param>
        /// <param name="departmentId">部门ID</param>
        /// <returns>角色列表</returns>
        Task<List<RoleDto>> GetRolesAsync(string company, string keyword = null, long? departmentId = null);

        /// <summary>
        /// 分页获取角色列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页角色列表</returns>
        Task<PageModelDto<RoleDto>> GetRolePageListAsync(RoleSearchDto searchDto);

        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="roleDto">角色信息</param>
        /// <returns>角色ID</returns>
        Task<long> CreateRoleAsync(RoleDto roleDto);

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="roleDto">角色信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateRoleAsync(RoleDto roleDto);

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteRoleAsync(long id);

        /// <summary>
        /// 获取角色详情
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>角色信息</returns>
        Task<RoleDto> GetRoleAsync(long id);

        /// <summary>
        /// 根据部门ID获取角色列表
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="company">公司/租户</param>
        /// <returns>角色列表</returns>
        Task<List<RoleDto>> GetRolesByDepartmentAsync(long departmentId, string company);

        /// <summary>
        /// 检查角色编码是否已存在
        /// </summary>
        /// <param name="roleCode">角色编码</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsRoleCodeExistsAsync(string tenantId, string roleCode, long? departmentId = null, long? excludeId = null);

        /// <summary>
        /// 获取部门角色树形结构
        /// </summary>
        /// <param name="company">公司/租户</param>
        /// <param name="keyword">关键字查询</param>
        /// <returns>部门角色树形结构</returns>
        Task<List<DepartmentRoleTreeDto>> GetDepartmentRoleTreeAsync(string company, string keyword = null);

        /// <summary>
        /// 获取公司部门角色树形结构
        /// </summary>
        /// <param name="keyword">关键字查询</param>
        /// <returns>公司部门角色树形结构</returns>
        Task<List<CompanyDepartmentRoleTreeDto>> GetCompanyDepartmentRoleTreeAsync(string token, string keyword = null);

        /// <summary>
        /// 判断角色是否与用户关联
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否存在关联关系</returns>
        Task<bool> HasUserAssociatedAsync(long roleId);
    }
}
