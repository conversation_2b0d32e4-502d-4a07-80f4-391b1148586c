using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices.Workflow
{
    /// <summary>
    /// Mp_App工作流服务接口
    /// 负责处理Mp_App的审批流程，包括提交、审批、拒绝等操作
    /// </summary>
    public interface IMpAppWorkflowService : IScopedService
    {

        /// <summary>
        /// 审批通过Mp_App
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="comments">审批备注（可选）</param>
        /// <returns>审批是否成功</returns>
        Task<bool> ApproveAsync(long appId, string comments = null);

        /// <summary>
        /// 拒绝Mp_App审批
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="comments">拒绝原因（可选）</param>
        /// <returns>拒绝操作是否成功</returns>
        Task<bool> RejectAsync(long appId, string comments = null);

        /// <summary>
        /// AI自动审批Mp_App
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>自动审批结果：true表示通过，false表示拒绝</returns>
        Task<bool> AutoApprovalAsync(long appId);

        /// <summary>
        /// 下架Mp_App
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="comments">下架原因（可选）</param>
        /// <returns>下架操作是否成功</returns>
        Task<bool> DelistAsync(long appId, string comments = null);

        /// <summary>
        /// 重新上架Mp_App
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="comments">重新上架备注（可选）</param>
        /// <returns>重新上架操作是否成功</returns>
        Task<bool> RelistAsync(long appId, string comments = null);

        /// <summary>
        /// 批量审批通过Mp_App
        /// </summary>
        /// <param name="appIds">应用ID列表</param>
        /// <param name="comments">审批备注（可选）</param>
        /// <returns>操作是否成功，失败时抛出异常</returns>
        Task<bool> BatchApproveAsync(List<long> appIds, string comments = null);

        /// <summary>
        /// 批量拒绝Mp_App
        /// </summary>
        /// <param name="appIds">应用ID列表</param>
        /// <param name="comments">拒绝原因（可选）</param>
        /// <returns>操作是否成功，失败时抛出异常</returns>
        Task<bool> BatchRejectAsync(List<long> appIds, string comments = null);

        /// <summary>
        /// 批量下架Mp_App
        /// </summary>
        /// <param name="appIds">应用ID列表</param>
        /// <param name="comments">下架原因（可选）</param>
        /// <returns>操作是否成功，失败时抛出异常</returns>
        Task<bool> BatchDelistAsync(List<long> appIds, string comments = null);

    }
}