using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices.Workflow
{
    /// <summary>
    /// Mp_Mcp工作流服务接口
    /// 负责处理Mp_Mcp的审批流程，包括提交、审批、拒绝等操作
    /// </summary>
    public interface IMpMcpWorkflowService : IScopedService
    {
        /// <summary>
        /// 审批通过Mp_Mcp
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <param name="comments">审批备注（可选）</param>
        /// <returns>审批是否成功</returns>
        Task<bool> ApproveAsync(long mcpId, string comments = null);

        /// <summary>
        /// 拒绝Mp_Mcp审批
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <param name="comments">拒绝原因（可选）</param>
        /// <returns>拒绝操作是否成功</returns>
        Task<bool> RejectAsync(long mcpId, string comments = null);

        /// <summary>
        /// 获取Mp_Mcp审批历史记录
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <returns>审批历史记录列表，按时间倒序排列</returns>
        Task<List<MpMcpWorkflowLogDto>> GetWorkflowLogsAsync(long mcpId);

        /// <summary>
        /// AI自动审批Mp_Mcp
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <returns>自动审批结果：true表示通过，false表示拒绝</returns>
        Task<bool> AutoApprovalAsync(long mcpId);

        /// <summary>
        /// 下架Mp_Mcp
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <param name="comments">下架原因（可选）</param>
        /// <returns>下架操作是否成功</returns>
        Task<bool> DelistAsync(long mcpId, string comments = null);

        /// <summary>
        /// 重新上架Mp_Mcp
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <param name="comments">重新上架备注（可选）</param>
        /// <returns>重新上架操作是否成功</returns>
        Task<bool> RelistAsync(long mcpId, string comments = null);

        /// <summary>
        /// 批量审批通过Mp_Mcp
        /// </summary>
        /// <param name="mcpIds">MCP服务ID列表</param>
        /// <param name="comments">审批备注（可选）</param>
        /// <returns>操作是否成功，失败时抛出异常</returns>
        Task<bool> BatchApproveAsync(List<long> mcpIds, string comments = null);

        /// <summary>
        /// 批量拒绝Mp_Mcp
        /// </summary>
        /// <param name="mcpIds">MCP服务ID列表</param>
        /// <param name="comments">拒绝原因（可选）</param>
        /// <returns>操作是否成功，失败时抛出异常</returns>
        Task<bool> BatchRejectAsync(List<long> mcpIds, string comments = null);

        /// <summary>
        /// 批量下架Mp_Mcp
        /// </summary>
        /// <param name="mcpIds">MCP服务ID列表</param>
        /// <param name="comments">下架原因（可选）</param>
        /// <returns>操作是否成功，失败时抛出异常</returns>
        Task<bool> BatchDelistAsync(List<long> mcpIds, string comments = null);
    }
} 