using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ReviewRecords;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Infrastructure;
using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.Exceptions;

namespace AgentCentral.Application.Contracts.IServices.Workflow
{
    /// <summary>
    /// 工作流接口
    /// </summary>
    public interface IWorkflowService : IScopedService
    {
        /// <summary>
        /// 启动审批流
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="description"></param>
        /// <param name="businessType"></param>
        /// <param name="variables"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        Task<StartResponseDto> WorkflowStart(long businessId, WorkflowBusinessTypeEnum businessType, object variables, int status = -1, string description = null);

        /// <summary>
        /// 更新审批流
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="status"></param>
        /// <param name="description"></param>
        /// <param name="variables"></param>
        /// <param name="businessType"></param>
        /// <returns></returns>
        /// <exception cref="AgentCentralException"></exception>
        Task<bool> WorkflowComplete(long businessId, int status, string description, object variables, WorkflowBusinessTypeEnum businessType);

        /// <summary>
        /// 查找记录
        /// </summary>
        /// <param name="businessId"></param>
        /// <returns></returns>
        Task<List<WorkflowLogDto>> GetWorkflowLogAsync(long businessId);
    }
}
