using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ReviewRecords;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Infrastructure;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.IServices.Workflow
{
    /// <summary>
    /// 工作流接口
    /// </summary>
    public interface IAppWorkflowService : IScopedService
    {
        /// <summary>
        /// 提交审批申请
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> SubmitForApprovalAsync(ApprovalActionDto model);

        /// <summary>
        /// 审批通过
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> ApproveAsync(ApprovalActionDto model);

        /// <summary>
        /// 驳回
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> RejectAsync(ApprovalActionDto model);

        /// <summary>
        /// 下架
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> DelistAsync(ApprovalActionDto model);

        /// <summary>
        /// 查找记录
        /// </summary>
        /// <param name="businessId"></param>
        /// <returns></returns>
        Task<List<WorkflowLogDto>> GetWorkflowLogAsync(string agentId);

        Task<bool> DifyAutoApprovalAsync(long businessId);
    }
}
