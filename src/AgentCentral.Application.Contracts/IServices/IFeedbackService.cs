using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.Feedback;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// 反馈服务接口
    /// </summary>
    public interface IFeedbackService : IScopedService
    {
        /// <summary>
        /// 更新反馈状态
        /// </summary>
        Task<bool> UpdateStatusAsync(long id, FeedbackStatusEnum status);

        /// <summary>
        /// 删除反馈
        /// </summary>
        Task<bool> DeleteAsync(long id);

        /// <summary>
        /// 获取反馈详情
        /// </summary>
        Task<FeedbackOutputDto> GetAsync(long id);

        /// <summary>
        /// 分页查询反馈
        /// </summary>
        Task<PageModelDto<FeedbackOutputDto>> GetPageListAsync(FeedbackSearchDto input);
    }
}