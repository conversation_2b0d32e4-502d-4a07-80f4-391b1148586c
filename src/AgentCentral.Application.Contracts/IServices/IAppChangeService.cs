using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Infrastructure;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// Application change record service interface
    /// </summary>
    public interface IAppChangeService : IScopedService
    {
        /// <summary>
        /// Get application change record
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>Change record</returns>
        Task<AppChangeDto> GetAsync(long appId);

        /// <summary>
        /// Update application change record
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Update result</returns>
        Task<bool> UpdateAsync(long appId, UpdateAppChangeDto updateDto);

        /// <summary>
        /// Update application change type
        /// </summary>
        /// <param name="appId">Dify application ID</param>
        /// <param name="updateDto">Change type data</param>
        /// <returns>Update result</returns>
        Task<bool> UpdateChangeTypeAsync(string appId, UpdateAppChangeTypeDto updateDto);
    }
}
