using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.Attachment;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ResourceManagement;
using AgentCentral.Application.Contracts.RequestModels.ResourceManagement;
using AgentCentral.Application.Contracts.ResponseModels.ResourceManagement;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.Services
{
    public interface IResourceManagementService : IScopedService
    {
        /// <summary>
        /// 创建资源信息
        /// </summary>
        Task<bool> CreateResourceAsync(CreateResourceRequest request);

        /// <summary>
        /// 更新资源信息
        /// </summary>
        Task<bool> UpdateResourceAsync(UpdateResourceRequest request);

        /// <summary>
        /// 获取资源信息
        /// </summary>
        Task<ResourceResponse> GetResourceAsync(string employeeId);

        /// <summary>
        /// 获取资源流程列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PageModelDto<ResourceProcessListDto>> GetResourceProcessListAsync(ResourceProcessSearchDto searchDto);

        /// <summary>
        /// 删除资源流程
        /// </summary>
        /// <param name="id">流程ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteResourceProcessAsync(long id);

        /// <summary>
        /// 批量删除资源流程
        /// </summary>
        /// <param name="ids">流程ID列表</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchDeleteResourceProcessAsync(List<long> ids);

        /// <summary>
        /// 获取资源流程的文件信息
        /// </summary>
        /// <param name="processId">流程ID</param>
        /// <returns>文件列表</returns>
        Task<List<AttachmentDto>> GetResourceProcessFilesAsync(long processId);

        /// <summary>
        /// 删除资源流程文件
        /// </summary>
        /// <param name="processId">流程ID</param>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteResourceProcessFileAsync(long processId, long attachmentId);

        Task SyncResourcesAsync(CancellationToken stoppingToken);
    }
}