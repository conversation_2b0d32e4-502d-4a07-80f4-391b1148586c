using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.RequestModels.UserTag;
using AgentCentral.Application.Contracts.ResponseModels.UserTag;

namespace AgentCentral.Application.Contracts.IServices
{
    /// <summary>
    /// 用户标签服务接口
    /// </summary>
    public interface IUserTagService
    {
        /// <summary>
        /// 创建用户标签
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>是否成功</returns>
        Task<bool> CreateUserTagAsync(CreateUserTagRequest request);

        /// <summary>
        /// 更新用户标签
        /// </summary>
        /// <param name="id">标签ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserTagAsync(long id, UpdateUserTagRequest request);

        /// <summary>
        /// 删除用户标签
        /// </summary>
        /// <param name="id">标签ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteUserTagAsync(long id);

        /// <summary>
        /// 获取用户标签
        /// </summary>
        /// <param name="id">标签ID</param>
        /// <returns>标签信息</returns>
        Task<UserTagDto> GetUserTagAsync(long id);

        /// <summary>
        /// 分页查询用户标签
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>分页结果</returns>
        Task<PageModelDto<UserTagDto>> SearchUserTagsAsync(SearchUserTagRequest request);

        /// <summary>
        /// 保存用户标签关联
        /// </summary>
        /// <param name="request">关联请求</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveUserTagRelationAsync(UserTagRelationRequest request);

        /// <summary>
        /// 获取用户标签列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>标签列表</returns>
        Task<List<UserTagDto>> GetUserTagsAsync(long userId);

        /// <summary>
        /// 检查标签编码是否存在
        /// </summary>
        /// <param name="tagCode">标签编码</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsTagCodeExistsAsync(string tagCode, long? excludeId = null);

        /// <summary>
        /// 获取所有标签列表（用于下拉选择）
        /// </summary>
        /// <param name="tagType">标签类型（可选）</param>
        /// <returns>标签列表</returns>
        Task<List<UserTagDto>> GetAllTagsForSelectAsync(string tenantId, string tagType = null);
    }
}
