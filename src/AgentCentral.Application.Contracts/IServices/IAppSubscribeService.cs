using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Application.Contracts.ResponseModels.AppSubscribe;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface IAppSubscribeService : IScopedService
    {
        /// <summary>
        /// 获取应用订阅用户列表
        /// </summary>
        Task<PageModelDto<AppSubscribeResponse>> GetSubscribeUsersAsync(string appId, int pageIndex, int pageSize);

        /// <summary>
        /// 获取应用订阅总数
        /// </summary>
        Task<int> GetSubscribeCountAsync(string appId);

        /// <summary>
        /// 订阅应用
        /// </summary>
        Task<bool> SubscribeAsync(string appId);

        /// <summary>
        /// 获取当前用户订阅的所有应用
        /// </summary>
        Task<PageModelDto<AppDetailPageResponse>> GetUserSubscribedAppsAsync(AppSearchPageDto searchDto);

        Task<bool> CheckSubscribedAsync(string appId);

        /// <summary>
        /// 取消订阅应用
        /// </summary>
        Task<bool> UnsubscribeAsync(string appId);

        /// <summary>
        /// Copy a single user's subscription to new version
        /// </summary>
        /// <param name="appId">New version app ID</param>
        /// <param name="userId">User ID</param>
        Task<bool> CopySubscriptionAsync(string appId, long userId);

        /// <summary>
        /// Copy all subscriptions to new version
        /// </summary>
        /// <param name="appId">New version app ID</param>
        Task<bool> CopyAllSubscriptionsAsync(string appId);

        /// <summary>
        /// 批量复制订阅
        /// </summary>
        /// <param name="dto">批量复制订阅DTO</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchCopySubscriptionAsync(BatchCopySubscriptionDto dto);
    }
}