using System.Collections.Generic;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.RequestModels.DocResource;
using AgentCentral.Application.Contracts.ResponseModels.DocResource;
using AgentCentral.Infrastructure;

namespace AgentCentral.Application.Contracts.IServices
{
    public interface IResourceService : IScopedService
    {
        /// <summary>
        /// 创建资源信息
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        Task<bool> CreateAsync(CreateDocResourceRequest request);

        /// <summary>
        /// 获取资源信息
        /// </summary>
        /// <param name="id">资源ID</param>
        /// <returns>资源信息</returns>
        Task<DocResourceResponse> GetAsync(long id);

        /// <summary>
        /// 更新资源信息
        /// </summary>
        /// <param name="resourceId">资源Id</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateAsync(long resourceId, UpdateDocResourceRequest request);

        /// <summary>
        /// 更新资源信息列表
        /// </summary>
        /// <returns></returns>
        Task<List<DocResourceResponse>> GetList();
    }
}