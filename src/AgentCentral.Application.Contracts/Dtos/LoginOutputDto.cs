using Item.Internal.Auth.Authentication.JwtBearer;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos
{
    public class LoginOutputDto
    {/// <summary>
     /// User OAuth Token
     /// </summary>
        public string OAuthToken { get; set; }

        /// <summary>
        /// Indicates if the login was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message in case of login failure
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// List of third-party applications
        /// </summary>
        public string[] ThirdPartyApplication { get; set; }

        /// <summary>
        /// IDM User ID
        /// </summary>
        public string IdmUserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Account status
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// User's contact phone number
        /// </summary>
        public string ContactPhone { get; set; }

        /// <summary>
        /// Validation version
        /// </summary>
        public string ValidationVersion { get; set; }

        /// <summary>
        /// Access token
        /// </summary>
        public JwtToken AccessToken { get; set; }

        /// <summary>
        /// Refresh token
        /// </summary>
        public JwtToken RefreshToken { get; set; }

        /// <summary>
        /// List of company IDs associated with the user
        /// </summary>
        public List<string> CompanyIds { set; get; }

        /// <summary>
        /// Tenant ID
        /// </summary>
        public long TenantId { get; set; }

        /// <summary>
        /// Dictionary of tenant IDs and names
        /// </summary>
        public Dictionary<long, string> Tenants { get; set; }
    }
}
