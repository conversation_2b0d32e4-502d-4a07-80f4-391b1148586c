using AgentCentral.Domain.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.Dtos.App
{
    /// <summary>
    /// 应用参数DTO
    /// </summary>
    public class AppParamDto
    {
        /// <summary>
        /// 参数ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// 参数键
        /// </summary>
        public string ParamKey { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        public string ParamName { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public AppParamTypeEnum ParamType { get; set; }

        /// <summary>
        /// 验证规则（JSON格式）
        /// </summary>
        public string ValidationRules { get; set; }

        /// <summary>
        /// 下拉选项（JSON格式，仅当ParamType=Dropdown时有效）
        /// </summary>
        public string Options { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }

        public List<SelectOption> SelectOptions { get; set; }
    }

    /// <summary>
    /// 创建应用参数请求
    /// </summary>
    public class CreateAppParamDto
    {
        /// <summary>
        /// 参数键
        /// </summary>
        public string ParamKey { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        public string ParamName { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public AppParamTypeEnum ParamType { get; set; }

        /// <summary>
        /// 验证规则（JSON格式）
        /// </summary>
        public string ValidationRules { get; set; }

        /// <summary>
        /// 下拉选项（JSON格式，仅当ParamType=Dropdown时有效）
        /// </summary>
        public string Options { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }
    }

    /// <summary>
    /// 更新应用参数请求
    /// </summary>
    public class UpdateAppParamDto
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string ParamName { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public AppParamTypeEnum ParamType { get; set; }

        /// <summary>
        /// 验证规则（JSON格式）
        /// </summary>
        public string ValidationRules { get; set; }

        /// <summary>
        /// 下拉选项（JSON格式，仅当ParamType=Dropdown时有效）
        /// </summary>
        public string Options { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }
    }

    /// <summary>
    /// 应用参数值DTO
    /// </summary>
    public class AppParamValueDto
    {
        /// <summary>
        /// 参数ID
        /// </summary>
        public long ParamId { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// 参数键
        /// </summary>
        public string ParamKey { get; set; }

        /// <summary>
        /// 参数值
        /// </summary>
        public string ParamValue { get; set; }
    }
}
