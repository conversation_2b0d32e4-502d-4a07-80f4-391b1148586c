using AgentCentral.Domain.Shared.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.Dtos.App
{
    public class CreateAppDto
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用编码
        /// </summary>
        public string AppCode { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        [Required(ErrorMessage = "AppName is required")]
        [StringLength(200, ErrorMessage = "AppName length should not exceed 200")]
        public string AppName { get; set; }

        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用模式
        /// </summary>
        [Required(ErrorMessage = "AppMode is required")]
        [StringLength(50, ErrorMessage = "AppMode length should not exceed 50")]
        public string AppMode { get; set; }

        /// <summary>
        /// 应用图标
        /// </summary>
        public string AppIcon { get; set; }

        /// <summary>
        /// 站点启用状态
        /// </summary>
        public bool EnableSite { get; set; } = true;

        /// <summary>
        /// API启用状态
        /// </summary>
        public bool EnableApi { get; set; } = true;

        public List<long> BannerAttachmentIds { get; set; } = [];

        public long VideoAttachmentId { get; set; }

        public AppPermissionEnum AppPermission { get; set; } = AppPermissionEnum.Public;

        public List<AppDepartmentDto> Departments { get; set; } = [];

        /// <summary>
        /// 用户标签IDs
        /// </summary>
        public List<long> UserTagIds { get; set; } = [];

        /// <summary>
        /// 角色IDs
        /// </summary>
        public List<long> RoleIds { get; set; } = [];
    }

    public class AppDepartmentDto
    {
        public string CompanyId { get; set; }

        public string DepartmentCode { get; set; }
    }
}
