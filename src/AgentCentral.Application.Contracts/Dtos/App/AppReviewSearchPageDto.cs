using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Domain.Shared.Enums;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.App
{
    public class AppReviewSearchPageDto : BaseSearchPageDto
    {
        /// <summary>
        /// 应用名称
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 标签ID列表
        /// </summary>
        public List<long> TagIds { get; set; } = new();

        /// <summary>
        /// 应用状态
        /// </summary>
        public AppStatusEnum? Status { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public int ReviewStatus { get; set; }

        /// <summary>
        /// 应用模式
        /// </summary>
        public string AppMode { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        public string Reviewer { get; set; }

        public AppPermissionEnum? PermissionType { get; set; }

        public bool? GuestMode { get; set; }
    }
}