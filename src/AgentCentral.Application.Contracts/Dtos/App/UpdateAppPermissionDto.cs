using AgentCentral.Domain.Shared.Enums;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.App
{
    /// <summary>
    /// 更新应用权限DTO
    /// </summary>
    public class UpdateAppPermissionDto
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 权限类型
        /// </summary>
        public AppPermissionEnum AppPermission { get; set; }

        /// <summary>
        /// 部门权限列表
        /// </summary>
        public List<AppDepartmentDto> Departments { get; set; } = new List<AppDepartmentDto>();

        /// <summary>
        /// 用户标签IDs
        /// </summary>
        public List<long> UserTagIds { get; set; } = [];

        /// <summary>
        /// 角色IDs
        /// </summary>
        public List<long> RoleIds { get; set; } = [];
    }
}