namespace AgentCentral.Application.Contracts.Dtos.App
{
    /// <summary>
    /// 验证规则模型
    /// </summary>
    public class ValidationRuleModel
    {
        /// <summary>
        /// 最小长度
        /// </summary>
        public int? MinLength { get; set; }

        /// <summary>
        /// 最大长度
        /// </summary>
        public int? MaxLength { get; set; }

        /// <summary>
        /// 正则表达式
        /// </summary>
        public string Pattern { get; set; }

        /// <summary>
        /// 正则验证失败消息
        /// </summary>
        public string PatternMessage { get; set; }

        /// <summary>
        /// 是否必填
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 必填验证失败消息
        /// </summary>
        public string RequiredMessage { get; set; }

        /// <summary>
        /// 最小值（数字类型）
        /// </summary>
        public decimal? Min { get; set; }

        /// <summary>
        /// 最大值（数字类型）
        /// </summary>
        public decimal? Max { get; set; }

        /// <summary>
        /// 小数位数（数字类型）
        /// </summary>
        public int? Precision { get; set; }

        /// <summary>
        /// 是否支持多选（下拉框）
        /// </summary>
        public bool? Multiple { get; set; }

        /// <summary>
        /// 最大选择数量（下拉框多选时有效）
        /// </summary>
        public int? MaxSelect { get; set; }
    }

    /// <summary>
    /// 下拉选项模型
    /// </summary>
    public class SelectOption
    {
        /// <summary>
        /// 选项标签
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; }
    }
}
