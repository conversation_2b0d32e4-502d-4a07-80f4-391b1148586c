using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos.App
{
    /// <summary>
    /// Application change record DTO
    /// </summary>
    public class AppChangeDto
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        [JsonConverter(typeof(ValueToStringConverter))]
        public long Id { get; set; }

        /// <summary>
        /// Application ID
        /// </summary>
        [J<PERSON><PERSON>onverter(typeof(ValueToStringConverter))]
        public long AppId { get; set; }

        /// <summary>
        /// Change type
        /// </summary>
        public int ChangeType { get; set; }

        /// <summary>
        /// Whether to upgrade version
        /// </summary>
        public bool NeedUpgradeVersion { get; set; }

        /// <summary>
        /// Whether to force update
        /// </summary>
        public bool ForceUpdate { get; set; }

        /// <summary>
        /// Change description
        /// </summary>
        public string ChangeDescription { get; set; }

        public int? NewVersion { get; set; }
    }

    /// <summary>
    /// Update application change record DTO
    /// </summary>
    public class UpdateAppChangeDto
    {
        /// <summary>
        /// Whether to upgrade version
        /// </summary>
        public bool NeedUpgradeVersion { get; set; }

        /// <summary>
        /// Whether to force update
        /// </summary>
        public bool ForceUpdate { get; set; }

        /// <summary>
        /// Change description
        /// </summary>
        public string ChangeDescription { get; set; }
    }
}
