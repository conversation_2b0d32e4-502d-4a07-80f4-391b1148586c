using AgentCentral.Domain.Shared.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.Dtos.App
{
    public class UpdateAppDto
    {
        /// <summary>
        /// 应用名称
        /// </summary>
        [Required(ErrorMessage = "AppName is required")]
        [StringLength(200, ErrorMessage = "AppName length should not exceed 200")]
        public string AppName { get; set; }

        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用图标
        /// </summary>
        public string AppIcon { get; set; }

        public List<long> BannerAttachmentIds { get; set; } = [];

        public long VideoAttachmentId { get; set; }

        public AppPermissionEnum AppPermission { get; set; } = AppPermissionEnum.Public;

        public List<AppDepartmentDto> Departments { get; set; } = [];

        public IList<long> TagIds { get; set; } = [];

        public string ChangeDescription { get; set; }

        /// <summary>
        /// 用户标签IDs
        /// </summary>
        public List<long> UserTagIds { get; set; } = [];

        /// <summary>
        /// 角色IDs
        /// </summary>
        public List<long> RoleIds { get; set; } = [];
    }
}
