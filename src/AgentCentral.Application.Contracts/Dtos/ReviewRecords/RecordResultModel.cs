using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.JsonConverts;
using Item.Internal.ChangeLog.Models;
using Newtonsoft.Json;
using System;

namespace AgentCentral.Application.Contracts.Dtos.ReviewRecords;

public class RecordResultModel
{
    public long AgentId { get; set; }

    public OpTypeEnum OpType { get; set; }

    public string AgentName { get; set; }

    public string Creator { get; set; }

    public long CreatorId { get; set; }

    public string Reviewer { get; set; }

    [JsonConverter(typeof(UsDateFormatConverter))]
    public DateTime OperationTime { get; set; }

    public AppStatusEnum Status { get; set; }

    public string Remarks { get; set; }

    public string VersionId { get; set; }

    public string ChangeDescription { get; set; }

    public string GeneratorId { get; set; }

    /// <summary>
    /// 用户邮箱
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string DepartmentName { get; set; }

}

