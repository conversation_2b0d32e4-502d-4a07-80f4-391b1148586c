using System;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos.MpMcp
{
    /// <summary>
    /// Mp_Mcp列表响应DTO
    /// </summary>
    public class MpMcpListDto
    {
        /// <summary>
        /// 主键ID (MCP ID)
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// DI平台的MCP ID
        /// </summary>
        public long McpId { get; set; }

        /// <summary>
        /// 服务名称 (SERVICE NAME)
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// 服务描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 服务分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceType { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// MCP服务状态
        /// </summary>
        public McpStatusEnum Status { get; set; }

        /// <summary>
        /// 自动审核结果
        /// </summary>
        public MpMcpWorkflowCommentsDto ReviewResult { get; set; }

        /// <summary>
        /// 创建日期 (CREATION DATE)
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 审批备注
        /// </summary>
        public string ReviewRemark { get; set; }
    }
} 