using System;
using System.Collections.Generic;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpMcp
{
    /// <summary>
    /// Mp_Mcp详情响应DTO
    /// </summary>
    public class MpMcpDetailDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// DI平台的MCP ID
        /// </summary>
        public long McpId { get; set; }

        /// <summary>
        /// 服务名称
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// 服务描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 服务Logo URL
        /// </summary>
        public string LogoUrl { get; set; }

        /// <summary>
        /// 服务分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 使用场景
        /// </summary>
        public string UseCases { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceType { get; set; }

        /// <summary>
        /// 服务价值
        /// </summary>
        public string ServiceValue { get; set; }

        /// <summary>
        /// 文档URL
        /// </summary>
        public string DocumentUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// MCP服务状态
        /// </summary>
        public McpStatusEnum Status { get; set; }

        /// <summary>
        /// 自动审核结果
        /// </summary>
        public MpMcpWorkflowCommentsDto ReviewResult { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateName { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdateName { get; set; }


        /// <summary>
        /// 服务Logo 文件ID
        /// </summary>
        public long LogoAttachmentId { get; set; }

        /// <summary>
        /// 文档文件ID
        /// </summary>
        public long DocumentAttachmentId { get; set; }

        /// <summary>
        /// 文档文件名
        /// </summary>
        public string DocumentFileName { get; set; }

        /// <summary>
        /// 文档文件大小(字节)
        /// </summary>
        public long DocumentFileSize { get; set; }

        /// <summary>
        /// 文档文件上传时间
        /// </summary>
        public DateTime? DocumentUploadTime { get; set; }

        /// <summary>
        /// 视频文件ID
        /// </summary>
        public long VideoAttachmentId { get; set; }

        /// <summary>
        /// 文档文件名
        /// </summary>
        public string VideoFileName { get; set; }

        /// <summary>
        /// 文档文件大小(字节)
        /// </summary>
        public long VideoFileSize { get; set; }

        /// <summary>
        /// 文档文件上传事件
        /// </summary>
        public DateTime? VideoUploadTime { get; set; }

        /// <summary>
        /// 工作流记录
        /// </summary>
        public List<WorkflowLogDto> WorkflowLogs { get; set; }
    }
} 