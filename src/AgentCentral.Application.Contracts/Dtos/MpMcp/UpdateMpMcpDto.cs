namespace AgentCentral.Application.Contracts.Dtos.MpMcp
{
    /// <summary>
    /// 通过表单数据更新MCP服务请求
    /// </summary>
    public class UpdateMpMcpDto
    {
        /// <summary>
        /// MCP服务ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// 服务描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 服务Logo附件ID
        /// </summary>
        public long? LogoAttachmentId { get; set; }

        /// <summary>
        /// 服务分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 使用场景
        /// </summary>
        public string UseCases { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceType { get; set; }

        /// <summary>
        /// 服务价值
        /// </summary>
        public string ServiceValue { get; set; }

        /// <summary>
        /// 文档附件ID
        /// </summary>
        public long? DocumentAttachmentId { get; set; }

        /// <summary>
        /// 视频附件ID
        /// </summary>
        public long? VideoAttachmentId { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Source { get; set; }

    }
} 