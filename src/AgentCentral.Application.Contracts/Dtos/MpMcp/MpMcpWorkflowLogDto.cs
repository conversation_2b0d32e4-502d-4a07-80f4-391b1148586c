using System;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpMcp
{
    /// <summary>
    /// Mp_Mcp工作流日志数据传输对象
    /// </summary>
    public class MpMcpWorkflowLogDto
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// MCP服务ID
        /// </summary>
        public long McpId { get; set; }

        /// <summary>
        /// MCP服务状态
        /// </summary>
        public McpStatusEnum Status { get; set; }

        /// <summary>
        /// 审批备注
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// 审批人用户名
        /// </summary>
        public string ApprovalUserName { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime ApprovalDate { get; set; }

        /// <summary>
        /// 操作类型（审批、拒绝、下架等）
        /// </summary>
        public string OperationType { get; set; }

    }
} 