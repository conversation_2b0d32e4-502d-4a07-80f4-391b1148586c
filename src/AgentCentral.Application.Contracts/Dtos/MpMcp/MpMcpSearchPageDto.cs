using System;
using System.Collections.Generic;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpMcp
{
    /// <summary>
    /// Mp_App审核搜索请求DTO
    /// </summary>
    public class MpMcpSearchPageDto : BaseSearchPageDto
    {
        /// <summary>
        /// MCP服务Id
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// DI平台的MCP ID
        /// </summary>
        public long? McpId { get; set; }

        /// <summary>
        /// 服务名称
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// 服务提供商
        /// </summary>
        public List<string> Provider { get; set; }

        /// <summary>
        /// 服务分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceType { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// MCP服务状态（支持多状态同时查询）
        /// </summary>
        public McpStatusEnum[] Status { get; set; } = [];

        /// <summary>
        /// 创建日期起始时间
        /// </summary>
        public DateTime? CreateDateStart { get; set; }

        /// <summary>
        /// 创建日期结束时间
        /// </summary>
        public DateTime? CreateDateEnd { get; set; }


        /// <summary>
        /// 审核状态 0-Pending Review 1-Reviewed
        /// </summary>
        public int ReviewStatus { get; set; } = 0;

    }
} 