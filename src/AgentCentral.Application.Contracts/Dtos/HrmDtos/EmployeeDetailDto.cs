namespace AgentCentral.Application.Contracts.Dtos.HrmDtos
{
    public class EmployeeDetailDto
    {
        /// <summary>
        /// 员工编号
        /// </summary>
        public string EmployeeCode { get; set; }

        /// <summary>
        /// 名
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 中间名
        /// </summary>
        public string MiddleName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 雇佣类型名称
        /// </summary>
        public string EmploymentTypeName { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        public int PayType { get; set; }

        /// <summary>
        /// 支付频率
        /// </summary>
        public int PayFrequency { get; set; }

        /// <summary>
        /// 劳动分配
        /// </summary>
        public string LaborAllocation { get; set; }

        /// <summary>
        /// 业务单元编码
        /// </summary>
        public string BusinessUnitCode { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string BusinessUnitName { get; set; }

        /// <summary>
        /// 职位编码
        /// </summary>
        public string PositionCode { get; set; }

        /// <summary>
        /// 职位名称
        /// </summary>
        public string PositionName { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public string DepartmentCode { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 设施编码
        /// </summary>
        public string FacilityCode { get; set; }

        /// <summary>
        /// 设施名称
        /// </summary>
        public string FacilityName { get; set; }

        /// <summary>
        /// 主管编码
        /// </summary>
        public string SupervisorCode { get; set; }

        /// <summary>
        /// 主管名称
        /// </summary>
        public string SupervisorName { get; set; }

        /// <summary>
        /// 二级主管编码
        /// </summary>
        public string SecondSupervisorCode { get; set; }

        /// <summary>
        /// 打卡类型
        /// </summary>
        public int PunchType { get; set; }

        /// <summary>
        /// 员工状态名称
        /// </summary>
        public string EmployeeStatusName { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// 是否为主管审核人
        /// </summary>
        public bool IsSupervisorReviewer { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 电话号码
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 工作邮箱
        /// </summary>
        public string WorkEmail { get; set; }

        public string TenantCode { get; set; }
    }
}
