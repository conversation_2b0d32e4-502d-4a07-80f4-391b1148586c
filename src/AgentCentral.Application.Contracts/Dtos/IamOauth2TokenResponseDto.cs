using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos
{
    /// <summary>
    /// Data transfer object for IAM OAuth2 token response
    /// </summary>
    public class IamOauth2TokenResponseDto
    {
        /// <summary>
        /// The access token issued by the authorization server
        /// </summary>
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        /// <summary>
        /// The type of the token issued, typically "Bearer"
        /// </summary>
        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        /// <summary>
        /// The lifetime in seconds of the access token
        /// </summary>
        [JsonProperty("expires_in")]
        public long ExpiresIn { get; set; }

        /// <summary>
        /// The scope of the access token
        /// </summary>
        public string Scope { get; set; }
    }
}