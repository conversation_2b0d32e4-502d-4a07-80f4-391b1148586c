using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.BaseDto
{
    public class PageModelDto<T> where T : class
    {
        public PageModelDto(int pageIndex, int pageSize, List<T> data, int count)
        {
            PageIndex = pageIndex;
            PageSize = pageSize;
            Total = count;
            Data = data;
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPage
        {
            get
            {
                if (Total > 0)
                {
                    return Total % PageSize == 0 ? Total / PageSize : Total / PageSize + 1;
                }
                else
                {
                    return 0;
                }
            }
        }

        public int PageIndex { get; set; } = 1;

        public int PageSize { get; set; } = 10;

        public int Total { get; set; }

        public List<T> Data { get; set; }
    }
}