using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.Dtos.BaseDto
{
    /// <summary>
    /// 公司部门角色树形结构DTO
    /// </summary>
    public class CompanyDepartmentRoleTreeDto
    {
        /// <summary>
        /// 公司名称
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// 公司标签（显示名称）
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 子节点（部门列表）
        /// </summary>
        public List<DepartmentNodeDto> Children { get; set; } = new List<DepartmentNodeDto>();
    }

    /// <summary>
    /// 部门节点DTO
    /// </summary>
    public class DepartmentNodeDto
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public string DepartmentCode { get; set; }

        /// <summary>
        /// 子节点（角色列表）
        /// </summary>
        public List<RoleNodeDto> Children { get; set; } = new List<RoleNodeDto>();
    }
}
