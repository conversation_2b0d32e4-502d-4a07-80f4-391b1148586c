using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Infrastructure.Models;

namespace AgentCentral.Application.Contracts.Dtos.BaseDto
{
    /// <summary>
    /// 角色搜索DTO
    /// </summary>
    public class RoleSearchDto : QueryPageModel
    {
        /// <summary>
        /// 公司/租户
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 关键字（角色名称或编码）
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 是否系统角色
        /// </summary>
        public bool? IsSystem { get; set; }
    }
}
