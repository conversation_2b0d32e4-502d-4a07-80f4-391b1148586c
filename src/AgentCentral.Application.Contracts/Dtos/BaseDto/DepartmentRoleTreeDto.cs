using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.Dtos.BaseDto
{
    /// <summary>
    /// 部门角色树形结构DTO
    /// </summary>
    public class DepartmentRoleTreeDto
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public string DepartmentCode { get; set; }

        /// <summary>
        /// 子节点（角色列表）
        /// </summary>
        public List<RoleNodeDto> Children { get; set; } = new List<RoleNodeDto>();
    }

    /// <summary>
    /// 角色节点DTO
    /// </summary>
    public class RoleNodeDto
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 角色编码
        /// </summary>
        public string RoleCode { get; set; }

        /// <summary>
        /// 所属部门ID
        /// </summary>
        public long DepartmentId { get; set; }
    }
}
