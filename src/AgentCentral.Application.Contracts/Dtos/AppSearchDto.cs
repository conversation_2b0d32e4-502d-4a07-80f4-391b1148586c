using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Domain.Shared.Enums;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos
{
    public class AppSearchDto : BaseSearchPageDto
    {
        public string AppMode { get; set; }

        public AppStatusEnum? AppStatus { get; set; }

        public AppPermissionEnum? PermissionType { get; set; }

        public IList<long> TagIds { get; set; } = [];
    }
}
