namespace AgentCentral.Application.Contracts.Dtos.ChangeLog;

/// <summary>
/// Data Transfer Object for requesting change log information
/// </summary>
public class ChangeLogRequestDto
{
    /// <summary>
    /// The index of the page to retrieve
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// The number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// The unique identifier of the customer
    /// </summary>
    public long CustomerId { get; set; }

    /// <summary>
    /// The category of the change log
    /// </summary>
    public string Category { get; set; }
}
