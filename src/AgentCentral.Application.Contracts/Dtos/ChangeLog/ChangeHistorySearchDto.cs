using Item.Common.Lib.CacheUtil;
using System;

namespace AgentCentral.Application.Contracts.Dtos.ChangeLog;

/// <summary>
/// Data Transfer Object for searching change history records
/// </summary>
public class ChangeHistorySearchDto : ICacheKey
{
    /// <summary>
    /// Category of the change history
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// Unique identifier of the customer
    /// </summary>
    public long CustomerId { get; set; }

    /// <summary>
    /// Start date for the search range
    /// </summary>
    public DateTimeOffset? StartDate { get; set; }

    /// <summary>
    /// End date for the search range
    /// </summary>
    public DateTimeOffset? EndDate { get; set; }

    /// <summary>
    /// Generates a cache key based on the Category and CustomerId
    /// </summary>
    /// <returns>A string representation of the cache key</returns>
    public string ToCacheKey()
    {
        return $"{Category}_{CustomerId}";
    }
}
