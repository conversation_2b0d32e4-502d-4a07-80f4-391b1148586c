using AgentCentral.Infrastructure.Models;
using Item.Internal.ChangeLog;
using Item.Internal.ChangeLog.Models;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.Dtos.ChangeLog;

public class ChangeLogDto
{
    public long Id { get; set; }

    public string TenantId { get; set; }

    public string TableName { get; set; }

    public string ColumnName { get; set; }

    public string OldValue { get; set; }

    public string NewValue { get; set; }

    public string PrimaryValue { get; set; }

    public long UserId { get; set; }

    public string UserName { get; set; }

    public string OpId { get; set; }

    public DateTime OpTime { get; set; }

    public OpTypeEnum OpType { get; set; }

    public long AppId { get; set; } = 0;

    public string ExtendData { get; set; }
}
