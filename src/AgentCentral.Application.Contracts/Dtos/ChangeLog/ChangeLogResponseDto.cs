using Item.Internal.ChangeLog.Models;
using System;

namespace AgentCentral.Application.Contracts.Dtos.ChangeLog;

/// <summary>
/// Data Transfer Object for change log response
/// </summary>
public class ChangeLogResponseDto
{
    /// <summary>
    /// Paged result containing change log data
    /// </summary>
    public PageingResultModel<ChangeLogData> Datas { get; set; }
}

/// <summary>
/// Represents individual change log entry data
/// </summary>
public class ChangeLogData
{
    /// <summary>
    /// Array of messages describing the changes
    /// </summary>
    public string[] Message { get; set; }

    /// <summary>
    /// Name of the user who made the changes
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// Unique identifier of the user who made the changes
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// Type of operation performed
    /// </summary>
    public OpTypeEnum OpType { get; set; }

    /// <summary>
    /// Time when the operation was performed
    /// </summary>
    public string OpTime { get; set; }

    /// <summary>
    /// Unique identifier of the operation
    /// </summary>
    public string OpId { get; set; }
}