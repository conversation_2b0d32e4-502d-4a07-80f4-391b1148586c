using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.Dtos.ResourceManagement
{
    public class ResourcePeopleDto : BaseDto.BaseDto
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        [MaxLength(50)]
        public string EmployeeId { get; set; }

        /// <summary>
        /// 员工名字
        /// </summary>
        [MaxLength(200)]
        public string Name { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        [MaxLength(200)]
        public string Company { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [MaxLength(200)]
        public string Department { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [MaxLength(200)]
        public string Position { get; set; }

        /// <summary>
        /// 设施
        /// </summary>
        [MaxLength(200)]
        public string Facility { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// URL链接
        /// </summary>
        public string UrlLinks { get; set; }
    }
}