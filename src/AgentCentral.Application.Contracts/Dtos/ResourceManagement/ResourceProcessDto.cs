using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AgentCentral.Application.Contracts.Dtos.Attachment;

namespace AgentCentral.Application.Contracts.Dtos.ResourceManagement
{
    public class ResourceProcessDto : BaseDto.BaseDto
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        [MaxLength(50)]
        public string EmployeeId { get; set; }

        /// <summary>
        /// 分组
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Group { get; set; }

        /// <summary>
        /// 流程环境
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string ProcessEnvironment { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Process { get; set; }

        /// <summary>
        /// 流程名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string ProcessName { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        [MaxLength(200)]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Customer { get; set; }

        /// <summary>
        /// 客户租户ID
        /// </summary>
        [MaxLength(32)]
        public string CustomerTenantId { get; set; }

        /// <summary>
        /// 零售商客户编码
        /// </summary>
        [MaxLength(200)]
        public string RetailerCode { get; set; }

        /// <summary>
        /// 零售商
        /// </summary>
        [MaxLength(200)]
        public string Retailer { get; set; }

        /// <summary>
        /// 零售商租户ID
        /// </summary>
        public string RetailerTenantId { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// URL链接
        /// </summary>
        public string UrlLinks { get; set; }

        public string UserName { get; set; }

        public string ContributorName { get; set; }

        /// <summary>
        /// 附件ID列表
        /// </summary>
        public List<long> AttachmentIds { get; set; }


        public List<AttachmentDto> Attachments { get; set; }
    }
}