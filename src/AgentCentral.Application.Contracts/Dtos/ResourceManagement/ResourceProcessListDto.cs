using AgentCentral.Application.Contracts.Dtos.Attachment;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.ResourceManagement
{
    /// <summary>
    /// 资源流程列表DTO
    /// </summary>
    public class ResourceProcessListDto : BaseDto.BaseDto
    {
        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 贡献者名称
        /// </summary>
        public string ContributorName { get; set; }

        /// <summary>
        /// 公司/部门
        /// </summary>
        public string ProcessEnvironment { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string Process { get; set; }

        /// <summary>
        /// 流程名称
        /// </summary>
        public string ProcessName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string Customer { get; set; }

        /// <summary>
        /// 零售商
        /// </summary>
        public string Retailer { get; set; }

        /// <summary>
        /// URL链接
        /// </summary>
        public string UrlLinks { get; set; }

        /// <summary>
        /// 文件数量
        /// </summary>
        public int FilesCount => Attachments.Count;

        /// <summary>
        /// 文件列表
        /// </summary>
        public List<AttachmentDto> Attachments { get; set; } = new List<AttachmentDto>();
    }
}