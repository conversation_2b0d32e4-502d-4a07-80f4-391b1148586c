using Newtonsoft.Json;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.MessageCenter
{
    public class SendMessageRequest
    {
        [JsonProperty("job_code")]
        public string JobCode { get; set; }

        [JsonProperty("title_content")]
        public string TitleContent { get; set; } = "";

        [JsonProperty("body_content")]
        public string BodyContent { get; set; } = "";

        [JsonProperty("recipients")]
        public List<Recipients> Recipients { get; set; } = new List<Recipients>();

        [JsonProperty("title_spel")]
        public List<SpelContent> TitleSpel { get; set; } = new List<SpelContent>();

        [JsonProperty("body_spel")]
        public List<SpelContent> BodySpel { get; set; } = new List<SpelContent>();

        [JsonProperty("attachments")]
        public List<Attachment> Attachments { get; set; } = new List<Attachment>();
    }

    public class Recipients
    {
        [JsonProperty("recipient")]
        public string Recipient { get; set; }

        [JsonProperty("recipient_type")]
        public string RecipientType { get; set; }
    }

    public class SpelContent
    {
        [JsonProperty("key")]
        public string Key { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }
    }

    public class Attachment
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }
    }
} 