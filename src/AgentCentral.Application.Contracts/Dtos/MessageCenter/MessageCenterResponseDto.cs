using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.MessageCenter
{
    /// <summary>
    /// Message Center API Response
    /// </summary>
    public class MessageCenterResponseDto
    {
        /// <summary>
        /// Whether the request was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Response code
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// Response timestamp
        /// </summary>
        public long Timestamp { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        public List<long> Data { get; set; }
    }
} 