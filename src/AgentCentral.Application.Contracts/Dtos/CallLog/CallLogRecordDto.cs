using System;

namespace AgentCentral.Application.Contracts.Dtos.CallLog
{
    /// <summary>
    /// Call log record data transfer object
    /// </summary>
    public class CallLogRecordDto
    {
        /// <summary>
        /// Record ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// Call time
        /// </summary>
        public string CallTime { get; set; }

        /// <summary>
        /// Phone number
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// Company name
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// Email address
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Intent type code
        /// </summary>
        public int IntentType { get; set; }

        /// <summary>
        /// Intent label
        /// </summary>
        public string IntentLabel { get; set; }

        /// <summary>
        /// Status code
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// Status label
        /// </summary>
        public string StatusLabel { get; set; }

        /// <summary>
        /// Creation time
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Update time
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }
}