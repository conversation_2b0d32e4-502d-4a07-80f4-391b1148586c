using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos.CallLog
{
    /// <summary>
    /// Call log search parameters data transfer object
    /// </summary>
    [JsonObject(NamingStrategyType = typeof(Newtonsoft.Json.Serialization.CamelCaseNamingStrategy))]
    public class CallLogSearchParamsDto
    {
        /// <summary>
        /// Phone number
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// Company name
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// Email address
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Intent type code
        /// </summary>
        public int? IntentType { get; set; }

        /// <summary>
        /// Start date and time
        /// </summary>
        public string StartDate { get; set; }

        /// <summary>
        /// End date and time
        /// </summary>
        public string EndDate { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Page number
        /// </summary>
        public int PageNo { get; set; }
    }
}