using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.CallLog
{
    /// <summary>
    /// Call support statistics data transfer object
    /// </summary>
    public class CallSupportStatisticsDto
    {
        /// <summary>
        /// Total number of calls
        /// </summary>
        public int TotalCalls { get; set; }

        /// <summary>
        /// Total calls change percent compared to last month, unit: %
        /// </summary>
        public double TotalCallsChangePercent { get; set; }

        /// <summary>
        /// Number of calls today
        /// </summary>
        public int CallsToday { get; set; }

        /// <summary>
        /// Today's calls change percent compared to yesterday, unit: %
        /// </summary>
        public double CallsTodayChangePercent { get; set; }

        /// <summary>
        /// Number of emails sent
        /// </summary>
        public int EmailsSent { get; set; }

        /// <summary>
        /// Email change percent compared to last month, unit: %
        /// </summary>
        public double EmailsSentChangePercent { get; set; }

        /// <summary>
        /// Intent distribution dictionary
        /// </summary>
        public Dictionary<string, double> IntentDistribution { get; set; }
    }
} 