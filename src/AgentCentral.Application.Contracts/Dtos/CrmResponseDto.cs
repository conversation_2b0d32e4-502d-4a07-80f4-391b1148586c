using System.Collections.Generic;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos;

public class CrmResponseDto<T>
{
    public PageData<T> Data { get; set; }
}

public class PageData<T>
{
    public T Data { get; set; }
}

public class CrmCustomerOrRetailerDto
{
    [JsonProperty("accountName")] public string Name { get; set; }

    [JsonProperty("code")] public string Code { get; set; }
    public string TenantId { get; set; }
}