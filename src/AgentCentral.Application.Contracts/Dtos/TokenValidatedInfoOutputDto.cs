using System;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos
{
    public class TokenValidatedInfoOutputDto
    {
        public long UserId { get; set; }
        public string UserName { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }

        public string ClientId { get; set; }

        public string ValidationVersion { get; set; }

        public string Token { get; set; }

        public UserTypeEnum UserType { get; set; }

        public string TenantId { get; set; }

        public DateTime CreateTime { get; set; }
    }
}