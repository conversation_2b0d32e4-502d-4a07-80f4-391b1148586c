using System.Collections.Generic;
using AgentCentral.Domain.Shared.Enums;
using Microsoft.AspNetCore.Http;

namespace AgentCentral.Application.Contracts.Dtos.MpApp
{
    /// <summary>
    /// 通过表单数据更新应用市场应用请求
    /// </summary>
    public class UpdateMpAppFormRequest
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }


        /// <summary>
        /// 变现方式(free/paid)
        /// </summary>
        public string Monetization { get; set; }

        /// <summary>
        /// 隐私政策URL地址
        /// </summary>
        public string PrivacyPolicyUrl { get; set; }

        /// <summary>
        /// 应用市场展示名称
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 简要描述
        /// </summary>
        public string ShortDescription { get; set; }

        /// <summary>
        /// 完整描述
        /// </summary>
        public string FullDescription { get; set; }

        /// <summary>
        /// 应用版本号
        /// </summary>
        public string VersionNumber { get; set; }

        /// <summary>
        /// 开发者名称
        /// </summary>
        public string DeveloperName { get; set; }

        /// <summary>
        /// 开发者联系方式
        /// </summary>
        public string DeveloperContact { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 商店链接
        /// </summary>
        public string StoreLink { get; set; }

        /// <summary>
        /// 是否已授权
        /// </summary>
        public bool IsAuthorized { get; set; }

        /// <summary>
        /// 是否草稿
        /// </summary>
        public bool IsDraft { get; set; }

        /// <summary>
        /// 应用图标附件ID
        /// </summary>
        public long? Icon { get; set; }
        
        /// <summary>
        /// 截图附件ID列表
        /// </summary>
        public List<long> Screenshots { get; set; }

        /// <summary>
        /// 需要保留的截图URL列表
        /// </summary>
        public List<string> ScreenshotUrls { get; set; }
        
        /// <summary>
        /// APK文件附件
        /// </summary>
        public string ApkFile { get; set; }

        /// <summary>
        /// 隐私政策文件附件ID
        /// </summary>
        public long? PrivacyPolicyFile { get; set; }

        /// <summary>
        /// 隐私政策文件类型
        /// </summary>
        public PrivacyPolicyDocumentTypeEnum PrivacyPolicyDocumentType { get; set; }

    }
} 