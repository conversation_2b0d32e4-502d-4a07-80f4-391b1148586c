using System;
using AgentCentral.Domain.Shared.Enums;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos.MpApp
{
    /// <summary>
    /// Mp_App工作流日志对象DTO
    /// </summary>
    public class MpAppWorkflowCommentsDto
    {

        /// <summary>
        /// 内容
        /// </summary>
        [JsonProperty("comments", NullValueHandling = NullValueHandling.Ignore)]
        public string Comments { get; set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        [JsonProperty("opName", NullValueHandling = NullValueHandling.Ignore)]
        public string OpName { get; set; }


        /// <summary>
        /// 内容质量评分
        /// </summary>
        [JsonProperty("contentQualityScore", NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ContentQualityScore { get; set; }

        /// <summary>
        /// 安全检查评分
        /// </summary>
        [JsonProperty("safetyCheckScore", NullValueHandling = NullValueHandling.Ignore)]
        public decimal? SafetyCheckScore { get; set; }

        /// <summary>
        /// 合规性评分
        /// </summary>
        [JsonProperty("complianceScore", NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ComplianceScore { get; set; }

        /// <summary>
        /// 总体评分
        /// </summary>
        [JsonProperty("overallScore", NullValueHandling = NullValueHandling.Ignore)]
        public decimal? OverallScore { get; set; }

        /// <summary>
        /// 上传包体分析/外链分析
        /// </summary>
        [JsonProperty("isFileAnalysis", NullValueHandling = NullValueHandling.Ignore)]
        public bool IsFileAnalysis { get; set; } = true;
        /// <summary>
        /// 隐私协议分析
        /// </summary>
        [JsonProperty("isPrivacyAnalysis", NullValueHandling = NullValueHandling.Ignore)]
        public bool IsPrivacyAnalysis { get; set; } = true;

    }
} 