using System;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpApp
{
    /// <summary>
    /// Mp_App审核列表响应DTO
    /// </summary>
    public class MpAppReviewListDto
    {
        /// <summary>
        /// 主键ID (APP ID)
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 应用编码(D/P+6位数字)
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用名称 (APP NAME)
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 开发者名称 (DEVELOPER)
        /// </summary>
        public string DeveloperName { get; set; }

        /// <summary>
        /// 应用状态
        /// </summary>
        public MpAppStatusEnum AppStatus { get; set; }

        /// <summary>
        /// 上传类型 (UPLOAD TYPE)
        /// </summary>
        public UploadTypeEnum UploadType { get; set; }

        /// <summary>
        /// 平台类型 (可能用于筛选)
        /// </summary>
        public PlatformTypeEnum? PlatformType { get; set; }

        /// <summary>
        /// 变现方式 (可能用于业务逻辑)
        /// </summary>
        public MonetizationTypeEnum MonetizationType { get; set; }

        /// <summary>
        /// 提交日期 (SUBMISSION DATE)
        /// </summary>
        public DateTime? SubmitDate { get; set; }

        /// <summary>
        /// 内容质量评分 (Content Quality)
        /// </summary>
        public decimal? ContentQualityScore { get; set; }

        /// <summary>
        /// 安全检查评分 (Safety Check)
        /// </summary>
        public decimal? SafetyCheckScore { get; set; }

        /// <summary>
        /// 合规性评分 (Compliance)
        /// </summary>
        public decimal? ComplianceScore { get; set; }

        /// <summary>
        /// 综合评分
        /// </summary>
        public decimal? OverallScore { get; set; }

        /// <summary>
        /// 审批备注
        /// </summary>
        public string ReviewRemark { get; set; }

        /// <summary>
        /// 上传包体分析/外链分析结果
        /// </summary>
        public bool IsFileAnalysis { get; set; }
        /// <summary>
        /// 隐私协议分析结果
        /// </summary>
        public bool IsPrivacyAnalysis { get; set; }

    }
}