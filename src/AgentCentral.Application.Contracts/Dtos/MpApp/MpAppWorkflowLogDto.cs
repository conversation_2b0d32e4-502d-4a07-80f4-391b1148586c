using System;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpApp
{
    /// <summary>
    /// Mp_App工作流日志数据传输对象
    /// </summary>
    public class MpAppWorkflowLogDto
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// 应用状态
        /// </summary>
        public MpAppStatusEnum Status { get; set; }

        /// <summary>
        /// 审批备注
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// 审批人用户名
        /// </summary>
        public string ApprovalUserName { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime ApprovalDate { get; set; }

        /// <summary>
        /// 操作类型（审批、拒绝、下架等）
        /// </summary>
        public string OperationType { get; set; }


        /// <summary>
        /// 根据状态获取操作类型
        /// </summary>
        public static string GetOperationTypeFromStatus(MpAppStatusEnum status)
        {
            return status switch
            {
                MpAppStatusEnum.Pending => "Submit",
                MpAppStatusEnum.Published => "Approve",
                MpAppStatusEnum.Rejected => "Reject",
                MpAppStatusEnum.AutoApproved => "Auto Approve",
                MpAppStatusEnum.AutoRejected => "Auto Reject",
                MpAppStatusEnum.Delisted => "Delist",
                MpAppStatusEnum.NotSubmitted => "Create",
                _ => "Unknown"
            };
        }
    }
}