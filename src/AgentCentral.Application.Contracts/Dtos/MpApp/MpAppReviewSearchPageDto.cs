using System;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpApp
{
    /// <summary>
    /// Mp_App审核搜索请求DTO
    /// </summary>
    public class MpAppReviewSearchPageDto : BaseSearchPageDto
    {

        /// <summary>
        /// 应用Id
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 应用编码(D/P+6位数字)
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 开发者名称
        /// </summary>
        public string DeveloperName { get; set; }

        /// <summary>
        /// 应用状态（支持多状态同时查询）
        /// </summary>
        public MpAppStatusEnum[] AppStatus { get; set; } = [];

        /// <summary>
        /// 上传类型
        /// </summary>
        public UploadTypeEnum? UploadType { get; set; }

        /// <summary>
        /// 平台类型
        /// </summary>
        public PlatformTypeEnum? PlatformType { get; set; }

        /// <summary>
        /// 变现方式
        /// </summary>
        public MonetizationTypeEnum? MonetizationType { get; set; }

        /// <summary>
        /// 提交日期起始时间
        /// </summary>
        public DateTime? SubmitDateStart { get; set; }

        /// <summary>
        /// 提交日期结束时间
        /// </summary>
        public DateTime? SubmitDateEnd { get; set; }

        /// <summary>
        /// 最小总体评分
        /// </summary>
        public decimal? MinOverallScore { get; set; }

        /// <summary>
        /// 最大总体评分
        /// </summary>
        public decimal? MaxOverallScore { get; set; }

        /// <summary>
        /// 审核状态 0-Pending Review 1-Reviewed
        /// </summary>
        public int ReviewStatus { get; set; } = 0;

    }
} 