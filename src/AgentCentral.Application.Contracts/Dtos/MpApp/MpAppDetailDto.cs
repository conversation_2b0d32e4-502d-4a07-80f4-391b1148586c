using System;
using System.Collections.Generic;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.MpApp
{
    /// <summary>
    /// Mp_App详情响应DTO
    /// </summary>
    public class MpAppDetailDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 应用编码(D/P+6位数字)
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 默认语言
        /// </summary>
        public string DefaultLanguage { get; set; }

        /// <summary>
        /// 变现方式
        /// </summary>
        public MonetizationTypeEnum MonetizationType { get; set; }

        /// <summary>
        /// 隐私政策类型
        /// </summary>
        public PrivacyPolicyDocumentTypeEnum? PrivacyPolicyDocumentType { get; set; }

        /// <summary>
        /// 隐私政策URL地址
        /// </summary>
        public string PrivacyPolicyUrl { get; set; }

        /// <summary>
        /// 隐私政策文档名称
        /// </summary>
        public string PrivacyPolicyFileName { get; set; }

        /// <summary>
        /// 隐私政策文档大小(bytes)
        /// </summary>
        public long? PrivacyPolicyFileSize { get; set; }

        /// <summary>
        /// 隐私政策文档类型
        /// </summary>
        public string PrivacyPolicyFileType { get; set; }

        /// <summary>
        /// 应用市场展示名称
        /// </summary>
        public string AppDisplayName { get; set; }

        /// <summary>
        /// 简要描述
        /// </summary>
        public string ShortDescription { get; set; }

        /// <summary>
        /// 完整描述
        /// </summary>
        public string FullDescription { get; set; }

        /// <summary>
        /// 应用图标路径
        /// </summary>
        public string AppIcon { get; set; }

        /// <summary>
        /// 应用版本号
        /// </summary>
        public string VersionNumber { get; set; }

        /// <summary>
        /// 开发者名称
        /// </summary>
        public string DeveloperName { get; set; }

        /// <summary>
        /// 开发者联系方式
        /// </summary>
        public string DeveloperContact { get; set; }

        /// <summary>
        /// App类型分类ID
        /// </summary>
        public long? CategoryId { get; set; }

        /// <summary>
        /// APK文件路径
        /// </summary>
        public string ApkFilePath { get; set; }

        /// <summary>
        /// APK文件名称
        /// </summary>
        public string ApkName { get; set; }

        /// <summary>
        /// APK文件大小(bytes)
        /// </summary>
        public long? ApkSize { get; set; }

        /// <summary>
        /// 上传类型
        /// </summary>
        public UploadTypeEnum UploadType { get; set; }

        /// <summary>
        /// 平台类型
        /// </summary>
        public PlatformTypeEnum? PlatformType { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public bool? AllowDownload { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public long DownloadCount { get; set; }

        /// <summary>
        /// 应用状态
        /// </summary>
        public MpAppStatusEnum AppStatus { get; set; }

        /// <summary>
        /// 提交日期
        /// </summary>
        public DateTime? SubmitDate { get; set; }

        /// <summary>
        /// 上线日期
        /// </summary>
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 应用评分(1-5分)
        /// </summary>
        public decimal Rating { get; set; }

        /// <summary>
        /// 内容质量评分
        /// </summary>
        public decimal? ContentQualityScore { get; set; }

        /// <summary>
        /// 安全检查评分
        /// </summary>
        public decimal? SafetyCheckScore { get; set; }

        /// <summary>
        /// 合规性评分
        /// </summary>
        public decimal? ComplianceScore { get; set; }

        /// <summary>
        /// 总体评分
        /// </summary>
        public decimal? OverallScore { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 应用截图
        /// </summary>
        public string[] Screenshots { get; set; }

        /// <summary>
        /// 工作流记录
        /// </summary>
        public List<WorkflowLogDto> WorkflowLogs { get; set; }

    }
} 