namespace AgentCentral.Application.Contracts.Dtos
{
    public class JwtTokenDto
    {
        /// <summary>
        /// Access token
        /// </summary>
        public string Access_token { get; set; }

        /// <summary>
        /// Number of seconds until the token expires
        /// </summary>
        public long Expires_in { get; set; }

        /// <summary>
        /// Type of the token
        /// </summary>
        public string Token_type { get; set; }

        /// <summary>
        /// Refresh token
        /// </summary>
        public string Refresh_token { get; set; }

        /// <summary>
        /// Scope of the token
        /// </summary>
        public string Scope { get; set; }
    }
}
