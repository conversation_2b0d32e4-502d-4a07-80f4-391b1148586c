using AgentCentral.Domain.Shared.Enums;
using System;

namespace AgentCentral.Application.Contracts.Dtos.Feedback
{
    /// <summary>
    /// 反馈查询DTO
    /// </summary>
    public class FeedbackSearchDto
    {
        /// <summary>
        /// 问题类型
        /// </summary>
        public FeedbackIssueTypeEnum? IssueType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public FeedbackStatusEnum? Status { get; set; }

        /// <summary>
        /// 模块编码
        /// </summary>
        public string ModuleType { get; set; }

        /// <summary>
        /// 反馈内容
        /// </summary>
        public string Comments { get; set; }

        public string FeedbackId { get; set; }

        public DateTime? StartTime { get; set; }

        public DateTime? EndTime { get; set; }

        public string CompanyOrDepartment { get; set; }

        /// <summary>
        /// 每页显示数量
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
    }
}