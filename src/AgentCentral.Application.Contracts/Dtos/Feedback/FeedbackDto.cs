using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AgentCentral.Domain.Shared.Enums;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.Dtos.Feedback
{
    /// <summary>
    /// 反馈DTO
    /// </summary>
    public class FeedbackDto
    {
        /// <summary>
        /// 问题类型
        /// </summary>
        [Required(ErrorMessage = "问题类型不能为空")]
        public FeedbackIssueTypeEnum IssueType { get; set; }

        /// <summary>
        /// 名
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; }

        /// <summary>
        /// 反馈内容
        /// </summary>
        [Required(ErrorMessage = "反馈内容不能为空")]
        public string Comments { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public FeedbackStatusEnum Status { get; set; }

        /// <summary>
        /// AI生成的标题
        /// </summary>
        public string Title { get; set; }

        public List<long> AttachmentIds { get; set; } = [];
    }
}