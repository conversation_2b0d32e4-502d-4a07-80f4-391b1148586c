using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.Feedback
{
    public class FeedbackOutputDto : FeedbackDto
    {
        [JsonConverter(typeof(ValueToStringConverter))]
        public long Id { get; set; }

        /// <summary>
        /// Generator Id
        /// </summary>
        public string FeedbackId { get; set; }

        public List<AttachmentOutputDto> Files { get; set; } = [];

        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime CreateTime { get; set; }

        public string Company { get; set; }

        public string Department { get; set; }

        public string CreateName { get; set; }

        public string Module { get; set; }
    }
}
