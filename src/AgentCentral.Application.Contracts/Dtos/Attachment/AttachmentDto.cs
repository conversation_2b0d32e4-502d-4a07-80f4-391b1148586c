using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;

namespace AgentCentral.Application.Contracts.Dtos.Attachment;

public class AttachmentDto
{
    /// <summary>
    /// 附件ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 文件原名
    /// </summary>
    public string RealName { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 访问URL
    /// </summary>
    public string AccessUrl { get; set; }

    [JsonConverter(typeof(UsDateFormatConverter))]
    public DateTime CreateTime { get; set; }
}