using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.Dtos
{
    public class LoginDto
    {
        /// <summary>
        /// Username
        /// </summary>
        [Required(ErrorMessage = "The user name cannot be empty")]
        public string Username
        {
            set; get;
        }

        /// <summary>
        /// User password
        /// </summary>
        [Required(ErrorMessage = "The password cannot be empty")]
        public string Password
        {
            set; get;
        }
    }
}
