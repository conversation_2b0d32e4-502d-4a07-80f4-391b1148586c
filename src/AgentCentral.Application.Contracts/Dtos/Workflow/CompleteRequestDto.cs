namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// Data transfer object for completing (updating) a workflow process
    /// </summary>
    public class CompleteRequestDto
    {
        /// <summary>
        /// Optional comment
        /// </summary>
        public string Comment { get; set; }

        /// <summary>
        /// Selected route. By configuring this parameter, you can achieve the purpose of jumping to any node. Generally not used.
        /// </summary>
        public string Plan { get; set; }

        /// <summary>
        /// Task identifier
        /// </summary>
        public string TaskId { get; set; }

        /// <summary>
        /// User identifier
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Custom parameters
        /// </summary>
        public object Variables { get; set; }
    }
}
