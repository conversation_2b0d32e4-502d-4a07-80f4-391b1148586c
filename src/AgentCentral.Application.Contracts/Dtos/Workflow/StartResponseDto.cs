namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// Data transfer object for the response of starting a workflow process
    /// </summary>
    public class StartResponseDto : BaseResponseDto<StartResponseData>
    { }

    public class StartResponseData
    {
        public string ProcessDefinitionId { get; set; }

        public string ProcessName { get; set; }

        public string ProcessDefinitionKey { get; set; }

        public string ProcessInstanceId { get; set; }

        /// <summary>
        /// Custom parameters
        /// </summary>
        public object Variables { get; set; }

        /// <summary>
        /// Information about the current node of the process
        /// </summary>
        public TaskNodeResultList[] TaskNodeResultList { get; set; }

        /// <summary>
        /// Indicates whether the process has ended, used to determine if the last node has been reached
        /// </summary>
        public bool End { get; set; }
    }

}
