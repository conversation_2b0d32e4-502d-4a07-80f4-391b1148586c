using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// 工作流日志DTO
    /// </summary>
    public class WorkflowLogDto
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 业务ID
        /// </summary>
        public long BusinessId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public WorkflowAppStatusEnum Status { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int WorkflowStatus { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime ApprovalDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string ApprovalUserName { get; set; }

        /// <summary>
        /// 变更的字段
        /// </summary>
        public List<FieldChangeDto> ChangedFields { get; set; } = new List<FieldChangeDto>();
    }
}
