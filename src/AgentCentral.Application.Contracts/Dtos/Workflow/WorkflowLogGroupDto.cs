using AgentCentral.Domain.Shared.Enums;
using System;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// 工作流日志分组DTO
    /// </summary>
    public class WorkflowLogGroupDto
    {
        /// <summary>
        /// 状态
        /// </summary>
        public AppStatusEnum Status { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// 日志列表
        /// </summary>
        public List<WorkflowLogDto> Logs { get; set; } = new List<WorkflowLogDto>();
    }
}