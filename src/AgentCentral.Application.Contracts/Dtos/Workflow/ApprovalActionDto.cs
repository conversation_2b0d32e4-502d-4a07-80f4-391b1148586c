using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// Data transfer object for approval action
    /// </summary>
    public class ApprovalActionDto
    {
        /// <summary>
        /// Unique identifier of the business entity
        /// </summary>
        public long BusinessId { get; set; }

        /// <summary>
        /// Type of the business workflow
        /// </summary>
        public WorkflowBusinessTypeEnum BusinessType { get; set; } = WorkflowBusinessTypeEnum.AgentReview;

        /// <summary>
        /// Comments or notes related to the approval action
        /// </summary>
        public string Comments { get; set; }
    }
}
