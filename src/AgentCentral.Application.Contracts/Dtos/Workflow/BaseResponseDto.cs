namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// Base response data transfer object for Workflow
    /// </summary>
    public class BaseResponseDto<T>
    {
        /// <summary>
        /// Status code (1000 or 0: Success)
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// Version information
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Response msg
        /// </summary>
        public string Msg { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; }

        public T Data { get; set; }
    }
}
