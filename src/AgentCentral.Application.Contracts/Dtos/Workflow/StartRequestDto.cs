namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// Data transfer object for starting a workflow process
    /// </summary>
    public class StartRequestDto
    {
        /// <summary>
        /// Unique identifier of the submitted data
        /// </summary>
        public string BusinessId { get; set; }

        /// <summary>
        /// Number or code of the submitted data
        /// </summary>
        public string BusinessNo { get; set; }

        /// <summary>
        /// Process instance identifier
        /// </summary>
        public string ProcessInstanceId { get; set; }

        /// <summary>
        /// Name of the workflow process
        /// </summary>
        public string ProcessName { get; set; }

        /// <summary>
        /// User identifier
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Custom parameters
        /// </summary>
        public object Variables { get; set; }

        /*
         Example of custom parameters:
          "variables": {
              "PayTermType": "1",
              "CreditLimit": "10000"
            }
         */
    }
}
