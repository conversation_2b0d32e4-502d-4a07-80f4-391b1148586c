namespace AgentCentral.Application.Contracts.Dtos.Workflow
{
    /// <summary>
    /// Data transfer object for the response of completing (updating) a workflow process
    /// </summary>
    public class CompleteResponseDto : BaseResponseDto<CompleteResponseData>
    {

    }

    public class CompleteResponseData
    {
        public string ProcessDefinitionId { get; set; }

        public string ProcessDefinitionKey { get; set; }

        public string ProcessInstanceId { get; set; }

        /// <summary>
        /// Information about the current node of the process
        /// </summary>
        public TaskNodeResultList[] TaskNodeResultList { get; set; }

        /// <summary>
        /// Custom parameters
        /// </summary>
        public object Variables { get; set; }

        /// <summary>
        /// Indicates whether the process has ended, used to determine if the last node has been reached
        /// </summary>
        public bool End { get; set; }
    }

    public class TaskNodeResultList
    {
        /// <summary>
        /// Task identifier, used for subsequent process submissions
        /// </summary>
        public string TaskId { get; set; }

        public string NodeId { get; set; }

        /// <summary>
        /// Name of the current node, corresponds to the node name in the process diagram
        /// </summary>
        public string NodeName { get; set; }

        public bool SendEmail { get; set; }

        public object OtherAttributes { get; set; }

        public NextNodeList[] NextNodeList { get; set; }

        public object Person { get; set; }

        public Group Group { get; set; }

        public bool LastNode { get; set; }
    }

    public class Group
    {
        public string[] Role { get; set; }
    }

    public class NextNodeList
    {
        public object NodeId { get; set; }

        public string NodeName { get; set; }

        public bool State { get; set; }
    }
}
