using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.RequestModels.AppUsage
{
    public class UpdateAppUsageRequest
    {
        /// <summary>
        /// token数量
        /// </summary>
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "TotalTokens must be greater than 0")]
        public int TotalTokens { get; set; }

        /// <summary>
        /// 使用时长(秒)
        /// </summary>
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "CostSeconds must be greater than 0")]
        public int CostSeconds { get; set; }
    }
}