using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.RequestModels.AppUsage
{
    public class CreateAppUsageRequest
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        [Required(ErrorMessage = "AppId is required")]
        [StringLength(200, ErrorMessage = "AppId length should not exceed 200")]
        public string AppId { get; set; }
        /// <summary>
        /// token数量
        /// </summary>
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "TotalTokens must be greater than 0")]
        public int TotalTokens { get; set; }

        /// <summary>
        /// 使用时长(秒)
        /// </summary>
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "CostSeconds must be greater than 0")]
        public int CostSeconds { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        [Required]
        public long UserId { get; set; }

    }
}