using AgentCentral.Application.Contracts.Dtos.BaseDto;
using System;

namespace AgentCentral.Application.Contracts.RequestModels.ResourceManagement
{
    /// <summary>
    /// 资源流程查询条件
    /// </summary>
    public class ResourceProcessSearchDto : BaseSearchPageDto
    {
        public string UserName { get; set; }

        public string ContributorName { get; set; }

        public string ProcessEnvironment { get; set; }

        private DateTime? _uploadTimeStart;
        public DateTime? UploadTimeStart
        {
            get => _uploadTimeStart?.Date;
            set => _uploadTimeStart = value;
        }

        private DateTime? _uploadTimeEnd;
        public DateTime? UploadTimeEnd
        {
            get => _uploadTimeEnd?.Date.AddDays(1).AddTicks(-1);
            set => _uploadTimeEnd = value;
        }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string Process { get; set; }

        public string ProcessName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string Customer { get; set; }

        /// <summary>
        /// 零售商
        /// </summary>
        public string Retailer { get; set; }

        public string OrderBy { get; set; } = "createTime";

        public bool IsAsc { get; set; } = false;
    }
}