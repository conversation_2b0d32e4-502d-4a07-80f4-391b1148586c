using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.RequestModels.App
{
    public class UpdateAppRequest
    {
        /// <summary>
        /// 应用名称
        /// </summary>
        [Required(ErrorMessage = "AppName is required")]
        [StringLength(200, ErrorMessage = "AppName length should not exceed 200")]
        public string AppName { get; set; }
        /// <summary>
        /// 应用编码
        /// </summary>
        [StringLength(200, ErrorMessage = "AppCode length should not exceed 200")]
        public string AppCode { get; set; }
        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用模式
        /// </summary>
        [Required(ErrorMessage = "AppMode is required")]
        [StringLength(50, ErrorMessage = "AppMode length should not exceed 50")]
        public string AppMode { get; set; }

        /// <summary>
        /// 应用图标
        /// </summary>
        [StringLength(200, ErrorMessage = "AppIcon length should not exceed 200")]
        public string AppIcon { get; set; }

        /// <summary>
        /// 站点启用状态
        /// </summary>
        public bool EnableSite { get; set; }

        /// <summary>
        /// API启用状态
        /// </summary>
        public bool EnableApi { get; set; }

        public IList<string> DifyTagIds { get; set; }
    }
}