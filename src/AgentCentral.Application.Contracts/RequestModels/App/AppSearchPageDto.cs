using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.RequestModels.App
{
    public class AppSearchPageDto : BaseSearchPageDto
    {

        /// <summary>
        /// 应用模式
        /// </summary>
        public string AppMode { get; set; }

        /// <summary>
        /// TagIds
        /// </summary>
        public IList<long> TagIds { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public IList<AppDepartmentDto> Departments { get; set; } = [];
    }
}