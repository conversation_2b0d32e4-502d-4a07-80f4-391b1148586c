using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.RequestModels.DocResource
{
    public class CreateDocResourceRequest
    {
        [Required]
        public List<long> AttachmentIds { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; }

        [Required]
        [MaxLength(200)]
        public string Company { get; set; }

        [Required]
        [MaxLength(200)]
        public string Department { get; set; }

        [Required]
        [MaxLength(200)]
        public string Position { get; set; }

        [MaxLength(2000)]
        public string JobDescription { get; set; }

        public List<string> RelatedCustomers { get; set; }

        public List<string> RelatedSuppliers { get; set; }

        public List<CommonProgramDto> CommonPrograms { get; set; }
    }

    public class CommonProgramDto
    {
        [Required]
        [MaxLength(200)]
        public string ProgramName { get; set; }

        [Required]
        [MaxLength(500)]
        public string Link { get; set; }

        [Required]
        [<PERSON><PERSON>ength(200)]
        public string LoginAccount { get; set; }
    }
}