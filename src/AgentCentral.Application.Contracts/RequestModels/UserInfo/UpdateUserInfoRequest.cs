using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.RequestModels.UserInfo
{
    /// <summary>
    /// 更新用户信息请求模型
    /// </summary>
    public class UpdateUserInfoRequest
    {
        /// <summary>
        /// AttachmentId
        /// </summary>
        public long? AttachmentId { get; set; }

        /// <summary>
        /// 用户配置信息
        /// </summary>
        [Required(ErrorMessage = "Profile is required.")]
        public string UserProfile { get; set; }


        /// <summary>
        /// 注册时间
        /// </summary>
        [JsonIgnore]
        public DateTime RegisterTime { get; set; }
    }
}