using System;
using System.ComponentModel.DataAnnotations;

using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.RequestModels.AppReviewComment
{
    public class CreateAppReviewCommentRequest
    {
        /// <summary>
        /// 评分（1-5星）
        /// </summary>
        [Required(ErrorMessage = "Rating is required")]
        [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
        public int RatingStar { get; set; }

        /// <summary>
        /// 评价内容
        /// </summary>
        [Required(ErrorMessage = "Content is required")]
        public string ReviewContent { get; set; }

        public long CreateBy { get; set; } = 0;

        public string CreateName { get; set; } = string.Empty;

        public DateTime CreateTime { get; set; } = DateTime.UtcNow;
    }
}