using Newtonsoft.Json;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.RequestModels.Dify
{
    /// <summary>
    /// Dify Workflow 执行请求模型
    /// </summary>
    public class RunDifyWorkflowRequest
    {

        /// <summary>
        /// 允许传入 App 定义的各变量值
        /// inputs 参数包含了多组键值对（Key/Value pairs），每组的键对应一个特定变量，每组的值则是该变量的具体值
        /// </summary>
        [JsonProperty("inputs")]
        public object Inputs { get; set; }

        /// <summary>
        /// 返回响应模式，支持：
        /// streaming 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回。
        /// blocking 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）
        /// </summary>
        [JsonProperty("response_mode")]
        public string ResponseMode { get; set; } = ResponseModes.Blocking;

        /// <summary>
        /// 用户标识，用于定义终端用户的身份，方便检索、统计
        /// 由开发者定义规则，需保证用户标识在应用内唯一
        /// </summary>
        [JsonProperty("user")]
        public string User { get; set; }

        /// <summary>
        /// 文件列表，适用于传入文件结合文本理解并回答问题，仅当模型支持该类型文件解析能力时可用
        /// </summary>
        [JsonProperty("files")]
        public List<DifyFileInput> Files { get; set; } = [];
    }

    /// <summary>
    /// 响应模式常量
    /// </summary>
    public static class ResponseModes
    {
        /// <summary>
        /// 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回
        /// </summary>
        public const string Streaming = "streaming";

        /// <summary>
        /// 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）
        /// </summary>
        public const string Blocking = "blocking";
    }

    /// <summary>
    /// 文件类型常量
    /// </summary>
    public static class FileTypes
    {
        /// <summary>
        /// 文档类型：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
        /// </summary>
        public const string Document = "document";

        /// <summary>
        /// 图片类型：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
        /// </summary>
        public const string Image = "image";

        /// <summary>
        /// 音频类型：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
        /// </summary>
        public const string Audio = "audio";

        /// <summary>
        /// 视频类型：'MP4', 'MOV', 'MPEG', 'MPGA'
        /// </summary>
        public const string Video = "video";

        /// <summary>
        /// 自定义类型：其他文件类型
        /// </summary>
        public const string Custom = "custom";
    }

    /// <summary>
    /// 文件传递方式常量
    /// </summary>
    public static class TransferMethods
    {
        /// <summary>
        /// 远程 URL 方式
        /// </summary>
        public const string RemoteUrl = "remote_url";

        /// <summary>
        /// 本地文件上传方式
        /// </summary>
        public const string LocalFile = "local_file";
    }

    /// <summary>
    /// Dify 文件输入模型
    /// </summary>
    public class DifyFileInput
    {
        /// <summary>
        /// 支持类型：
        /// document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
        /// image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
        /// audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
        /// video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
        /// custom 具体类型包含：其他文件类型
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; }

        /// <summary>
        /// 传递方式，remote_url 图片地址 / local_file 上传文件
        /// </summary>
        [JsonProperty("transfer_method")]
        public string TransferMethod { get; set; }

        /// <summary>
        /// 图片地址（仅当传递方式为 remote_url 时）
        /// </summary>
        [JsonProperty("url")]
        public string Url { get; set; }

        /// <summary>
        /// 上传文件 ID（仅当传递方式为 local_file 时）
        /// </summary>
        [JsonProperty("upload_file_id")]
        public string UploadFileId { get; set; }

        public static DifyFileInput OfImageUrl(string url)
        {
            return new DifyFileInput
            {
                Type = FileTypes.Image,
                TransferMethod = TransferMethods.RemoteUrl,
                Url = url
            };
        }

        public static DifyFileInput OfDocUrl(string url)
        {
            return new DifyFileInput
            {
                Type = FileTypes.Document,
                TransferMethod = TransferMethods.RemoteUrl,
                Url = url
            };
        }
    }
}