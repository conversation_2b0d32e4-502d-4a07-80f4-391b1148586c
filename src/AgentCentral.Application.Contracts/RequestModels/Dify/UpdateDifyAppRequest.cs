using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.RequestModels.Dify
{
    public class UpdateDifyAppRequest
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("use_icon_as_answer_icon")]
        public bool UseIconAsAnswerIcon { get; set; } = false;
    }
}
