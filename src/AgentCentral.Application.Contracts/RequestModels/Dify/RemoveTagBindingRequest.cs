using AgentCentral.Domain.Shared.Enums;
using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.RequestModels.Dify
{
    public class RemoveTagBindingRequest
    {
        [JsonProperty("tag_id")]
        public string TagId { get; set; }

        [JsonProperty("target_id")]
        public string TargetId { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; } = TagTypeEnum.App.ToString().ToLower();
    }
}