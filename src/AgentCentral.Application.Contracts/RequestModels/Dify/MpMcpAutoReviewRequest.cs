using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.RequestModels.Dify
{
    /// <summary>
    /// Mp_Mcp自动审核请求DTO
    /// </summary>
    public class MpMcpAutoReviewRequest
    {
        /// <summary>
        /// 服务名称
        /// </summary>
        [JsonProperty("Service_Name")]
        public string ServiceName { get; set; }

        /// <summary>
        /// 服务描述
        /// </summary>
        [JsonProperty("Description")]
        public string Description { get; set; }

        /// <summary>
        /// 服务Logo
        /// </summary>
        [JsonProperty("MCP_Server_Logo")]
        public DifyFileInput Logo { get; set; }

        /// <summary>
        /// 使用场景
        /// </summary>
        [JsonProperty("Use_Cases")]
        public string UseCases { get; set; }
    }
} 