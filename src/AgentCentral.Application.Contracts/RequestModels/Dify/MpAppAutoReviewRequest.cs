using Newtonsoft.Json;

namespace AgentCentral.Application.Contracts.RequestModels.Dify
{
    /// <summary>
    /// Mp_App详情响应DTO
    /// </summary>
    public class MpAppAutoReviewRequest
    {

        /// <summary>
        /// 应用名称
        /// </summary>
        [JsonProperty("App_Name")]
        public string AppName { get; set; }


        /// <summary>
        /// 隐私政策URL地址
        /// </summary>
        [JsonProperty("Privacy_Policy")]
        public DifyFileInput PrivacyPolicy { get; set; }

        /// <summary>
        /// 简要描述
        /// </summary>
        [JsonProperty("Short_Description")]
        public string ShortDescription { get; set; }

        /// <summary>
        /// 完整描述
        /// </summary>
        [JsonProperty("Full_Description")]
        public string FullDescription { get; set; }

        /// <summary>
        /// 应用图标路径
        /// </summary>
        [JsonProperty("App_Icon")]
        public DifyFileInput AppIcon { get; set; }

        /// <summary>
        /// App截图
        /// </summary>
        [JsonProperty("Screenshot")]
        public DifyFileInput[] Screenshot { get; set; }

        /// <summary>
        /// 开发者名称
        /// </summary>
        [JsonProperty("Developer_Name")]
        public string DeveloperName { get; set; }

        /// <summary>
        /// 开发者联系方式
        /// </summary>
        [JsonProperty("Developer_Contact")]
        public string DeveloperContact { get; set; }

    }
} 