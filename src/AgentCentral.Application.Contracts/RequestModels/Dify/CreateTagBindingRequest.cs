using AgentCentral.Domain.Shared.Enums;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace AgentCentral.Application.Contracts.RequestModels.Dify
{
    public class CreateTagBindingRequest
    {
        [JsonProperty("tag_ids")]
        public List<string> TagIds { get; set; }

        [JsonProperty("target_id")]
        public string TargetId { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; } = TagTypeEnum.App.ToString().ToLower();
    }
}