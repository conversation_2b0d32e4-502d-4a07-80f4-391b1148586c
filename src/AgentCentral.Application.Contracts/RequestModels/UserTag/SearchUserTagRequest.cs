using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;

namespace AgentCentral.Application.Contracts.RequestModels.UserTag
{
    /// <summary>
    /// 搜索用户标签请求
    /// </summary>
    public class SearchUserTagRequest : BaseSearchPageDto
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        public string TagName { get; set; }

        /// <summary>
        /// 标签编码
        /// </summary>
        public string TagCode { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        public string TagType { get; set; }
    }
}
