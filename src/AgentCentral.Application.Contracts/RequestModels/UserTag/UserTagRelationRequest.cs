using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.RequestModels.UserTag
{
    /// <summary>
    /// 用户标签关联请求
    /// </summary>
    public class UserTagRelationRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public long UserId { get; set; }

        /// <summary>
        /// 标签ID列表
        /// </summary>
        [Required(ErrorMessage = "标签ID列表不能为空")]
        public List<long> TagIds { get; set; }
    }
}
