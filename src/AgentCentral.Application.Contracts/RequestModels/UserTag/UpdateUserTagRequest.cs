using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts.RequestModels.UserTag
{
    /// <summary>
    /// 更新用户标签请求
    /// </summary>
    public class UpdateUserTagRequest
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        [Required(ErrorMessage = "标签名称不能为空")]
        [StringLength(100, ErrorMessage = "标签名称长度不能超过100个字符")]
        public string TagName { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        [StringLength(50, ErrorMessage = "标签类型长度不能超过50个字符")]
        public string TagType { get; set; }
    }
}
