using AgentCentral.Domain.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.RequestModels.Tag
{
    public class CreateTagRequest
    {
        [Required(ErrorMessage = "TagName is required")]
        [StringLength(255, ErrorMessage = "TagName length should not exceed 255")]
        public string TagName { get; set; }

        public string DifyTagId { get; set; }

        public string TagType { get; set; } = TagTypeEnum.App.ToString();
    }
}
