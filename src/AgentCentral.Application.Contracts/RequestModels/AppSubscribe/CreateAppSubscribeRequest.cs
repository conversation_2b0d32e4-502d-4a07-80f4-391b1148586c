using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Application.Contracts.RequestModels.AppSubscribe
{
    public class CreateAppSubscribeRequest
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        [Required(ErrorMessage = "AppId is required")]
        [StringLength(200, ErrorMessage = "AppId length should not exceed 200")]
        public string AppId { get; set; }
    }
}