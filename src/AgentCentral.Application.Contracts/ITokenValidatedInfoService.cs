using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Infrastructure;
using System.Threading.Tasks;

namespace AgentCentral.Application.Contracts
{
    /// <summary>
    /// Token唯一标识验证服务
    /// </summary>
    public interface ITokenValidatedInfoService : ITransientService
    {
        Task<bool> AddTokenInfoAsync(TokenValidatedInfoOutputDto info);

        Task<bool> InvalidTokenInfoAsync(string token);

        Task<TokenValidatedInfoOutputDto> GetTokenInfoAsync(string token);

        Task<bool> IsExistTokenInfoAsync(string token);

        Task<bool> InvalidTokenInfosByUserIdAsync(string userId);

        Task<bool> UpdateExpireTimeAsync(string version, string userId);

        Task<bool> UpdateTokenInfoAsync(string version, string userId, TokenValidatedInfoOutputDto tokenInfo);
    }
}