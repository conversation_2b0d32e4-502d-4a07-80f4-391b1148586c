using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.SqlSugarDB;
using SqlSugar;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Domain.Entities.Workflow;
using Item.Internal.ChangeLog.Models;

namespace AgentCentral.Infrastructure.Repositories;

/// <summary>
/// 数据变更日志仓储实现
/// </summary>
public class DataChangeLogRepository : BaseRepository<DataChangeLog>, IDataChangeLogRepository
{
    public DataChangeLogRepository(ISqlSugarClient context) : base(context)
    {

    }

    /// <summary>
    /// 获取应用变更历史记录
    /// </summary>
    /// <param name="searchModel"></param>
    /// <returns></returns>
    public async Task<(List<AppChangeLogModel>, int)> GetAppChangeHistoryAsync(SearchReviewRecordModel searchModel)
    {
        var columnNames = new List<string>() { nameof(Def_App.IsActive), nameof(Def_App.IsCurrentVersion), nameof(Def_App.VersionNumber) };
        RefAsync<int> totalCount = 0;
        var result = await db.Queryable<Def_App>()
            .InnerJoin<DataChangeLog>((a, dl) => a.Id == SqlFunc.ToInt64(dl.PrimaryValue))
            .Where((a, dl) => dl.TableName == "def_app" && dl.ColumnName != nameof(Def_App.DifyPublished) && a.VersionNumber > 0)
            .WhereIF(!string.IsNullOrEmpty(searchModel.AgentName), (a, dl) => searchModel.AgentName.Contains(a.AppName))
            .WhereIF(!string.IsNullOrEmpty(searchModel.Creator), (a, dl) => a.CreateName.Contains(searchModel.Creator))
            .WhereIF(!string.IsNullOrEmpty(searchModel.AgentId), (a, dl) => a.GeneratorId == searchModel.AgentId)
            .Where((a, dl) => SqlFunc.Subqueryable<DataChangeLog>()
                        .Where(p => p.Id == dl.Id && columnNames.Contains(p.ColumnName) && p.OpType != OpTypeEnum.Insert)
                        .NotAny())
            .WhereIF(searchModel.StartTime.HasValue && searchModel.EndTime.HasValue, (a, dl) => dl.OpTime >= searchModel.StartTime.Value)
            .WhereIF(searchModel.StartTime.HasValue && searchModel.EndTime.HasValue, (a, dl) => dl.OpTime <= searchModel.EndTime.Value.AddDays(1).AddSeconds(-1))
            .GroupBy((a, dl) => new
            {
                dl.OpId,
                dl.OpType,
                a.MainAppId,
                a.AppName,
                AppId = SqlFunc.ToInt64(dl.PrimaryValue),
                a.VersionNumber,
                a.GeneratorId
            })
            .OrderByDescending((a, dl) => dl.OpTime)
            .Select((a, dl) => new
            {
                dl.OpId,
                dl.OpType,
                a.MainAppId,
                a.AppName,
                CreateName = SqlFunc.AggregateMax(a.CreateName),
                CreatorId = SqlFunc.AggregateMax(a.CreateBy),
                TimeStamp = SqlFunc.MappingColumn(default(DateTime), " FROM_UNIXTIME((SUBSTRING_INDEX(dl.OpId, '.', 1) - 116444736000000000) / 10000000)"),
                dl.AppId,
                a.VersionNumber,
                a.GeneratorId
            })
            .MergeTable()
            .LeftJoin<DataChangeLog>((t, l) => t.OpId == l.OpId && l.ColumnName == nameof(Def_App.AppName))
            .LeftJoin<DataChangeLog>((t, l, l1) => t.OpId == l1.OpId && l1.ColumnName == nameof(Def_App.AppStatus))
            .LeftJoin<WorkflowLog>
            ((t, l, l1, w) =>
            t.AppId == w.BusinessId
            && SqlFunc.ToInt32(l1.NewValue) == w.ApprovalStatus
            && t.TimeStamp >= SqlFunc.DateAdd(w.ApprovalDate, -1, DateType.Second)
            && t.TimeStamp <= SqlFunc.DateAdd(w.ApprovalDate, 1, DateType.Second)
            )
            .LeftJoin<Def_App_Change>((t, l, l1, w, c) => t.AppId == c.AppId)
            .WhereIF(!string.IsNullOrEmpty(searchModel.Reviewer), (t, l, l1, w, c) => w.ApprovalUserName.Contains(searchModel.Reviewer))
            .WhereIF(searchModel.Status.HasValue, (t, l, l1, w, c) => SqlFunc.ToInt32(SqlFunc.IsNull(l1.NewValue, "1")) == (int)searchModel.Status.Value)
            .WhereIF(searchModel.Operation.HasValue, (t, l, l1, w, c) => t.OpType == searchModel.Operation)
            .Select((t, l, l1, w, c) => new
            {
                t.AppId,
                AppName = SqlFunc.IsNull(l.NewValue, t.AppName),
                t.OpType,
                AppStatus = SqlFunc.ToInt32(SqlFunc.IsNull(l1.NewValue, "1")),
                t.TimeStamp,
                t.CreatorId,
                t.OpId,
                t.MainAppId,
                t.VersionNumber,
                ApprovalUserName = SqlFunc.IsNull(w.ApprovalUserName, ""),
                Description = SqlFunc.IsNull(w.Description, ""),
                ChangeDescription = SqlFunc.IsNull(c.ChangeDescription, ""),
                t.GeneratorId
            })
            .ToPageListAsync(searchModel.PageIndex, searchModel.PageSize, totalCount);

        var list = result.Select(x => new AppChangeLogModel
        {
            AppId = x.AppId,
            AppName = x.AppName,
            OpType = (int)x.OpType,
            AppStatus = x.AppStatus,
            OpTime = x.TimeStamp,
            CreatorId = x.CreatorId,
            OpId = x.OpId,
            MainAppId = x.MainAppId,
            VersionNumber = x.VersionNumber,
            ApprovalUserName = x.ApprovalUserName,
            Description = x.Description,
            ChangeDescription = x.ChangeDescription,
            GeneratorId = x.GeneratorId
        }).ToList();
        return (list, totalCount);
    }
}