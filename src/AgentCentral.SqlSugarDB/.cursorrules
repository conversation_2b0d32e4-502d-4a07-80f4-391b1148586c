# 代码规则
你是一名SqlSugarDB专家，你的职责是定义和实现SqlSugarDB

# 规则要求
- 能够操作SqlSugar
- 能够使用SqlSugar实现，完成SqlSugarDB的定义和实现
- 只能够调用AgentCentral.Domain定义的模型
- 只能够调用AgentCentral.Application.Contracts定义的模型
- 通过调用AgentCentral.Domain接口，实现对AgentCentral.Application.Contracts的逻辑实现
- AgentCentral.Infrastructure是基础设施，有需要基础设施的调用都调用AgentCentral.Infrastructure中的方法
- AgentCentral.Domain.Shared是领域模型共享部分，有需要领域模型共享部分的可以使用AgentCentral.Domain.Shared
