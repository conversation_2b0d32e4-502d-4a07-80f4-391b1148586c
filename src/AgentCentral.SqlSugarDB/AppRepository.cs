using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Entities.Workflow;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using SqlSugar;

namespace AgentCentral.SqlSugarDB
{
    public class AppRepository : BaseRepository<Def_App>, IAppRepository
    {
        private readonly UserContext _userContext;

        public AppRepository(ISqlSugarClient context,
            UserContext userContext) : base(context)
        {
            _userContext = userContext;
        }

        public async Task<(List<Def_App> list, int total)> GetPageListWithStatsAsync(
            Expression<Func<Def_App, bool>> expression,
            int pageIndex,
            int pageSize,
            string orderByField,
            bool isAsc,
            IList<long> tagIds,
            AppQueryModeEnum queryMode = AppQueryModeEnum.Public,
            Dictionary<string, List<string>>? departments = null)
        {
            // 在查询之前预处理权限条件
            var permissionConditions = departments?
                .SelectMany(d => d.Value.Select(v => new { Company = d.Key, DepartmentCode = v }))
                .ToList();

            var dbQuery = db.Queryable<Def_App>()
                .LeftJoin<Def_App_Usage_Stats>((a, s) => a.AppId == s.AppId)
                .LeftJoin<Def_User_Info>((a, s, u) => a.CreateBy == u.UserId)
                .WhereIF(expression != null, expression)
                .WhereIF(tagIds != null && tagIds.Count > 0, a => SqlFunc.Subqueryable<Def_Tag_Mapping>().Where(m => m.ResourceId == a.Id && m.IsActive && tagIds.Contains(m.TagId)).Any())
                .WhereIF(queryMode == AppQueryModeEnum.CurrentUser, (a, s, u) => a.CreateBy == _userContext.UserId && u.UserType == UserTypeEnum.Central)
                .WhereIF(queryMode == AppQueryModeEnum.Public && !string.IsNullOrEmpty(_userContext.DepartmentCode), (a, s) =>
                    SqlFunc.Subqueryable<Def_App_Permission>().Where(p => p.AppId == a.AppId && p.IsActive && p.DepartmentCode == _userContext.DepartmentCode && p.Company == _userContext.Company).Any() || a.AppPermission == AppPermissionEnum.Public)
                .WhereIF(queryMode == AppQueryModeEnum.Public && string.IsNullOrEmpty(_userContext.DepartmentCode), (a, s) =>
                    a.AppPermission == AppPermissionEnum.Public)
                .WhereIF(permissionConditions != null && permissionConditions.Any(), a =>
                    SqlFunc.Subqueryable<Def_App_Permission>()
                        .Where(p => p.AppId == a.AppId && p.IsActive)
                        .Where(p => permissionConditions.Any(c =>
                            p.Company == c.Company &&
                            p.DepartmentCode == c.DepartmentCode))
                        .Any())
                .Where((a, s, u) => a.IsCurrentVersion)
                .Select((a, s, u) => new
                {
                    Id = a.Id,
                    TenantId = a.TenantId,
                    IsActive = a.IsActive,
                    CreateBy = a.CreateBy,
                    CreateName = u.UserType == UserTypeEnum.Central ? "item.com" : a.CreateName,
                    CreateTime = a.CreateTime,
                    UpdateBy = a.UpdateBy,
                    UpdateName = a.UpdateName,
                    UpdateTime = a.UpdateTime,
                    AppId = a.AppId,
                    AppCode = a.AppCode,
                    AppName = a.AppName,
                    AppDescription = a.AppDescription,
                    AppMode = a.AppMode,
                    AppIcon = a.AppIcon,
                    EnableSite = a.EnableSite,
                    EnableApi = a.EnableApi,
                    AppTag = a.AppTag,
                    Department = a.Department,
                    AppStatus = a.AppStatus,
                    AppSource = a.AppSource,
                    RatingStar = SqlFunc.Subqueryable<Def_App_Review_Comment>().Where(arc => arc.AppId == a.AppId && arc.IsActive)
                        .Avg(arc => arc.RatingStar),
                    TotalStar = SqlFunc.Subqueryable<Def_App_Review_Comment>().Where(arc => arc.AppId == a.AppId && arc.IsActive)
                        .Sum(arc => arc.RatingStar),
                    Tags = SqlFunc.Subqueryable<Def_Tag_Mapping>().InnerJoin<Def_Tag>((m, t) => m.TagId == t.Id && m.IsActive && t.IsActive).Where((m, t) => m.ResourceId == a.Id).SelectStringJoin((m, t) => t.TagName, ","),
                    a.GeneratorId
                })
                .OrderByIF(!string.IsNullOrEmpty(orderByField) && orderByField.Equals("ratingstar", StringComparison.CurrentCultureIgnoreCase), $"{orderByField} {(isAsc ? "asc" : "desc")},TotalStar desc")
                .OrderByIF(!string.IsNullOrEmpty(orderByField) && !orderByField.Equals("ratingstar", StringComparison.CurrentCultureIgnoreCase), $"{orderByField} {(isAsc ? "asc" : "desc")}")
                .OrderByPropertyName("AppId", OrderByType.Asc);

            RefAsync<int> totalCount = 0;
            var list = await dbQuery.ToPageListAsync(pageIndex, pageSize, totalCount);

            return (list.Select(s => new Def_App
            {
                Id = s.Id,
                TenantId = s.TenantId,
                IsActive = s.IsActive,
                CreateBy = s.CreateBy,
                CreateName = s.CreateName,
                CreateTime = s.CreateTime,
                UpdateBy = s.UpdateBy,
                UpdateName = s.UpdateName,
                UpdateTime = s.UpdateTime,
                AppId = s.AppId,
                AppCode = s.AppCode,
                AppName = s.AppName,
                AppDescription = s.AppDescription,
                AppMode = s.AppMode,
                AppIcon = s.AppIcon,
                EnableSite = s.EnableSite,
                EnableApi = s.EnableApi,
                AppTag = s.Tags,
                Department = s.Department,
                AppStatus = s.AppStatus,
                AppSource = s.AppSource,
                GeneratorId = s.GeneratorId
            }).ToList(), totalCount);
        }

        public async Task<Def_App> GetAppDetailByAppId(string appId)
        {
            return await db.Queryable<Def_App>().Where(a => a.AppId == appId && (a.CreateBy == _userContext.UserId || _userContext.UserType == UserTypeEnum.Admin || _userContext.UserType == UserTypeEnum.Central)).FirstAsync();
        }

        public async Task<(List<AppReviewListModel> list, int total)> GetReviewPageListAsync(
            int pageIndex,
            int pageSize,
            string orderByField,
            bool isAsc,
            string agentName,
            string creator,
            string company,
            string department,
            List<long> tagIds,
            AppStatusEnum? status,
            AppPermissionEnum? permission,
            string reviewer,
            string appMode,
            bool? guestMode,
            int reviewStatus)
        {
            List<AppStatusEnum> statusEnumList = new List<AppStatusEnum>();
            if (reviewStatus == 1)
            {
                statusEnumList = new List<AppStatusEnum>
                {
                    AppStatusEnum.AutoApproved,
                    AppStatusEnum.AutoRejected,
                };
            }
            else
            {
                statusEnumList = new List<AppStatusEnum>
                {
                    AppStatusEnum.Published,
                    AppStatusEnum.Rejected,
                    AppStatusEnum.Delisted,
                    AppStatusEnum.Deleted,
                };
            }
            var query = db.Queryable<Def_App>()
                .LeftJoin<Def_User_Info>((a, u) => a.CreateBy == u.UserId && (u.UserType == UserTypeEnum.User || u.UserType == UserTypeEnum.Admin))
                .LeftJoin<WorkflowInfo>((a, u, w) => a.Id == w.BusinessId && w.Category == (int)WorkflowBusinessTypeEnum.AgentReview)
                    .WhereIF(!string.IsNullOrEmpty(agentName), a => a.AppName.Contains(agentName))
                    .WhereIF(!string.IsNullOrEmpty(creator), a => a.CreateName.Contains(creator))
                    .WhereIF(!string.IsNullOrEmpty(appMode), (a, u) => a.AppMode == appMode)
                    .WhereIF(!string.IsNullOrEmpty(reviewer), (a, u, w) => w.UpdateName.Contains(reviewer))
                    .WhereIF(!string.IsNullOrEmpty(company), (a, u) => u.CompanyName.Contains(company))
                    .WhereIF(!string.IsNullOrEmpty(department), (a, u) => u.DepartmentName.Contains(department))
                    .WhereIF(tagIds != null && tagIds.Count > 0, a =>
                        SqlFunc.Subqueryable<Def_Tag_Mapping>()
                            .Where(m => m.ResourceId == a.Id && m.IsActive && tagIds.Contains(m.TagId))
                            .Any())
                    .Where(a => a.AppStatus != AppStatusEnum.Unpublished)
                    .WhereIF(status.HasValue && statusEnumList.Contains(status.Value), a => a.AppStatus == status.Value)
                    .WhereIF(permission.HasValue, (a, u) => a.AppPermission == permission.Value)
                    .WhereIF(guestMode.HasValue, (a, u) => a.GuestMode == guestMode.Value)
                    .WhereIF(reviewStatus == 1 && status == null, a => statusEnumList.Contains(a.AppStatus))
                    .WhereIF(reviewStatus == 2, a => statusEnumList.Contains(a.AppStatus))
                    // .WhereIF(reviewStatus == 2, a =>
                    //     SqlFunc.Subqueryable<WorkflowLog>()
                    //         .Where(w => w.BusinessId == a.Id &&
                    //                   w.ApprovalUserId == _userContext.UserId &&
                    //                   w.ApprovalDate == SqlFunc.Subqueryable<WorkflowLog>()
                    //                         .Where(w2 => w2.BusinessId == a.Id &&
                    //                                w2.ApprovalUserId == _userContext.UserId)
                    //                         .Max(w2 => w2.ApprovalDate))
                    //         .Any())
                    //.OrderByIF(!string.IsNullOrEmpty(orderByField), $"{orderByField} {(isAsc ? "asc" : "desc")}")
                    .OrderBy(a => SqlFunc.Subqueryable<WorkflowLog>()
                        .Where(w => w.BusinessId == a.Id)
                        .Max(w => w.ApprovalDate), OrderByType.Desc);

            RefAsync<int> totalCount = 0;
            var list = await query.Select((a, u, w) => new AppReviewListModel
            {
                Id = a.Id,
                AppId = a.AppId,
                AppCode = a.AppCode,
                AppName = a.AppName,
                AppDescription = a.AppDescription,
                AppMode = a.AppMode,
                AppIcon = a.AppIcon,
                EnableSite = a.EnableSite,
                EnableApi = a.EnableApi,
                AppPermission = a.AppPermission,
                AppStatus = a.AppStatus,
                AppSource = a.AppSource,
                ContentQualityScore = a.ContentQualityScore,
                SafetyCheckScore = a.SafetyCheckScore,
                ComplianceScore = a.ComplianceScore,
                OverallScore = a.OverallScore,
                DifyPublished = a.DifyPublished,
                CompanyName = u.CompanyName,
                DepartmentName = u.DepartmentName,
                CreateName = a.CreateName,
                CreateTime = a.CreateTime,
                GuestMode = a.GuestMode,
                SubmitDate = a.SubmitDate,
                Reviewer = w.UpdateName,
                AppTag = SqlFunc.Subqueryable<Def_Tag_Mapping>().InnerJoin<Def_Tag>((m, t) => m.TagId == t.Id && m.IsActive && t.IsActive).Where((m, t) => m.ResourceId == a.Id).SelectStringJoin((m, t) => t.TagName, ","),
                ApprovalDate = SqlFunc.Subqueryable<WorkflowLog>()
                        .Where(w => w.BusinessId == a.Id)
                        .Max(w => w.ApprovalDate)
            }).ToPageListAsync(pageIndex, pageSize, totalCount);

            return (list, totalCount);
        }

        public async Task<(List<AppListModel> list, int total)> GetPlatformPageListAsync(
            Expression<Func<Def_App, bool>> expression,
            int pageIndex,
            int pageSize,
            string orderByField,
            bool isAsc,
            IList<long> tagIds,
            bool queryUser = false
            )
        {
            var subQuery = db.Queryable<Def_App>()
                .Select(a => new
                {
                    a.Id,
                    RowNum = SqlFunc.RowNumber(SqlFunc.Desc(a.CreateTime), a.MainAppId)
                });

            var dbQuery = db.Queryable<Def_App>()
                .InnerJoin(subQuery, (a, sub) => a.Id == sub.Id)
                .LeftJoin<Def_App_Usage_Stats>((a, sub, s) => a.AppId == s.AppId)
                .LeftJoin<Def_User_Info>((a, sub, s, u) => a.CreateBy == u.UserId)
                .WhereIF(expression != null, expression)
                .WhereIF(tagIds != null && tagIds.Count > 0, a => SqlFunc.Subqueryable<Def_Tag_Mapping>().Where(m => m.ResourceId == a.Id && m.IsActive && tagIds.Contains(m.TagId)).Any())
                .WhereIF(queryUser, (a, sub, s, u) => a.CreateBy == _userContext.UserId)
                .Where((a, sub, s, u) => u.UserType == UserTypeEnum.Central)
                .Where((a, sub, s, u) => sub.RowNum == 1)
                .Select((a, sub, s, u) => new AppListModel
                {
                    Id = a.Id,
                    TenantId = a.TenantId,
                    IsActive = a.IsActive,
                    CreateBy = a.CreateBy,
                    CreateName = u.UserType == UserTypeEnum.Central ? "item.com" : a.CreateName,
                    CreateTime = a.CreateTime,
                    UpdateBy = a.UpdateBy,
                    UpdateName = a.UpdateName,
                    UpdateTime = a.UpdateTime,
                    AppId = a.AppId,
                    AppCode = a.AppCode,
                    AppName = a.AppName,
                    AppDescription = a.AppDescription,
                    AppMode = a.AppMode,
                    AppIcon = a.AppIcon,
                    EnableSite = a.EnableSite,
                    EnableApi = a.EnableApi,
                    AppTag = a.AppTag,
                    Department = a.Department,
                    AppStatus = a.AppStatus,
                    AppSource = a.AppSource,
                    RatingStar = SqlFunc.Subqueryable<Def_App_Review_Comment>().Where(arc => arc.MainAppId == a.MainAppId && arc.IsActive)
                    .Avg(arc => arc.RatingStar),
                    TotalStar = SqlFunc.Subqueryable<Def_App_Review_Comment>().Where(arc => arc.MainAppId == a.MainAppId && arc.IsActive)
                    .Sum(arc => arc.RatingStar),
                    Tags = SqlFunc.Subqueryable<Def_Tag_Mapping>().InnerJoin<Def_Tag>((m, t) => m.TagId == t.Id && m.IsActive && t.IsActive).Where((m, t) => m.ResourceId == a.Id).SelectStringJoin((m, t) => t.TagName, ","),
                    RejectReason = SqlFunc.Subqueryable<WorkflowLog>()
                        .Where(arc => arc.BusinessId == a.Id && (arc.ApprovalStatus == (int)AppStatusEnum.Rejected || arc.ApprovalStatus == (int)AppStatusEnum.AutoRejected))
                        .OrderByDesc(arc => arc.ApprovalDate)
                        .Select(arc => arc.Description),
                    MainAppId = a.MainAppId,
                    DifyPublished = a.DifyPublished,
                    GeneratorId = a.GeneratorId
                })
                .OrderByIF(!string.IsNullOrEmpty(orderByField) && orderByField.Equals("ratingstar", StringComparison.CurrentCultureIgnoreCase), $"{orderByField} {(isAsc ? "asc" : "desc")},TotalStar desc")
                .OrderByIF(!string.IsNullOrEmpty(orderByField) && !orderByField.Equals("ratingstar", StringComparison.CurrentCultureIgnoreCase), $"{orderByField} {(isAsc ? "asc" : "desc")}")
                .OrderByPropertyName("AppId", OrderByType.Asc);

            RefAsync<int> totalCount = 0;
            var list = await dbQuery.ToPageListAsync(pageIndex, pageSize, totalCount);

            return (list, totalCount);
        }
    }
}