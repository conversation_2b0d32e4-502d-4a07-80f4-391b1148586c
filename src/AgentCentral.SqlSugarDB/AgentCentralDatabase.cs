using System.Data.Common;
using Item.Common.Lib.LogUtil;
using Item.Internal.ChangeLog;
using Item.Internal.ChangeLog.Models;
using Microsoft.AspNetCore.Http;
using MySqlConnector;
using SqlSugar;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace AgentCentral.SqlSugarDB;

public class AgentCentralDatabase : IDatabase, IDisposable, IAsyncDisposable
{
    private readonly IChangeLogProfileService _profileService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly MySqlConnection _connection;
    private readonly string _tableName;
    private readonly Dictionary<string, (string PropName, MySqlDbType DbType)> _map;
    private string _tenantId = string.Empty;

    public AgentCentralDatabase(
        IChangeLogProfileService profileService, IHttpContextAccessor httpContextAccessor, IConfiguration configuration)
    {
        _httpContextAccessor = httpContextAccessor;
        _tableName = "data_change_log";
        _profileService = profileService;
        _connection = new MySqlConnection(configuration.GetConnectionString("AgentCentral"));
        _map = [];
        _map.Add("Id", (nameof(DataChangeLogModel.Id), MySqlDbType.Int64));
        _map.Add("TableName", (nameof(DataChangeLogModel.TableName), MySqlDbType.VarChar));
        _map.Add("ColumnName", (nameof(DataChangeLogModel.ColumnName), MySqlDbType.VarChar));
        _map.Add("NewValue", (nameof(DataChangeLogModel.NewValue), MySqlDbType.MediumText));
        _map.Add("OldValue", (nameof(DataChangeLogModel.OldValue), MySqlDbType.MediumText));
        _map.Add("PrimaryValue", (nameof(DataChangeLogModel.PrimaryValue), MySqlDbType.VarChar));
        _map.Add("UserId", (nameof(DataChangeLogModel.UserId), MySqlDbType.VarChar));
        _map.Add("UserName", (nameof(DataChangeLogModel.UserName), MySqlDbType.VarChar));
        _map.Add("OpTime", (nameof(DataChangeLogModel.OpTime), MySqlDbType.DateTime));
        _map.Add("TenantId", (nameof(DataChangeLogModel.TenantId), MySqlDbType.VarChar));
        _map.Add("AppId", (nameof(DataChangeLogModel.CustomerId), MySqlDbType.Int64));
        _map.Add("OpType", (nameof(DataChangeLogModel.OpType), MySqlDbType.Int32));
        _map.Add("OpId", (nameof(DataChangeLogModel.OpId), MySqlDbType.VarChar));
        _map.Add("ExtendData", (nameof(DataChangeLogModel.ExtendData), MySqlDbType.VarChar));
    }

    private static List<MySqlParameter> CloneParameters(List<MySqlParameter> parameters)
    {
        var list = new List<MySqlParameter>();
        foreach (var parameter in parameters)
        {
            list.Add(parameter.Clone());
        }
        return list;
    }

    private async Task<int> ExecuteNonQueryAsync(string sql, List<MySqlParameter> parameters)
    {
        var command = new MySqlCommand(sql, _connection);
        command.Parameters.AddRange(parameters.ToArray());
        return await command.ExecuteNonQueryAsync();
    }

    private async Task<DbDataReader> ExecuteReaderAsync(string sql, List<MySqlParameter> npgsqlParameters)
    {
        var command = new MySqlCommand(sql, _connection);
        command.Parameters.AddRange(npgsqlParameters.ToArray());
        var reader = await command.ExecuteReaderAsync();
        return reader;
    }

    private async Task<T> ExecuteScalarAsync<T>(string sql, List<MySqlParameter> npgsqlParameters)
    {
        var command = new MySqlCommand(sql, _connection);
        command.Parameters.AddRange(npgsqlParameters.ToArray());
        var value = await command.ExecuteScalarAsync();
        return (T)Convert.ChangeType(value, typeof(T))!;
    }

    private static List<T> ReadToObject<T>(DbDataReader reader, Dictionary<string, (string PropName, MySqlDbType DbType)> map) where T : class, new()
    {
        var list = new List<T>();
        var type = typeof(T);
        var props = type.GetProperties();

        while (reader.Read())
        {
            var itemObj = Activator.CreateInstance<T>();
            list.Add(itemObj);
            for (var i = 0; i < reader.FieldCount; i++)
            {
                var name = reader.GetName(i);
                var obj = reader[name];
                if (map.ContainsKey(name))
                {
                    var prop = type.GetProperty(map[name].PropName);
                    if (prop != null)
                    {
                        if (obj == null)
                            continue;

                        try
                        {
                            object value;
                            if (prop.PropertyType == typeof(DateTimeOffset))
                            {
                                DateTime dateTime = (DateTime)obj;
                                TimeSpan offset = TimeZoneInfo.Local.GetUtcOffset(dateTime);
                                DateTimeOffset dateTimeOffset = new DateTimeOffset(dateTime, offset);
                                value = dateTimeOffset;
                            }
                            else if (prop.PropertyType.IsEnum)
                            {
                                value = (int)obj;
                            }
                            else
                                value = Convert.ChangeType(obj, prop.PropertyType);

                            prop.SetValue(itemObj, value);
                        }
                        catch (Exception ex)
                        {
                            UnisLog.Error(ex, "read change log error");
                            throw;
                        }
                    }
                }
            }
        }
        reader.Close();
        return list;
    }

    private static List<string> ReaderFirstColumn(DbDataReader reader)
    {
        var list = new List<string>();
        while (reader.Read())
        {
            if (reader[0]?.ToString() != null)
            {
                list.Add(reader[0].ToString()!);
            }
        }
        reader.Close();
        return list;
    }

    private static List<MySqlParameter> ConvertParameter(List<string> list)
    {
        var ps = new List<MySqlParameter>();
        var strList = new List<string>();
        var index = 0;
        foreach (var id in list)
        {
            var name = $"@opId{index}";
            strList.Add(name);
            ps.Add(new MySqlParameter(name, id));
            index++;
        }
        return ps;
    }

    private (string Sql, List<MySqlParameter> Parameters) BuildQuerySql(Dictionary<string, List<string>> tables, SearchModel model)
    {
        long? customerId = model.BusinessId;
        var parameters = new List<MySqlParameter>();

        if (customerId.HasValue)
        {
            parameters.Add(new MySqlParameter("@AppId", customerId));
        }

        var whereStr = string.Empty;

        var list = new List<string>();
        var rowIndex = 0;
        foreach (var item in tables)
        {
            var pName = $"@tableName{rowIndex}";
            parameters.Add(new MySqlParameter(pName, item.Key));

            list.Add($"(TableName = {pName} and ColumnName in ({(new Func<string>(() =>
            {
                var columnIndex = 0;
                var columnNameList = item.Value.Select(columnName =>
                {
                    pName = $"@column{rowIndex}{columnIndex}";
                    parameters.Add(new MySqlParameter(pName, columnName));
                    columnIndex++;
                    return pName;
                });

                return string.Join(",", columnNameList);
            })).Invoke()}))");
            rowIndex++;
        }

        var sql = @$"SELECT OpId FROM {_tableName}
        WHERE AppId = @AppId {(!string.IsNullOrEmpty(_tenantId) ? " AND TenantId = @TenantId" : string.Empty)}
        AND ( {string.Join(" or ", list)} ) 
        GROUP BY OpId ORDER BY OpId DESC";

        if (!string.IsNullOrEmpty(_tenantId))
        {
            parameters.Add(new MySqlParameter("@TenantId", _tenantId));
        }

        return (sql, parameters);
    }

    private (string sql, List<MySqlParameter> Parameters) BuildInsertSql(List<DataChangeLogModel> entities)
    {
        var ps = new List<MySqlParameter>();
        List<string> names = new List<string>();
        List<string> values = new List<string>();
        var rowIndex = 0;
        foreach (var entity in entities)
        {
            var temps = new List<string>();
            typeof(DataChangeLogModel).GetProperties().ToList().ForEach(p =>
            {
                if (p.Name.Equals("id", StringComparison.OrdinalIgnoreCase))
                    return;

                var field = _map.FirstOrDefault(x => x.Value.PropName == p.Name);

                if (rowIndex == 0)
                {
                    names.Add(field.Key);
                }

                var pName = $"@{p.Name}{rowIndex}";
                temps.Add($"{pName}");
                var value = p.GetValue(entity);
                if (p.Name.Equals("customerid", StringComparison.OrdinalIgnoreCase))
                {
                    if (!long.TryParse(value?.ToString(), out var appId))
                    {
                        var sql = $"select Id from Def_App where AppId='{value}'";
                        value = ExecuteScalarAsync<long>(sql, []).GetAwaiter().GetResult();
                    }
                }
                if (p.PropertyType.IsEnum)
                    value = (int)value!;
                ps.Add(new MySqlParameter(pName, field.Value.DbType) { Value = value ?? DBNull.Value });
            });
            values.Add($"({string.Join(",", temps)})");
            rowIndex++;
        }
        var sql = $"INSERT INTO {_tableName}({string.Join(",", names)}) VALUES " +
            $"{string.Join(",", values)}";

        return (sql, ps);
    }

    public async Task<PageingResultModel<DataChangeLogModel>> GetChangeLogAsync(SearchModel searchModel)
    {
        _tenantId = searchModel.TenantId ?? string.Empty;

        var tables = _profileService.GetTableListByCategory(searchModel.Category);
        if (tables.Count == 0)
            return new PageingResultModel<DataChangeLogModel>()
            {
                PageIndex = 1,
                PageSize = searchModel.Pageing.PageSize,
                TotalCount = 0
            };

        var hasExtensionProperty = _profileService.GetIncludeExtensionProperty(searchModel.Category);

        var dict = new Dictionary<string, List<string>>();
        foreach (var table in tables)
        {
            var columns = _profileService.GetColumnList(searchModel.Category, table);
            dict.Add(table, columns);
        }

        try
        {
            var (sql, paramsters) = BuildQuerySql(dict, searchModel);

            await _connection.OpenAsync();
            var total = await ExecuteScalarAsync<int>($"SELECT COUNT(*) FROM ({sql}) AS temp", CloneParameters(paramsters));

            var reader = await ExecuteReaderAsync($"SELECT * FROM ({sql}) AS temp LIMIT @pageSize,@skip",

                [
                    .. CloneParameters(paramsters),
                    new MySqlParameter("@pageSize", searchModel.Pageing.PageSize),
                    new MySqlParameter("@skip", (searchModel.Pageing.PageIndex - 1) * searchModel.Pageing.PageSize)
                ]);

            var ids = ReaderFirstColumn(reader);

            var opIdParams = ConvertParameter(ids);
            var opIdNames = opIdParams.Select(x => x.ParameterName).ToList();

            List<DataChangeLogModel> objs = [];
            if (opIdNames.Count > 0)
            {
                reader = await ExecuteReaderAsync($"SELECT * FROM {_tableName} WHERE OpId IN ({string.Join(",", opIdNames)}) ORDER BY OpId DESC", [.. opIdParams]);
                objs = ReadToObject<DataChangeLogModel>(reader, _map);
            }

            var result = new PageingResultModel<DataChangeLogModel>
            {
                PageSize = searchModel.Pageing.PageSize,
                PageIndex = searchModel.Pageing.PageIndex,
                TotalCount = total,
                Data = objs,
            };
            return result;
        }
        catch (Exception ex)
        {
            UnisLog.Error(ex, "read data_change_log error");
            throw;
        }
        finally
        {
            await _connection.CloseAsync();
        }
    }

    public async Task WriteAsync(List<DataChangeLogModel> entities)
    {
        try
        {
            if (entities.Count <= 0)
                return;

            await _connection.OpenAsync();
            var pageSize = 100;
            for (var i = 0; i < Math.Ceiling((double)entities.Count / pageSize); i++)
            {
                var datas = entities.Skip((i) * pageSize).Take(pageSize);

                var (sql, parameters) = BuildInsertSql(datas.ToList());
                var count = await ExecuteNonQueryAsync(sql, parameters);
                UnisLog.Information($"insert data {count} size");
            }
        }
        catch (Exception ex)
        {
            UnisLog.Error(ex, "change log error");
            throw;
        }
        finally
        {
            await _connection.CloseAsync();
        }
    }

    public void Dispose()
    {
        try
        {
            GC.SuppressFinalize(this);
            _connection.Dispose();
        }
        finally { }
    }

    public string GetSqlString(string sql, DbParameter[] parameters)
    {
        foreach (var item in parameters)
        {
            var value = string.Empty;
            if (item.Value == null)
                value = string.Empty;
            else
            {
                var type = item.Value.GetType();
                object ObjValue = item.Value;
                if (type.IsGenericType)
                {
                    var type1 = type.GetGenericArguments()[0];
                    if (typeof(Nullable<>).MakeGenericType(type1) == type)
                    {
                        ObjValue = type.GetProperty("Value")!.GetValue(ObjValue, null)!;
                        type = type1;
                    }
                }

                if (type == typeof(string))
                    value = $"'{item.Value}'";
                else if (new Type[] { typeof(long), typeof(int), typeof(double), typeof(decimal), typeof(byte) }.Contains(type))
                    value = ObjValue.ToString();
                else if (type == typeof(DateTime))
                {
                    value = ((DateTime)ObjValue).ToString("yyyy-MM-dd HH:mm:ss");
                }
            }

            sql = sql.Replace(item.ParameterName, value);
        }
        return sql;
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            GC.SuppressFinalize(this);
            await _connection.DisposeAsync();
        }
        finally { }
    }
}
