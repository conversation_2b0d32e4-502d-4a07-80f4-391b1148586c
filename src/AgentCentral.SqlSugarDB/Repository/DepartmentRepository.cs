using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 部门数据访问实现
    /// </summary>
    public class DepartmentRepository : BaseRepository<Def_Department>, IDepartmentRepository, IScopedService
    {
        public DepartmentRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取部门树形结构
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <param name="condition">查询条件</param>
        /// <returns>部门树结构</returns>
        public async Task<List<Def_Department>> GetDepartmentTreeAsync(string tenantId, Expression<Func<Def_Department, bool>> condition = null)
        {
            Expression<Func<Def_Department, bool>> filter;
            if (condition != null)
            {
                filter = Expressionable.Create<Def_Department>()
                    .And(x => x.TenantId == tenantId && x.IsActive == true)
                    .And(condition)
                    .ToExpression();
            }
            else
            {
                filter = x => x.TenantId == tenantId && x.IsActive == true;
            }

            // 获取所有符合条件的部门
            List<Def_Department> allDepartments = await base.GetListAsync(filter);

            // 获取顶级部门
            List<Def_Department> rootDepartments = allDepartments.Where(d => d.ParentId == null).ToList();

            // 递归构建树结构
            return rootDepartments;
        }

        /// <summary>
        /// 检查部门编码是否已存在
        /// </summary>
        /// <param name="departmentCode">部门编码</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="excludeId">排除的部门ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsDepartmentCodeExistsAsync(string departmentCode, string tenantId, long? excludeId = null)
        {
            Expression<Func<Def_Department, bool>> filter = Expressionable.Create<Def_Department>()
                .And(x => x.DepartmentCode == departmentCode && x.IsActive == true)
                .And(x => x.TenantId == tenantId)
                .AndIF(excludeId.HasValue, x => x.Id != excludeId.Value)
                .ToExpression();

            return await base.IsAnyAsync(filter);
        }

        /// <summary>
        /// 获取部门及其所有子部门
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>部门及其子部门列表</returns>
        public async Task<List<Def_Department>> GetDepartmentAndChildrenAsync(long departmentId, string tenantId)
        {
            Expression<Func<Def_Department, bool>> filter = x => x.TenantId == tenantId && x.IsActive == true;
            List<Def_Department> allDepartments = await base.GetListAsync(filter);

            // 获取当前部门
            Def_Department currentDepartment = allDepartments.FirstOrDefault(d => d.Id == departmentId);
            if (currentDepartment == null)
            {
                return new List<Def_Department>();
            }

            // 获取所有子部门
            List<Def_Department> result = new List<Def_Department> { currentDepartment };
            GetChildDepartments(allDepartments, departmentId, result);

            return result;
        }

        /// <summary>
        /// 递归获取子部门
        /// </summary>
        private void GetChildDepartments(List<Def_Department> allDepartments, long parentId, List<Def_Department> result)
        {
            var children = allDepartments.Where(d => d.ParentId == parentId).ToList();
            foreach (var child in children)
            {
                result.Add(child);
                GetChildDepartments(allDepartments, child.Id, result);
            }
        }
    }
}
