using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 应用参数值仓储实现
    /// </summary>
    public class AppParamValueRepository : BaseRepository<Def_App_Param_Value>, IAppParamValueRepository
    {
        private readonly ISqlSugarClient _db;

        public AppParamValueRepository(ISqlSugarClient db) : base(db)
        {
            _db = db;
        }

        /// <summary>
        /// 获取应用的所有参数值
        /// </summary>
        public async Task<List<Def_App_Param_Value>> GetAppParamValuesAsync(long appId)
        {
            return await _db.Queryable<Def_App_Param_Value>()
                .Where(p => p.AppId == appId && p.IsActive)
                .ToListAsync();
        }

        /// <summary>
        /// 根据参数键获取参数值
        /// </summary>
        public async Task<Def_App_Param_Value> GetParamValueByKeyAsync(long appId, string paramKey)
        {
            return await _db.Queryable<Def_App_Param_Value>()
                .Where(p => p.AppId == appId && p.ParamKey == paramKey && p.IsActive)
                .FirstAsync();
        }
    }
}
