using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 用户标签仓储实现
    /// </summary>
    public class UserTagRepository : BaseRepository<Def_User_Tag>, IUserTagRepository, IScopedService
    {
        private readonly IBaseRepository<Def_User_Tag_Relation> _userTagRelationRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="userTagRelationRepository">用户标签关联仓储</param>
        public UserTagRepository(
            ISqlSugarClient context,
            IBaseRepository<Def_User_Tag_Relation> userTagRelationRepository) : base(context)
        {
            _userTagRelationRepository = userTagRelationRepository;
        }

        /// <summary>
        /// 获取用户的所有标签ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>标签ID列表</returns>
        public async Task<List<long>> GetUserTagIdsAsync(long userId)
        {
            var tagRelations = await _userTagRelationRepository.GetListAsync(r => r.UserId == userId && r.IsActive == true);
            return tagRelations.Select(r => r.TagId).ToList();
        }

        /// <summary>
        /// 获取用户的所有标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>标签列表</returns>
        public async Task<List<Def_User_Tag>> GetUserTagsAsync(long userId)
        {
            var tagIds = await GetUserTagIdsAsync(userId);
            if (tagIds.Count == 0)
            {
                return new List<Def_User_Tag>();
            }

            return await base.GetListAsync(t => tagIds.Contains(t.Id) && t.IsActive == true);
        }

        /// <summary>
        /// 批量保存用户标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tagIds">标签ID列表</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveUserTagsAsync(long userId, List<long> tagIds, long createBy, string tenantId, string createName)
        {
            try
            {
                BeginTran();
                // 先将该用户的所有标签关联设为无效
                await _userTagRelationRepository.UpdateSetColumnsTrueAsync(
                    r => new Def_User_Tag_Relation { IsActive = false },
                    r => r.UserId == userId && r.IsActive && r.TenantId == tenantId
                );

                // 添加新的标签关联
                List<Def_User_Tag_Relation> relations = tagIds.Select(tagId => new Def_User_Tag_Relation
                {
                    TenantId = tenantId,
                    UserId = userId,
                    TagId = tagId,
                    CreateBy = createBy,
                    CreateName = createName,
                    UpdateBy = createBy,
                    UpdateName = createName
                }).ToList();

                if (relations.Any())
                {
                    await _userTagRelationRepository.InsertRangeAsync(relations);
                }

                CommitTran();
                return true;
            }
            catch
            {
                RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除用户的所有标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteUserTagsAsync(long userId)
        {
            return await _userTagRelationRepository.UpdateSetColumnsTrueAsync(
                r => new Def_User_Tag_Relation { IsActive = false },
                r => r.UserId == userId && r.IsActive
            );
        }

        /// <summary>
        /// 检查标签编码是否已存在
        /// </summary>
        /// <param name="tagCode">标签编码</param>
        /// <param name="excludeId">排除的标签ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsTagCodeExistsAsync(string tagCode, long? excludeId = null)
        {
            if (excludeId.HasValue)
            {
                return await base.IsAnyAsync(t => t.TagCode == tagCode && t.Id != excludeId.Value && t.IsActive);
            }
            else
            {
                return await base.IsAnyAsync(t => t.TagCode == tagCode && t.IsActive);
            }
        }
        
        public async Task<Dictionary<long, List<Def_User_Tag>>> GetUserTagsAsync(List<long> userIds)
        {
            var tagRelations = await _userTagRelationRepository.GetListAsync(r => userIds.Contains(r.UserId) && r.IsActive == true);
            
            if (tagRelations.Count == 0)
            {
                return userIds.ToDictionary(userId => userId, userId => new List<Def_User_Tag>());
            }

            // 获取所有相关的标签ID
            var tagIds = tagRelations.Select(r => r.TagId).Distinct().ToList();

            // 查询所有相关的标签
            var tags = await base.GetListAsync(t => tagIds.Contains(t.Id) && t.IsActive == true);

            // 创建标签ID到标签的映射
            var tagMap = tags.ToDictionary(t => t.Id);

            // 创建用户ID到标签ID列表的映射
            var userTagIdsMap = tagRelations
                .GroupBy(r => r.UserId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(r => r.TagId).ToList()
                );

            // 构建结果字典
            var result = new Dictionary<long, List<Def_User_Tag>>();
            foreach (var userId in userIds)
            {
                if (userTagIdsMap.TryGetValue(userId, out var userTagIds))
                {
                    result[userId] = userTagIds
                        .Where(tagId => tagMap.ContainsKey(tagId))
                        .Select(tagId => tagMap[tagId])
                        .ToList();
                }
                else
                {
                    result[userId] = new List<Def_User_Tag>();
                }
            }

            return result;
        }
    }
}
