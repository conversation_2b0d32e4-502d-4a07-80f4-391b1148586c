using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 角色数据访问实现
    /// </summary>
    public class RoleRepository : BaseRepository<Def_Role>, IRoleRepository, IScopedService
    {
        public RoleRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取部门下的所有角色
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>角色列表</returns>
        public async Task<List<Def_Role>> GetRolesByDepartmentAsync(long departmentId, string tenantId)
        {
            Expression<Func<Def_Role, bool>> filter = x => x.DepartmentId == departmentId && x.TenantId == tenantId && x.IsActive == true;
            return await base.GetListAsync(filter);
        }

        /// <summary>
        /// 检查角色编码是否已存在
        /// </summary>
        /// <param name="roleName">角色编码</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsRoleCodeExistsAsync(string tenantId, string roleName, long? departmentId = null, long? excludeId = null)
        {
            Expression<Func<Def_Role, bool>> filter = Expressionable.Create<Def_Role>()
                .And(x => x.RoleCode == roleName && x.IsActive == true && x.TenantId == tenantId)
                .AndIF(departmentId.HasValue, x => x.DepartmentId == departmentId.Value)
                .AndIF(excludeId.HasValue, x => x.Id != excludeId.Value)
                .ToExpression();

            return await base.IsAnyAsync(filter);
        }

        /// <summary>
        /// 根据角色编码获取角色
        /// </summary>
        /// <param name="roleCodes">角色编码列表</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>角色列表</returns>
        public async Task<List<Def_Role>> GetRolesByCodeAsync(List<string> roleCodes, string tenantId)
        {
            Expression<Func<Def_Role, bool>> filter = x => roleCodes.Contains(x.RoleCode) && x.TenantId == tenantId && x.IsActive == true;
            return await base.GetListAsync(filter);
        }
    }
}
