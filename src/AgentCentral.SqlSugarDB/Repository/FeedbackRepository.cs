using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 反馈仓储实现
    /// </summary>
    public class FeedbackRepository : BaseRepositoryImpl<Def_Feedback>, IFeedbackRepository
    {
        public FeedbackRepository(ISqlSugarClient db) : base(db)
        {
        }

        public async Task<(List<FeedbackModel>, int)> GetFeedbackPageList(Expression<Func<Def_Feedback, Def_User_Info, bool>> where, int pageIndex, int pageSize)
        {
            RefAsync<int> totalNumber = 0;
            var query = db.Queryable<Def_Feedback>()
                .LeftJoin<Def_User_Info>((f, u) => f.CreateBy == u.UserId)
                .Where(where)
                .OrderByDescending((f, u) => f.CreateTime)
                .Select((f, u) => new FeedbackModel
                {
                    Id = f.Id,
                    FeedbackId = f.FeedbackId,
                    Title = SqlFunc.IsNullOrEmpty(f.Title) ? f.Comments : f.Title,
                    IssueType = f.IssueType,
                    Status = f.Status,
                    CreateName = f.CreateName,
                    Company = u.CompanyName,
                    Department = u.DepartmentName,
                    CreateTime = f.CreateTime,
                    Email = u.UserName,
                    FirstName = f.FirstName,
                    LastName = f.LastName,
                    Module = f.Module
                });

            var result = await query.ToPageListAsync(pageIndex, pageSize, totalNumber);
            return (result, totalNumber);
        }
    }
}