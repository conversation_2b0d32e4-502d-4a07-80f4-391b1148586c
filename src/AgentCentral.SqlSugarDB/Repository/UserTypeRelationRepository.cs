using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    public class UserTypeRelationRepository : BaseRepository<Def_User_Type_Relation>, IUserTypeRelationRepository, IScopedService
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        public UserTypeRelationRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取用户的类型
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户类型</returns>
        public async Task<UserTypeEnum?> GetUserTypeAsync(long userId)
        {
            Def_User_Type_Relation relation = await GetFirstAsync(r => r.UserId == userId && r.IsActive == true);
            return relation?.UserType;
        }

        /// <summary>
        /// 保存用户类型
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userType">用户类型</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveUserTypeAsync(long userId, UserTypeEnum userType, long createBy, string createName)
        {
            try
            {
                BeginTran();

                // 先将该用户的所有类型关联设为无效
                await UpdateSetColumnsTrueAsync(
                    r => new Def_User_Type_Relation { IsActive = false },
                    r => r.UserId == userId && r.IsActive
                );

                // 添加新的类型关联
                Def_User_Type_Relation relation = new Def_User_Type_Relation
                {
                    UserId = userId,
                    UserType = userType,
                    CreateBy = createBy,
                    CreateName = createName,
                    UpdateBy = createBy,
                    UpdateName = createName
                };

                bool result = await InsertAsync(relation);
                CommitTran();
                return result;
            }
            catch
            {
                RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 根据类型获取用户列表
        /// </summary>
        /// <param name="userType">用户类型</param>
        /// <returns>用户ID列表</returns>
        public async Task<List<long>> GetUsersByTypeAsync(UserTypeEnum userType)
        {
            List<Def_User_Type_Relation> relations = await GetListAsync(r => r.UserType == userType && r.IsActive == true);
            return relations.Select(r => r.UserId).ToList();
        }

        /// <summary>
        /// 删除用户的类型关联
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteUserTypeAsync(long userId)
        {
            return await UpdateSetColumnsTrueAsync(
                r => new Def_User_Type_Relation { IsActive = false },
                r => r.UserId == userId && r.IsActive
            );
        }
    }
}
