using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 用户角色关联数据访问实现
    /// </summary>
    public class UserRoleRepository : BaseRepository<Def_User_Role>, IUserRoleRepository, IScopedService
    {
        public UserRoleRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取用户的所有角色ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色ID列表</returns>
        public async Task<List<long>> GetUserRoleIdsAsync(long userId)
        {
            Expression<Func<Def_User_Role, bool>> filter = ur =>
                ur.UserId == userId &&
                ur.IsActive == true;

            var userRoles = await base.GetListAsync(filter);
            return userRoles.Select(ur => ur.RoleId).ToList();
        }

        /// <summary>
        /// 获取用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户角色关联列表</returns>
        public async Task<List<Def_User_Role>> GetUserRolesAsync(long userId)
        {
            Expression<Func<Def_User_Role, bool>> filter = ur =>
                ur.UserId == userId &&
                ur.IsActive == true;

            return await base.GetListAsync(filter);
        }

        /// <summary>
        /// 批量保存用户角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleIds">角色ID列表</param>
        /// <param name="departmentId">部门ID</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveUserRolesAsync(long userId, List<long> roleIds, long departmentId, long createBy, string createName)
        {
            try
            {
                // 开启事务
                base.BeginTran();

                // 先删除该用户的所有角色
                Expression<Func<Def_User_Role, bool>> filter = ur =>
                    ur.UserId == userId;
                await base.DeleteAsync(filter);

                // 创建新的用户角色记录
                var userRoles = new List<Def_User_Role>();
                foreach (var roleId in roleIds)
                {
                    userRoles.Add(new Def_User_Role
                    {
                        UserId = userId,
                        RoleId = roleId,
                        DepartmentId = departmentId,
                        CreateBy = createBy,
                        CreateName = createName,
                        UpdateBy = createBy,
                        UpdateName = createName
                    });
                }

                // 批量插入新的用户角色记录
                await base.InsertRangeAsync(userRoles);

                // 提交事务
                base.CommitTran();
                return true;
            }
            catch (Exception)
            {
                // 回滚事务
                base.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 批量保存用户角色，每个角色使用其对应的部门ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleWithDepartments">角色ID及其对应的部门ID字典</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveUserRolesWithDifferentDepartmentsAsync(long userId, Dictionary<long, long> roleWithDepartments, long createBy, string createName)
        {
            try
            {
                // 开启事务
                base.BeginTran();

                // 先删除该用户的所有角色
                Expression<Func<Def_User_Role, bool>> filter = ur =>
                    ur.UserId == userId;
                await base.DeleteAsync(filter);

                // 创建新的用户角色记录
                var userRoles = new List<Def_User_Role>();
                foreach (var roleDept in roleWithDepartments)
                {
                    userRoles.Add(new Def_User_Role
                    {
                        UserId = userId,
                        RoleId = roleDept.Key,               // 角色ID
                        DepartmentId = roleDept.Value,       // 对应的部门ID
                        CreateBy = createBy,
                        CreateName = createName,
                        UpdateBy = createBy,
                        UpdateName = createName
                    });
                }

                // 批量插入新的用户角色记录
                await base.InsertRangeAsync(userRoles);

                // 提交事务
                base.CommitTran();
                return true;
            }
            catch (Exception)
            {
                // 回滚事务
                base.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 批量保存用户角色，每个角色使用其对应的部门ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleWithDepartments">角色ID及其对应的部门ID字典</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveUserRolesWithDifferentDepartmentsAsync(long userId, Dictionary<long, long> roleWithDepartments, long createBy, string createName, string tenantId, List<long>? departmentIds = null)
        {
            try
            {
                // 开启事务
                base.BeginTran();

                // 先删除该用户的所有角色
                Expression<Func<Def_User_Role, bool>> filter = ur =>
                    ur.UserId == userId;
                await base.DeleteAsync(filter);

                // 创建新的用户角色记录
                var userRoles = new List<Def_User_Role>();
                foreach (var roleDept in roleWithDepartments)
                {
                    userRoles.Add(new Def_User_Role
                    {
                        TenantId = tenantId,
                        UserId = userId,
                        RoleId = roleDept.Key,               // 角色ID
                        DepartmentId = roleDept.Value,       // 对应的部门ID
                        CreateBy = createBy,
                        CreateName = createName,
                        UpdateBy = createBy,
                        UpdateName = createName
                    });
                }
                if(departmentIds !=null && departmentIds.Count > 0)
                {
                    foreach (var deparmentId in departmentIds)
                    {
                        userRoles.Add(new Def_User_Role
                        {
                            TenantId = tenantId,
                            UserId = userId,
                            RoleId = 0,                       // 角色ID
                            DepartmentId = deparmentId,       // 对应的部门ID
                            CreateBy = createBy,
                            CreateName = createName,
                            UpdateBy = createBy,
                            UpdateName = createName
                        });
                    }
                }

                // 批量插入新的用户角色记录
                await base.InsertRangeAsync(userRoles);

                // 提交事务
                base.CommitTran();
                return true;
            }
            catch (Exception)
            {
                // 回滚事务
                base.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteUserRolesAsync(long userId, string tenantId)
        {
            Expression<Func<Def_User_Role, bool>> filter = ur =>
                ur.UserId == userId && ur.TenantId == tenantId;

            return await base.DeleteAsync(filter);
        }

        /// <summary>
        /// 检查用户是否拥有指定角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否拥有角色</returns>
        public async Task<bool> HasRoleAsync(long userId, long roleId)
        {
            Expression<Func<Def_User_Role, bool>> filter = ur =>
                ur.UserId == userId &&
                ur.RoleId == roleId &&
                ur.IsActive == true;

            return await base.IsAnyAsync(filter);
        }

        /// <summary>
        /// 获取具有指定角色的所有用户ID
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户ID列表</returns>
        public async Task<List<long>> GetUserIdsByRoleAsync(long roleId)
        {
            Expression<Func<Def_User_Role, bool>> filter = ur =>
                ur.RoleId == roleId &&
                ur.IsActive == true;

            var userRoles = await base.GetListAsync(filter);
            return userRoles.Select(ur => ur.UserId).Distinct().ToList();
        }
    }
}
