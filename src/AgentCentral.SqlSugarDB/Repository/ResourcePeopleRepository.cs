using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository;

public class ResourcePeopleRepository : BaseRepositoryImpl<Doc_Resource_People>, IResourcePeopleRepository
{
    public ResourcePeopleRepository(ISqlSugarClient context) : base(context)
    {
    }

    public async Task<List<ProcessAttachmentModel>> GetPeopleAttachments(CancellationToken cancellationToken)
    {
        return await db.Queryable<Doc_Resource_People>()
            .LeftJoin<Doc_Attachment_Mapping>((p, mapping) =>
                p.Id == mapping.BusinessId && mapping.BusinessType == AttachmentTypeEnum.DocResourcePeople)
            .LeftJoin<Doc_Attachment>((p, mapping, att) => mapping.AttachmentId == att.Id)
            .Where((p, mapping, att) => !SqlFunc.IsNullOrEmpty(att.FileName))
            .Select((p, mapping, att) => new ProcessAttachmentModel
            {
                RealName = att.RealName,
                FileName = att.FileName,
                EmployeeName = p.Name
            })
            .ToListAsync(cancellationToken);
    }
}