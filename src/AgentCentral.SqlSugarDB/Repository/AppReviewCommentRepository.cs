using SqlSugar;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using Microsoft.IdentityModel.Tokens;

namespace AgentCentral.SqlSugarDB.Repository
{
    public class AppReviewCommentRepository : BaseRepositoryImpl<Def_App_Review_Comment>, IAppReviewCommentRepository
    {
        public AppReviewCommentRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取应用评价列表
        /// </summary>
        public async Task<(List<ReviewCommentModel>, int)> GetReviewCommentsAsync(string appId, int? ratingStar, int pageIndex,
            int pageSize, string orderBy, bool isAsc, CancellationToken cancellationToken = default)
        {
            var query = db.Queryable<Def_App_Review_Comment>()
                .LeftJoin<Def_User_Info>((r, u) => r.CreateBy == u.UserId)
                .LeftJoin<Doc_Attachment_Mapping>((r, u, a) =>
                    u.UserId == a.BusinessId && a.BusinessType == AttachmentTypeEnum.UserProfilePicture)
                .LeftJoin<Doc_Attachment>((r, u, a, att) => a.AttachmentId == att.Id)
                .Where(r => r.AppId == appId)
                .WhereIF(ratingStar.HasValue, r => r.RatingStar == ratingStar)
                .OrderByIF(!orderBy.IsNullOrEmpty(), $"{orderBy} {(isAsc ? "asc" : "desc")}")
                .Select((r, u, a, att) => new ReviewCommentModel
                {
                    RatingStar = r.RatingStar,
                    ReviewContent = r.ReviewContent,
                    CreateTime = r.CreateTime,
                    UserName = u.UserName,
                    AttachmentId = a.AttachmentId,
                    FileName = att.FileName
                });

            RefAsync<int> totalCount = 0;
            var list = await query.ToPageListAsync(pageIndex, pageSize, totalCount, cancellationToken);
            return (list, totalCount);
        }

        /// <summary>
        /// 获取应用评价统计信息
        /// </summary>
        public async Task<(double avgRating, int totalCount, Dictionary<int, int> ratingDistribution)>
            GetReviewStatsAsync(string appId, CancellationToken cancellationToken = default)
        {
            var query = db.Queryable<Def_App_Review_Comment>()
                .Where(x => x.AppId == appId);

            // 获取平均评分和总数
            var stats = await query.Select(x => new
            {
                AvgRating = SqlFunc.AggregateAvg(x.RatingStar),
                TotalCount = SqlFunc.AggregateCount(x.Id)
            }).FirstAsync(cancellationToken);

            // 获取评分分布
            var distribution = await query
                .GroupBy(x => x.RatingStar)
                .Select(x => new
                {
                    Rating = x.RatingStar,
                    Count = SqlFunc.AggregateCount(x.Id)
                })
                .ToListAsync(cancellationToken);

            var ratingDistribution = distribution.ToDictionary(x => x.Rating, x => x.Count);

            return (stats.AvgRating, stats.TotalCount, ratingDistribution);
        }
    }
}