using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// Mp_Mcp Repository实现类
    /// </summary>
    public class MpMcpRepository : BaseRepository<Mp_Mcp>, IMpMcpRepository
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">SqlSugar客户端</param>
        public MpMcpRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取Mp_Mcp审核分页列表
        /// </summary>
        /// <param name="expression">查询条件表达式</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="orderBy">排序字段</param>
        /// <param name="isAsc">是否升序</param>
        /// <returns>分页结果</returns>
        public async Task<(List<Mp_Mcp> items, int total)> GetReviewPageListAsync(
            Expression<Func<Mp_Mcp, bool>> expression,
            int pageIndex,
            int pageSize,
            string orderBy,
            bool isAsc)
        {
            // 创建查询条件列表
            List<Expression<Func<Mp_Mcp, bool>>> whereExpressionList = new List<Expression<Func<Mp_Mcp, bool>>>();
            
            // 添加传入的条件
            whereExpressionList.Add(expression);

            // 使用BaseRepository的分页查询方法
            (List<Mp_Mcp> items, int total) = await GetPageListAsync(
                whereExpressionList,
                pageIndex,
                pageSize,
                orderBy,
                isAsc);

            return (items, total);
        }

        /// <summary>
        /// 批量更新Mp_Mcp状态
        /// </summary>
        /// <param name="mcpIds">MCP服务ID列表</param>
        /// <param name="status">目标状态</param>
        /// <param name="updateBy">更新人ID</param>
        /// <param name="updateName">更新人姓名</param>
        /// <returns>更新的记录数</returns>
        public async Task<int> BatchUpdateStatusAsync(
            List<long> mcpIds,
            McpStatusEnum status,
            long updateBy,
            string updateName)
        {
            if (mcpIds == null || !mcpIds.Any())
            {
                return 0;
            }

            // 使用BaseRepository的批量更新方法
            bool result = await UpdateAsync(
                x => new Mp_Mcp
                {
                    Status = status,
                    UpdateBy = updateBy,
                    UpdateName = updateName,
                    UpdateTime = DateTime.Now
                },
                x => mcpIds.Contains(x.Id) && x.IsActive == true);

            // 如果更新成功，返回影响的行数，这里简化为返回mcpIds的数量
            // 实际应用中可能需要更精确的计数
            return result ? mcpIds.Count : 0;
        }

        /// <summary>
        /// 根据MCP服务ID获取Mp_Mcp详情
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <returns>Mp_Mcp实体</returns>
        public async Task<Mp_Mcp> GetByMcpIdAsync(long mcpId)
        {
            // 使用BaseRepository的查询方法
            Mp_Mcp result = await GetFirstAsync(x => x.Id == mcpId);
            return result;
        }

        /// <summary>
        /// 根据DI平台的MCP ID获取Mp_Mcp详情
        /// </summary>
        /// <param name="diMcpId">DI平台的MCP ID</param>
        /// <returns>Mp_Mcp实体</returns>
        public async Task<Mp_Mcp> GetByDiMcpIdAsync(long diMcpId)
        {
            // 使用BaseRepository的查询方法
            Mp_Mcp result = await GetFirstAsync(x => x.McpId == diMcpId);
            return result;
        }

        public async Task<List<string>> ListProvieders(string keyword, int pageNumber = 1, int pageSize = 10)
        {
            bool hasKeyword = !string.IsNullOrEmpty(keyword);
            var sugarQueryable = db.Queryable<Mp_Mcp>()
                .WhereIF(hasKeyword, it => it.Provider.Contains(keyword))
                .Select(it => it.Provider)
                .Distinct();
            return await sugarQueryable.ToPageListAsync(pageNumber, pageSize);
        }
    }
}