using SqlSugar;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using System.Linq.Expressions;
using AgentCentral.Domain.Entities.Workflow;

namespace AgentCentral.SqlSugarDB.Repository
{
    public class AppSubscribeRepository : BaseRepositoryImpl<Def_App_Subscribe>, IAppSubscribeRepository
    {
        public AppSubscribeRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取应用订阅用户列表
        /// </summary>
        public async Task<(List<SubscribeUsersModel>, int)> GetSubscribeUsersAsync(string appId, int pageIndex,
            int pageSize, CancellationToken cancellationToken = default)
        {
            var query = db.Queryable<Def_App_Subscribe>()
                .LeftJoin<Def_User_Info>((s, u) => s.CreateBy == u.UserId)
                .LeftJoin<Doc_Attachment_Mapping>((s, u, mapping) =>
                    u.UserId == mapping.BusinessId && mapping.BusinessType == AttachmentTypeEnum.UserProfilePicture)
                .LeftJoin<Doc_Attachment>((s, u, mapping, att) => mapping.AttachmentId == att.Id)
                .Where(s => s.AppId == appId)
                .OrderByDescending(s => s.CreateTime)
                .Select((s, u, mapping, att) => new SubscribeUsersModel
                {
                    UserName = u.UpdateName,
                    AttachmentId = mapping.AttachmentId,
                    CreateTime = s.CreateTime,
                    FileName = att.FileName
                });

            RefAsync<int> totalCount = 0;
            var list = await query.ToPageListAsync(pageIndex, pageSize, totalCount, cancellationToken);
            return (list, totalCount);
        }

        /// <summary>
        /// 获取应用订阅总数
        /// </summary>
        public async Task<int> GetSubscribeCountAsync(string appId, CancellationToken cancellationToken = default)
        {
            return await db.Queryable<Def_App_Subscribe>()
                .Where(x => x.AppId == appId)
                .CountAsync(cancellationToken);
        }

        public async Task<(List<AppListModel> list, int total)> GetUserSubscribedAppsAsync(
            Expression<Func<Def_App_Subscribe, Def_App, bool>> filterExpression,
            int pageIndex,
            int pageSize,
            string orderByField,
            bool isAsc,
            IList<long> tagIds,
            CancellationToken cancellationToken = default)
        {
            var query = db.Queryable<Def_App_Subscribe>()
                    .InnerJoin<Def_App>((s, a) => s.AppId == a.AppId)
                    .LeftJoin<Def_User_Info>((s, a, u) => a.CreateBy == u.UserId)
                    .Where(filterExpression)
                    .WhereIF(tagIds != null && tagIds.Count > 0, (x, a) => SqlFunc.Subqueryable<Def_Tag_Mapping>().Where(m => m.ResourceId == a.Id && m.IsActive && tagIds.Contains(m.TagId)).Any())
                    .Select((s, a, u) => new AppListModel
                    {
                        Id = a.Id,
                        TenantId = a.TenantId,
                        IsActive = a.IsActive,
                        CreateBy = a.CreateBy,
                        CreateName = u.UserType == UserTypeEnum.Central ? "item.com" : a.CreateName,
                        CreateTime = a.CreateTime,
                        UpdateBy = a.UpdateBy,
                        UpdateName = a.UpdateName,
                        UpdateTime = a.UpdateTime,
                        AppId = a.AppId,
                        AppCode = a.AppCode,
                        AppName = a.AppName,
                        AppDescription = a.AppDescription,
                        AppMode = a.AppMode,
                        AppIcon = a.AppIcon,
                        EnableSite = a.EnableSite,
                        EnableApi = a.EnableApi,
                        AppTag = a.AppTag,
                        Department = a.Department,
                        AppStatus = a.AppStatus,
                        AppSource = a.AppSource,
                        RatingStar = SqlFunc.Subqueryable<Def_App_Review_Comment>().Where(arc => arc.AppId == a.AppId && arc.IsActive)
                            .Avg(arc => arc.RatingStar),
                        TotalStar = SqlFunc.Subqueryable<Def_App_Review_Comment>().Where(arc => arc.AppId == a.AppId && arc.IsActive)
                            .Sum(arc => arc.RatingStar),
                        Tags = SqlFunc.Subqueryable<Def_Tag_Mapping>().InnerJoin<Def_Tag>((m, t) => m.TagId == t.Id && m.IsActive && t.IsActive)
                            .Where((m, t) => m.ResourceId == a.Id)
                            .SelectStringJoin((m, t) => t.TagName, ","),
                        RejectReason = SqlFunc.Subqueryable<WorkflowLog>()
                            .Where(arc => arc.BusinessId == a.Id && (arc.ApprovalStatus == (int)AppStatusEnum.Rejected || arc.ApprovalStatus == (int)AppStatusEnum.AutoRejected))
                            .OrderByDesc(arc => arc.ApprovalDate)
                            .Select(arc => arc.Description),
                        CheckUpdate = SqlFunc.Subqueryable<Def_App>()
                            .Where(x => x.MainAppId == a.MainAppId
                                && x.IsActive
                                && (x.AppStatus == AppStatusEnum.Published || x.AppStatus == AppStatusEnum.AutoPublished)
                                && x.VersionNumber > a.VersionNumber)
                            .Any(),
                        CurrentAppId = SqlFunc.Subqueryable<Def_App>()
                            .Where(x => x.MainAppId == a.MainAppId
                                && x.IsActive
                                && x.IsCurrentVersion)
                            .Select(x => x.AppId)
                    })
                    .OrderByIF(!string.IsNullOrEmpty(orderByField) && orderByField.Equals("ratingstar", StringComparison.CurrentCultureIgnoreCase), $"{orderByField} {(isAsc ? "asc" : "desc")},TotalStar desc")
                    .OrderByIF(!string.IsNullOrEmpty(orderByField) && !orderByField.Equals("ratingstar", StringComparison.CurrentCultureIgnoreCase), $"{orderByField} {(isAsc ? "asc" : "desc")}")
                    .OrderByPropertyName("AppId", OrderByType.Asc);

            RefAsync<int> totalCount = 0;
            var list = await query.ToPageListAsync(pageIndex, pageSize, totalCount, cancellationToken);
            return (list, totalCount);
        }
    }
}