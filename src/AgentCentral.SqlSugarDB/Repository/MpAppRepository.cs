using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// Mp_App Repository实现类
    /// </summary>
    public class MpAppRepository : BaseRepository<Mp_App>, IMpAppRepository
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">SqlSugar客户端</param>
        public MpAppRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取Mp_App审核分页列表
        /// </summary>
        /// <param name="expression">查询条件表达式</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="orderBy">排序字段</param>
        /// <param name="isAsc">是否升序</param>
        /// <returns>分页结果</returns>
        public async Task<(List<Mp_App> items, int total)> GetReviewPageListAsync(
            Expression<Func<Mp_App, bool>> expression,
            int pageIndex,
            int pageSize,
            string orderBy,
            bool isAsc)
        {
            // 创建查询条件列表
            List<Expression<Func<Mp_App, bool>>> whereExpressionList = new List<Expression<Func<Mp_App, bool>>>();
            
            // 添加传入的条件
            if (expression != null)
            {
                whereExpressionList.Add(expression);
            }
            
            // 添加默认的IsActive条件
            whereExpressionList.Add(x => x.IsActive == true);

            // 使用BaseRepository的分页查询方法
            (List<Mp_App> items, int total) = await GetPageListAsync(
                whereExpressionList,
                pageIndex,
                pageSize,
                orderBy,
                isAsc);

            return (items, total);
        }

        /// <summary>
        /// 批量更新Mp_App状态
        /// </summary>
        /// <param name="appIds">应用ID列表</param>
        /// <param name="status">目标状态</param>
        /// <param name="updateBy">更新人ID</param>
        /// <param name="updateName">更新人姓名</param>
        /// <returns>更新的记录数</returns>
        public async Task<int> BatchUpdateStatusAsync(
            List<long> appIds,
            MpAppStatusEnum status,
            long updateBy,
            string updateName)
        {
            if (appIds == null || !appIds.Any())
            {
                return 0;
            }

            // 使用BaseRepository的批量更新方法
            bool result = await UpdateAsync(
                x => new Mp_App
                {
                    AppStatus = status,
                    UpdateBy = updateBy,
                    UpdateName = updateName,
                    UpdateTime = DateTime.Now
                },
                x => appIds.Contains(x.Id) && x.IsActive == true);

            // 如果更新成功，返回影响的行数，这里简化为返回appIds的数量
            // 实际应用中可能需要更精确的计数
            return result ? appIds.Count : 0;
        }

        /// <summary>
        /// 根据应用ID获取Mp_App详情
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>Mp_App实体</returns>
        public async Task<Mp_App> GetByAppIdAsync(long appId)
        {
            // 使用BaseRepository的查询方法
            Mp_App result = await GetFirstAsync(x => x.Id == appId && x.IsActive == true);
            return result;
        }
    }
} 