using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using SqlSugar;

namespace AgentCentral.SqlSugarDB.Repository
{
    /// <summary>
    /// 应用参数定义仓储实现
    /// </summary>
    public class AppParamRepository : BaseRepository<Def_App_Param>, IAppParamRepository
    {
        private readonly ISqlSugarClient _db;

        public AppParamRepository(ISqlSugarClient db) : base(db)
        {
            _db = db;
        }

        /// <summary>
        /// 获取应用的所有参数定义
        /// </summary>
        public async Task<List<Def_App_Param>> GetAppParamsAsync(long appId)
        {
            return await _db.Queryable<Def_App_Param>()
                .Where(p => p.AppId == appId && p.IsActive)
                .OrderBy(p => p.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 根据参数键获取参数定义
        /// </summary>
        public async Task<Def_App_Param> GetParamByKeyAsync(long appId, string paramKey)
        {
            return await _db.Queryable<Def_App_Param>()
                .Where(p => p.AppId == appId && p.ParamKey == paramKey && p.IsActive)
                .FirstAsync();
        }
    }
}
