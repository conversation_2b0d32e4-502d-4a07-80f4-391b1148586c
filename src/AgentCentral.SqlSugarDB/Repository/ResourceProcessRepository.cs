using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using SqlSugar;
using System.Linq.Expressions;

namespace AgentCentral.SqlSugarDB.Repository;

public class ResourceProcessRepository : BaseRepositoryImpl<Doc_Resource_Process>, IResourceProcessRepository
{
    public ResourceProcessRepository(ISqlSugarClient context) : base(context)
    {
    }

    public async Task<List<ProcessAttachmentModel>> GetProcessAttachments(CancellationToken cancellationToken)
    {
        return await db.Queryable<Doc_Resource_Process>()
            .LeftJoin<Doc_Attachment_Mapping>((p, mapping) =>
                p.Id == mapping.BusinessId && mapping.BusinessType == AttachmentTypeEnum.DocResourceProcess)
            .LeftJoin<Doc_Attachment>((p, mapping, att) => mapping.AttachmentId == att.Id)
            .LeftJoin<Doc_Resource_People>((p, mapping, att, rp) => p.EmployeeId == rp.EmployeeId)
            .Where((p, mapping, att, rp) => !SqlFunc.IsNullOrEmpty(att.FileName))
            .Select((p, mapping, att, rp) => new ProcessAttachmentModel
            {
                ProcessEnvironment = p.ProcessEnvironment,
                RealName = att.RealName,
                FileName = att.FileName,
                EmployeeName = rp.Name
            })
            .ToListAsync(cancellationToken);
    }

    public async Task<(List<ResourceProcessListModel>, int)> GetResourcePageListAsync(Expression<Func<Doc_Resource_Process, Doc_Resource_People, bool>> where, int pageSize, int pageIndex, string orderBy, bool isAsc)
    {
        RefAsync<int> totalCount = 0;
        var query = db.Queryable<Doc_Resource_Process>()
            .LeftJoin<Doc_Resource_People>((r, p) => r.EmployeeId == p.EmployeeId)
            .Where(where);
        var orderColumn = typeof(ResourceProcessListModel).GetProperties().FirstOrDefault(p => p.Name.Equals(orderBy, StringComparison.OrdinalIgnoreCase))?.Name;
        if (orderColumn == null)
        {
            throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "OrderBy Column is Invalid.");
        }
        var order = isAsc ? OrderByType.Asc : OrderByType.Desc;
        var orderStr = $"{orderColumn} {order}";
        var result = await query.Select((r, p) => new ResourceProcessListModel
        {
            Id = r.Id,
            UserName = r.CreateName,
            ContributorName = p.Name,
            ProcessEnvironment = r.ProcessEnvironment,
            Process = r.Process,
            ProcessName = r.ProcessName,
            Description = r.Description,
            CreateTime = r.CreateTime,
            Customer = r.Customer,
            Retailer = r.Retailer,
            UrlLinks = r.UrlLinks

        }).OrderBy(orderStr).ToPageListAsync(pageIndex, pageSize, totalCount);
        return (result, totalCount);
    }
}