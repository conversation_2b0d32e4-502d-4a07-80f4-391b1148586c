using System.Linq.Expressions;
using SqlSugar;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;

namespace AgentCentral.SqlSugarDB.Repository
{
    public class UserInfoRepository : BaseRepositoryImpl<Def_User_Info>, IUserInfoRepository
    {
        public UserInfoRepository(ISqlSugarClient context) : base(context)
        {
        }

        /// <summary>
        /// 获取用户详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        public async Task<Def_User_Info> GetUserDetailByUserIdAsync(long userId)
        {
            return await base.GetFirstAsync(u => u.UserId == userId);
        }

        /// <summary>
        /// 获取用户信息列表
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="roleIds">角色ID列表，支持多选</param>
        /// <param name="tagIds">标签ID列表，支持多选</param>
        /// <returns>用户信息列表和总数</returns>
        public async Task<(List<Def_User_Info> users, int total)> GetUserInfoListAsync(
            Expression<Func<Def_User_Info, bool>> filter,
            int pageIndex,
            int pageSize,
            List<string> roleIds = null,
            List<long> tagIds = null)
        {
            // 基础查询
            var query = db.Queryable<Def_User_Info>().Where(filter);

            // 筛选角色（多选）
            if (roleIds != null && roleIds.Any())
            {
                // 提取实际的roleId（格式为"x_roleId"中的roleId部分）
                var actualRoleIds = roleIds
                    .Where(r => r.Contains("_"))
                    .Select(r => r.Split('_').Last())
                    .ToList();
                
                // 查询拥有指定角色之一的用户ID
                var userIdsWithRoles = await db.Queryable<Def_User_Role>()
                    .Where(r => actualRoleIds.Contains(r.RoleId.ToString()) && r.IsActive == true)
                    .Select(r => r.UserId)
                    .Distinct()
                    .ToListAsync();

                // 只返回拥有指定角色之一的用户
                query = query.Where(u => userIdsWithRoles.Contains(u.Id));
            }

            // 筛选标签（多选）
            if (tagIds != null && tagIds.Any())
            {
                // 查询拥有指定标签之一的用户ID
                var userIdsWithTags = await db.Queryable<Def_User_Tag_Relation>()
                    .Where(t => tagIds.Contains(t.TagId) && t.IsActive == true)
                    .Select(t => t.UserId)
                    .Distinct()
                    .ToListAsync();

                // 只返回拥有指定标签之一的用户
                query = query.Where(u => userIdsWithTags.Contains(u.Id));
            }

            RefAsync<int> totalCount = 0;
            var users = await query
                .OrderByDescending(u => u.CreateTime)
                .ToPageListAsync(pageIndex, pageSize, totalCount);

            return (users, totalCount);
        }

        /// <summary>
        /// 获取用户详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        public async Task<Def_User_Info> GetUserDetailAsync(long userId)
        {
            return await base.GetFirstAsync(u => u.Id == userId);
        }

        /// <summary>
        /// 更新用户状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateUserStatusAsync(long userId, bool isActive)
        {
            var user = await base.GetFirstAsync(u => u.Id == userId);
            if (user == null)
            {
                return false;
            }

            user.IsActive = isActive;
            return await base.UpdateAsync(user);
        }

        /// <summary>
        /// 检查用户名是否已存在
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="excludeUserId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsUserNameExistsAsync(string userName, long? excludeUserId = null)
        {
            Expression<Func<Def_User_Info, bool>> filter = Expressionable.Create<Def_User_Info>()
                .And(u => u.UserName == userName && u.IsActive == true)
                .AndIF(excludeUserId.HasValue, u => u.Id != excludeUserId.Value)
                .ToExpression();

            return await base.IsAnyAsync(filter);
        }

        /// <summary>
        /// 检查邮箱是否已存在
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="excludeUserId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsEmailExistsAsync(string email, long? excludeUserId = null)
        {
            Expression<Func<Def_User_Info, bool>> filter = Expressionable.Create<Def_User_Info>()
                .And(u => u.Email == email && u.IsActive == true)
                .AndIF(excludeUserId.HasValue, u => u.Id != excludeUserId.Value)
                .ToExpression();

            return await base.IsAnyAsync(filter);
        }
    }
}