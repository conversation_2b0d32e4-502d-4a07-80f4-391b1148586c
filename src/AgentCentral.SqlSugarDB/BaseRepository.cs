using System.Linq.Expressions;
using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.IRepository;
using SqlSugar;

namespace AgentCentral.SqlSugarDB
{
    /// <summary>
    /// 数据仓储公共接口实现类，IBaseRepository<T>：继承数据仓储公共接口）
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseRepository<T> : IBaseRepository<T> where T : class, new()
    {
        public ISqlSugarClient db;

        public BaseRepository(ISqlSugarClient context)
        {
            db = context;
        }

        #region 新增

        /// <summary>
        /// 插入数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Insert(T insertObj)
        {
            return db.Insertable(insertObj).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 插入或更新数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool InsertOrUpdate(T data)
        {
            return db.Storageable(data).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 插入或更新数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        public bool InsertOrUpdate(List<T> datas)
        {
            return db.Storageable(datas).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 插入数据并返回自增id
        /// </summary>
        /// <returns>返回值</returns>
        public int InsertReturnIdentity(T insertObj)
        {
            return db.Insertable(insertObj).ExecuteReturnIdentity();
        }

        /// <summary>
        /// 插入数据并返回bigInt自增id
        /// </summary>
        /// <returns>返回值</returns>
        public long InsertReturnBigIdentity(T insertObj)
        {
            return db.Insertable(insertObj).ExecuteReturnBigIdentity();
        }

        /// <summary>
        /// 插入数据并返回雪花id
        /// </summary>
        /// <returns>返回值</returns>
        public long InsertReturnSnowflakeId(T insertObj)
        {
            return db.Insertable(insertObj).ExecuteReturnSnowflakeId();
        }

        /// <summary>
        /// 插入数据列表并返回雪花id列表
        /// </summary>
        /// <returns>返回值</returns>
        public List<long> InsertReturnSnowflakeId(List<T> insertObjs)
        {
            return db.Insertable(insertObjs).ExecuteReturnSnowflakeIdList();
        }

        /// <summary>
        /// 插入数据并返回实体
        /// </summary>
        /// <returns>返回值</returns>
        public T InsertReturnEntity(T insertObj)
        {
            return db.Insertable(insertObj).ExecuteReturnEntity();
        }

        /// <summary>
        /// 插入数据数组
        /// </summary>
        /// <returns>是否成功</returns>
        public bool InsertRange(T[] insertObjs)
        {
            return db.Insertable(insertObjs).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 插入数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        public bool InsertRange(List<T> insertObjs)
        {
            return db.Insertable(insertObjs).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 插入数据并返回雪花id（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<long> InsertReturnSnowflakeIdAsync(T insertObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObj).ExecuteReturnSnowflakeIdAsync();
        }

        /// <summary>
        /// 插入数据并返回SnowflakeId（异步）
        /// </summary>
        /// <returns>SnowflakeId列表</returns>
        public async Task<List<long>> InsertReturnSnowflakeIdAsync(List<T> insertObjs,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObjs).ExecuteReturnSnowflakeIdListAsync();
        }

        /// <summary>
        /// 插入或更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertOrUpdateAsync(T data, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Storageable(data).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量插入或更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertOrUpdateAsync(List<T> datas, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Storageable(datas).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 插入数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertAsync(T insertObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObj).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 插入数据并返回Identity（异步）
        /// </summary>
        /// <returns>Identity</returns>
        public async Task<int> InsertReturnIdentityAsync(T insertObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObj).ExecuteReturnIdentityAsync();
        }

        /// <summary>
        /// 插入数据并返回BigIdentity（异步）
        /// </summary>
        /// <returns>BigIdentity</returns>
        public async Task<long> InsertReturnBigIdentityAsync(T insertObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObj).ExecuteReturnBigIdentityAsync();
        }

        /// <summary>
        /// 插入数据并返回实体（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<T> InsertReturnEntityAsync(T insertObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObj).ExecuteReturnEntityAsync();
        }

        /// <summary>
        /// 批量插入数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertRangeAsync(T[] insertObjs, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObjs).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量插入数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertRangeAsync(List<T> insertObjs, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Insertable(insertObjs).ExecuteCommandAsync() > 0;
        }

        #endregion 新增

        #region 修改

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Update(T updateObj)
        {
            return db.Updateable(updateObj).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 更新数据数组
        /// </summary>
        /// <returns>是否成功</returns>
        public bool UpdateRange(T[] updateObjs)
        {
            return db.Updateable(updateObjs).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 更新数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        public bool UpdateRange(List<T> updateObjs)
        {
            return db.Updateable(updateObjs).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 根据条件更新数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Update(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression)
        {
            return db.Updateable<T>().SetColumns(columns).Where(whereExpression).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 根据条件更新数据并设置列为true
        /// </summary>
        /// <returns>是否成功</returns>
        public bool UpdateSetColumnsTrue(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression)
        {
            return db.Updateable<T>().SetColumns(columns, appendColumnsByDataFilter: true).Where(whereExpression)
                .ExecuteCommand() > 0;
        }

        /// <summary>
        /// 更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAsync(T updateObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Updateable(updateObj).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateRangeAsync(T[] updateObjs, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Updateable(updateObjs).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateRangeAsync(List<T> updateObjs, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Updateable(updateObjs).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 根据条件更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAsync(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Updateable<T>().SetColumns(columns).Where(whereExpression).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 根据条件更新数据并设置列为True（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateSetColumnsTrueAsync(Expression<Func<T, T>> columns,
            Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Updateable<T>().SetColumns(columns, appendColumnsByDataFilter: true).Where(whereExpression)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量更新某些字段
        /// </summary>
        /// <param name="updateObjs">更新实体</param>
        /// <param name="columns">更新的列名</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<bool> UpdateColumnsRangeAsync(List<T> updateObjs, Expression<Func<T, object>> columns,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Updateable(updateObjs).UpdateColumns(columns).ExecuteCommandAsync() > 0;
        }

        #endregion 修改

        #region 查询

        /// <summary>
        /// 根据id获取数据
        /// </summary>
        /// <returns>返回值</returns>
        public T GetById(object id)
        {
            return db.Queryable<T>().InSingle(id);
        }

        /// <summary>
        /// 得到所有数据
        /// </summary>
        /// <returns>返回值</returns>
        public List<T> GetList()
        {
            return db.Queryable<T>().ToList();
        }

        /// <summary>
        /// 根据条件获取数据列表
        /// </summary>
        /// <returns>返回值</returns>
        public List<T> GetList(Expression<Func<T, bool>> whereExpression)
        {
            return db.Queryable<T>().Where(whereExpression).ToList();
        }

        /// <summary>
        /// 根据条件获取单个数据
        /// </summary>
        /// <returns>返回值</returns>
        public T GetSingle(Expression<Func<T, bool>> whereExpression)
        {
            return db.Queryable<T>().Single(whereExpression);
        }

        /// <summary>
        /// 根据条件获取第一个数据
        /// </summary>
        /// <returns>返回值</returns>
        public T GetFirst(Expression<Func<T, bool>> whereExpression)
        {
            return db.Queryable<T>().First(whereExpression);
        }

        /// <summary>
        /// 根据id获取数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<T> GetByIdAsync(object id, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().InSingleAsync(id);
        }

        /// <summary>
        /// 获取所有数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<List<T>> GetListAsync(CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().ToListAsync();
        }

        /// <summary>
        /// 根据条件获取数据列表（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().Where(whereExpression).ToListAsync();
        }

        /// <summary>
        /// 根据条件获取排序的数据列表（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression, string orderPropertyName,
            bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null)
        {
            OrderByType orderByType = isAsc ? OrderByType.Asc : OrderByType.Desc;
            ISugarQueryable<T> query = db.Queryable<T>();
            if (selectedColumnExpression != null)
            {
                query.Select(selectedColumnExpression);
            }

            if (!string.IsNullOrEmpty(orderPropertyName))
            {
                query = query.OrderByPropertyName(orderPropertyName, orderByType);
            }

            query = query.Where(whereExpression);
            return await query.ToListAsync();
        }

        /// <summary>
        /// 根据条件获取单个数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<T> GetSingleAsync(Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().SingleAsync(whereExpression);
        }

        /// <summary>
        /// 根据条件获取第一个数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        public async Task<T> GetFirstAsync(Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().FirstAsync(whereExpression);
        }

        /// <summary>
        /// 根据排序条件和查询条件获取第一个数据（异步）
        /// </summary>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="order">排序：desc为倒序，其他为正序</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<T> GetFirstAsync(Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, object>> orderByExpression, string order, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            var query = db.Queryable<T>();
            if (orderByExpression != null)
                query = order == "desc" ? query.OrderByDescending(orderByExpression) : query.OrderBy(orderByExpression);
            return await query.FirstAsync(whereExpression);
        }

        /// <summary>
        /// 根据条件记录某列的和
        /// </summary>
        /// <typeparam name="TResult">返回值类型</typeparam>
        /// <param name="whereExpression">条件</param>
        /// <param name="sumByExpression">计算和的列</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<TResult> GetSumAsync<TResult>(Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> sumByExpression, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            var query = db.Queryable<T>();
            if (whereExpression != null)
                query = query.Where(whereExpression);
            return await query.SumAsync(sumByExpression);
        }

        /// <summary>
        /// 分组求和
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="whereExpression"></param>
        /// <param name="groupExpression"></param>
        /// <param name="selectExpression"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<List<TResult>> GetSumByGroupAsync<TResult>(Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, object>> groupExpression, Expression<Func<T, TResult>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            var query = db.Queryable<T>();
            if (whereExpression != null)
                query = query.Where(whereExpression);
            query = query.GroupByIF(groupExpression != null, groupExpression);
            return await query.Select(selectExpression).ToListAsync();
        }

        /// <summary>
        /// 根据条件查询某个字段最小值
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="whereExpression"></param>
        /// <param name="selectExpression"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<TResult> GetMinAsync<TResult>(Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> selectExpression, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            var query = db.Queryable<T>();
            if (whereExpression != null)
                query = query.Where(whereExpression);
            return await query.MinAsync(selectExpression);
        }

        /// <summary>
        /// 根据条件查询某个字段最大值
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="whereExpression"></param>
        /// <param name="selectExpression"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<TResult> GetMaxAsync<TResult>(Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> selectExpression, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            var query = db.Queryable<T>();
            if (whereExpression != null)
                query = query.Where(whereExpression);
            return await query.MaxAsync(selectExpression);
        }

        #endregion 查询

        #region 分页查询

        /// <summary>
        /// 分页, 可排序
        /// </summary>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="TotalCount">总条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <returns>结果列表</returns>
        public List<T> GetPageList(Expression<Func<T, bool>> whereExpression, int pageIndex, int pageSize,
            out int TotalCount, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true,
            Expression<Func<T, T>> selectedColumnExpression = null)
        {
            return GetPageList(new List<Expression<Func<T, bool>>>() { whereExpression }, pageIndex, pageSize,
                out TotalCount, orderByExpression, isAsc, selectedColumnExpression);
        }

        /// <summary>
        /// 分页(异步), 可排序,可取消操作
        /// </summary>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <param name="cancellationToken">可取消操作</param>
        /// <returns>(结果列表, 总条数)</returns>
        public async Task<(List<T>, int)> GetPageListAsync(Expression<Func<T, bool>> whereExpression, int pageIndex,
            int pageSize, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true,
            Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default)
        {
            return await GetPageListAsync(new List<Expression<Func<T, bool>>>() { whereExpression }, pageIndex,
                pageSize, orderByExpression, isAsc, selectedColumnExpression, cancellationToken);
        }

        public async Task<(List<T>, int)> GetPageListAsync(Expression<Func<T, bool>> whereExpression, int pageIndex,
            int pageSize, string orderPropertyName, bool isAsc = true,
            Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default)
        {
            return await GetPageListAsync(new List<Expression<Func<T, bool>>>() { whereExpression }, pageIndex,
                pageSize, orderPropertyName, isAsc, selectedColumnExpression, cancellationToken);
        }

        /// <summary>
        /// 分页, 可排序
        /// </summary>
        /// <param name="whereExpressionList">多条件动态联合查询（适合有多个可选查询项的查询场景）</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="TotalCount">总条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <returns>结果列表</returns>
        public List<T> GetPageList(List<Expression<Func<T, bool>>> whereExpressionList, int pageIndex, int pageSize,
            out int TotalCount, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true,
            Expression<Func<T, T>> selectedColumnExpression = null)
        {
            PageModel page = new PageModel() { PageIndex = pageIndex, PageSize = pageSize };
            OrderByType orderByType = isAsc ? OrderByType.Asc : OrderByType.Desc;

            int totalNumber = 0;
            ISugarQueryable<T> query = db.Queryable<T>();
            if (selectedColumnExpression != null)
            {
                query.Select(selectedColumnExpression);
            }

            query = query.OrderByIF(orderByExpression != null, orderByExpression, orderByType);
            foreach (var item in whereExpressionList)
            {
                if (item != null)
                {
                    query = query.Where(item);
                }
            }

            List<T> results = query.ToPageList(page.PageIndex, page.PageSize, ref totalNumber);
            TotalCount = totalNumber;
            return results;
        }

        /// <summary>
        /// 分页(异步), 可排序,可取消操作
        /// </summary>
        /// <param name="whereExpressionList">多条件动态联合查询（适合有多个可选查询项的查询场景）</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <param name="cancellationToken">可取消操作</param>
        /// <returns>(结果列表, 总条数)</returns>
        public async Task<(List<T>, int)> GetPageListAsync(List<Expression<Func<T, bool>>> whereExpressionList,
            int pageIndex, int pageSize, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true,
            Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;

            PageModel page = new PageModel() { PageIndex = pageIndex, PageSize = pageSize };
            OrderByType orderByType = isAsc ? OrderByType.Asc : OrderByType.Desc;

            RefAsync<int> count = 0;
            ISugarQueryable<T> query = db.Queryable<T>();
            if (selectedColumnExpression != null)
            {
                query.Select(selectedColumnExpression);
            }

            query = query.OrderByIF(orderByExpression != null, orderByExpression, orderByType);
            foreach (var item in whereExpressionList)
            {
                if (item != null)
                {
                    query = query.Where(item);
                }
            }

            List<T> results = await query.ToPageListAsync(page.PageIndex, page.PageSize, count);
            page.TotalCount = count;
            return (results, page.TotalCount);
        }

        public async Task<(List<T>, int)> GetPageListAsync(List<Expression<Func<T, bool>>> whereExpressionList,
            int pageIndex, int pageSize, string orderPropertyName, bool isAsc = true,
            Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;

            PageModel page = new PageModel() { PageIndex = pageIndex, PageSize = pageSize };
            OrderByType orderByType = isAsc ? OrderByType.Asc : OrderByType.Desc;

            RefAsync<int> count = 0;
            ISugarQueryable<T> query = db.Queryable<T>();
            if (selectedColumnExpression != null)
            {
                query.Select(selectedColumnExpression);
            }

            if (!string.IsNullOrEmpty(orderPropertyName))
            {
                query = query.OrderByPropertyName(orderPropertyName, orderByType);
            }

            foreach (var item in whereExpressionList)
            {
                if (item != null)
                {
                    query = query.Where(item);
                }
            }

            List<T> results = await query.ToPageListAsync(page.PageIndex, page.PageSize, count);
            page.TotalCount = count;
            return (results, page.TotalCount);
        }

        #endregion 分页查询

        #region 删除

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Delete(T deleteObj)
        {
            return db.Deleteable<T>().Where(deleteObj).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 删除数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Delete(List<T> deleteObjs)
        {
            return db.Deleteable<T>().Where(deleteObjs).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 根据条件删除数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Delete(Expression<Func<T, bool>> whereExpression)
        {
            return db.Deleteable<T>().Where(whereExpression).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 根据id删除数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool DeleteById(object id)
        {
            return db.Deleteable<T>().In(id).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(T deleteObj, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Deleteable<T>().Where(deleteObj).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(List<T> deleteObjs, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Deleteable<T>().Where(deleteObjs).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 根据条件删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Deleteable<T>().Where(whereExpression).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 根据id删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByIdAsync(object id, CancellationToken cancellationToken = default)
        {
            return await new SimpleClient<T>(db).DeleteByIdAsync(id, cancellationToken);
        }

        /// <summary>
        /// 根据id集合删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByIdsAsync(dynamic[] ids, CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Deleteable<T>().In(ids).ExecuteCommandAsync() > 0;
        }

        #endregion 删除

        #region 是否存在

        /// <summary>
        /// 根据条件判断是否存在数据
        /// </summary>
        /// <returns>返回值</returns>
        public bool IsAny(Expression<Func<T, bool>> whereExpression)
        {
            return db.Queryable<T>().Where(whereExpression).Any();
        }

        /// <summary>
        /// 根据条件判断是否存在数据（异步）
        /// </summary>
        /// <returns>是否存在</returns>
        public async Task<bool> IsAnyAsync(Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().Where(whereExpression).AnyAsync();
        }

        #endregion 是否存在

        #region 计算数据量

        /// <summary>
        /// 根据条件计算数据数量
        /// </summary>
        /// <returns>返回值</returns>
        public int Count(Expression<Func<T, bool>> whereExpression)
        {
            return db.Queryable<T>().Where(whereExpression).Count();
        }

        /// <summary>
        /// 根据条件计算数据数量（异步）
        /// </summary>
        /// <returns>数量</returns>
        public async Task<int> CountAsync(Expression<Func<T, bool>> whereExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>().Where(whereExpression).CountAsync();
        }

        #endregion 计算数据量

        #region 事务

        /// <summary>
        /// 开始事务
        /// </summary>
        public void BeginTran()
        {
            db.AsTenant().BeginTran();
        }

        /// <summary>
        /// 提交事务
        /// </summary>
        public void CommitTran()
        {
            db.AsTenant().CommitTran();
        }

        /// <summary>
        /// 回滚事务
        /// </summary>
        public void RollbackTran()
        {
            db.AsTenant().RollbackTran();
        }

        /// <summary>
        /// 执行事务
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>是否执行成功</returns>
        public bool UseTran(Action action)
        {
            try
            {
                // 开始事务
                db.AsTenant().BeginTran();

                // 执行操作
                action();

                // 提交事务
                db.AsTenant().CommitTran();
                return true;
            }
            catch (Exception)
            {
                // 如果操作失败，回滚事务
                db.AsTenant().RollbackTran();

                return false;
            }
        }

        /// <summary>
        /// 执行事务，异步
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>是否执行成功</returns>
        public async Task<bool> UseTranAsync(Func<Task> action)
        {
            try
            {
                // 开始事务
                await db.AsTenant().BeginTranAsync();

                // 执行操作
                await action();

                // 提交事务
                await db.AsTenant().CommitTranAsync();
                return true;
            }
            catch (Exception)
            {
                // 如果操作失败，回滚事务
                await db.AsTenant().RollbackTranAsync();
                //回滚后，需要将事务里发生的异常继续向外抛出
                throw;
            }
            /*
             *  调用方式
                 await UseTranAsync(async () =>
                {
                // 这里是你要在事务中执行的操作
                // 例如：
                // await db.InsertAsync(newEntity);
                // await db.UpdateAsync(existingEntity);
                });
            */
        }

        /// <summary>
        /// 执行事务并获取指定返回值
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>返回值，是否执行成功</returns>
        public (ReturnValueT result, bool success) UseTranReturnValue<ReturnValueT>(Func<ReturnValueT> action)
        {
            bool success = false;
            ReturnValueT backValue = default;
            try
            {
                // 开始事务
                db.AsTenant().BeginTran();

                // 执行操作
                backValue = action();

                // 提交事务
                db.AsTenant().CommitTran();
                success = false;
            }
            catch (Exception)
            {
                // 如果操作失败，回滚事务
                db.AsTenant().RollbackTran();
            }

            // 返回操作的结果和成功标志
            return (backValue, success);
        }

        /// <summary>
        /// 执行事务并获取指定返回值，异步
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>返回值，是否执行成功</returns>
        public async Task<(ReturnValueT result, bool success)> UseTranReturnValueAsync<ReturnValueT>(
            Func<Task<ReturnValueT>> action)
        {
            bool success = false;
            ReturnValueT backValue = default;

            try
            {
                // 开始事务
                await db.AsTenant().BeginTranAsync();

                // 执行操作并获取返回值
                backValue = await action();

                // 提交事务
                await db.AsTenant().CommitTranAsync();

                // 设置成功标志
                success = true;
            }
            catch (Exception e)
            {
                // 如果操作失败，回滚事务
                await db.AsTenant().RollbackTranAsync();
            }

            // 返回操作的结果和成功标志
            return (backValue, success);
        }

        #endregion 事务

        #region 不能这样使用异步事务，会无效

        ////开始事务
        //public async Task BeginTranAsync()
        //{
        //    await db.AsTenant().BeginTranAsync();
        //}
        ////提交事务
        //public async Task CommitTranAsync()
        //{
        //    await db.AsTenant().CommitTranAsync();
        //}
        ////回滚事务
        //public async Task RollbackTranAsync()
        //{
        //    await db.AsTenant().RollbackTranAsync();
        //}

        #endregion 不能这样使用异步事务，会无效

        public void QueryFilterClearAndBackup()
        {
            db.QueryFilter.ClearAndBackup();
        }

        public void QueryFilterClear()
        {
            db.QueryFilter.Clear();
        }

        public void QueryFilterRestore()
        {
            db.QueryFilter.Restore();
        }

        public SugarUnitOfWork CreateContext()
        {
            return db.CreateContext(db.Ado.IsNoTran());
        }

        #region 查询部分列扩展
        /// <summary>
        /// 根据条件获取第一个数据,支持查询部分列
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <returns>返回值</returns>
        public TResult GetFirst<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression)
        {
            return db.Queryable<T>()
                .Where(whereExpression)
                .Select(selectExpression)
                .First();
        }

        /// <summary>
        /// 根据条件和排序获取第一个数据,支持查询部分列
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="orderByExpression">排序字段</param>
        /// <param name="orderByType">排序方式</param>
        /// <returns>返回值</returns>
        public TResult GetFirst<TResult>(
            Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> selectExpression,
            Expression<Func<T, object>> orderByExpression,
            OrderByType orderByType = OrderByType.Asc)
        {
            return db.Queryable<T>()
                .Where(whereExpression)
                .OrderBy(orderByExpression, orderByType)
                .Select(selectExpression)
                .First();
        }

        /// <summary>
        /// 根据条件获取第一个数据,支持查询部分列（异步）
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回值</returns>
        public async Task<TResult> GetFirstAsync<TResult>(
            Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> selectExpression,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>()
                .Where(whereExpression)
                .Select(selectExpression)
                .FirstAsync();
        }

        /// <summary>
        /// 根据条件和排序获取第一个数据,支持查询部分列（异步）
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="orderByExpression">排序字段</param>
        /// <param name="orderByType">排序方式</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回值</returns>
        public async Task<TResult> GetFirstAsync<TResult>(
            Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> selectExpression,
            Expression<Func<T, object>> orderByExpression,
            OrderByType orderByType = OrderByType.Asc,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            return await db.Queryable<T>()
                .Where(whereExpression)
                .OrderBy(orderByExpression, orderByType)
                .Select(selectExpression)
                .FirstAsync();
        }

        /// <summary>
        /// 根据条件和排序获取第一个数据,支持查询部分列（异步）
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="orderByExpression">排序字段</param>
        /// <param name="order">排序方式（desc为倒序，其他为正序）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回值</returns>
        public async Task<TResult> GetFirstAsync<TResult>(
            Expression<Func<T, bool>> whereExpression,
            Expression<Func<T, TResult>> selectExpression,
            Expression<Func<T, object>> orderByExpression,
            string order,
            CancellationToken cancellationToken = default)
        {
            db.Ado.CancellationToken = cancellationToken;
            var query = db.Queryable<T>().Where(whereExpression);

            if (orderByExpression != null)
            {
                query = order == "desc"
                    ? query.OrderByDescending(orderByExpression)
                    : query.OrderBy(orderByExpression);
            }

            return await query.Select(selectExpression).FirstAsync();
        }
        #endregion
    }
}