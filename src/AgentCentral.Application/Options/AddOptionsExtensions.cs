using AgentCentral.Application.Contracts.Options;
using Item.Internal.Auth.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AgentCentral.Application.Options
{
    public static class AddOptionsExtensions
    {
        public static void AddOptions(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            _ = serviceCollection
                .Configure<IamOptions>(configuration.GetSection("IamApis"))
                .Configure<ResourceOptions>(configuration.GetSection("ResourceApis"))
                .Configure<ClockApiOptions>(configuration.GetSection("ClockApis"))
                .Configure<CrmOptions>(configuration.GetSection("CrmApis"))
                .Configure<ResourceOptions>(configuration.GetSection("ResourceApis"))
                .Configure<GlobalConfigOptions>(configuration.GetSection("Global"))
                .Configure<FileConversionOptions>(configuration.GetSection("FileConversionConfig"))
            .Configure<KestrelServerOptions>(configuration.GetSection("Kestrel"))
            .Configure<FormOptions>(configuration.GetSection("FormOptions"))
            .Configure<HrmOptions>(configuration.GetSection("HrmApis"))
            .Configure<DifyOptions>(configuration.GetSection("DifyApis"))
            .Configure<KnowledgeBaseProcessOptions>(configuration.GetSection("KnowledgeBaseProcess"))
            .Configure<JwtOptions>(configuration.GetSection("JWT"))
            .Configure<CentralOptions>(configuration.GetSection("CentralConfig"))
            .Configure<WorkflowOptions>(configuration.GetSection("WorkflowApis"))
            .Configure<AgentCentralOptions>(configuration.GetSection("AgentCentralApis"))
            .Configure<CallLogOptions>(configuration.GetSection("CallLogApiUrls"))
            .Configure<MessageCenterOptions>(configuration.GetSection("MessageCenterApiUrls"));
        }
    }
}