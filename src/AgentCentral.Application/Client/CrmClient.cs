using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.HttpUtil;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace AgentCentral.Application.Client
{
    public class CrmClient
    {
        private readonly HttpClient _client;
        private readonly IOptionsSnapshot<CrmOptions> _crmOptions;
        private readonly UserContext _userContext;
        private readonly IamClient _iamClient;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CrmClient(
            HttpClient client,
            IOptionsSnapshot<CrmOptions> crmOptions,
            UserContext userContext,
            IamClient iamClient, IHttpContextAccessor httpContextAccessor)
        {
            _client = client;
            _crmOptions = crmOptions;
            _userContext = userContext;
            _iamClient = iamClient;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 获取客户分页列表
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="accountName">每页大小</param>
        /// <param name="tags">标签列表</param>
        /// <param name="tenantId">tenantId</param>
        /// <returns></returns>
        public async Task<List<CrmCustomerOrRetailerDto>> GetCustomerPageListAsync(int pageIndex, int pageSize,
            string accountName,
            int tags, string tenantId)
        {

            var result = await _client.GetObjectAsync<CrmResponseDto<List<CrmCustomerOrRetailerDto>>>(
                _crmOptions.Value.ApiUrls.CustomerPage +
                $"?Tags={tags}&PageIndex={pageIndex}&PageSize={pageSize}&accountName={accountName}",
                new Dictionary<string, string>
                {
                    { "Authorization", $"Bearer {await _iamClient.GetOauth2TokenAsync()}" },
                    { "x-tenant-id", tenantId }
                }
            );

            return result?.Data?.Data ?? [];
        }
    }
}