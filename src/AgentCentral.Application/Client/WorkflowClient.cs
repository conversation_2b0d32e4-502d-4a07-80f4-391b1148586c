using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Infrastructure.Exceptions;
using Item.Common.Lib.HttpUtil;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace AgentCentral.Application.Client
{
    public class WorkflowClient
    {
        private readonly HttpClient _client;

        private readonly IOptionsSnapshot<WorkflowOptions> _options;

        public WorkflowClient(HttpClient client,
            IOptionsSnapshot<WorkflowOptions> options)
        {
            _client = client;
            _options = options;
        }

        /// <summary>
        /// 得到token
        /// </summary>
        /// <param name="tenantId">租户id</param>
        /// <returns></returns>
        public async Task<TokenResponseDto> GetProjectToken(string tenantId)
        {
            tenantId = string.IsNullOrEmpty(tenantId) ? _options.Value.DefaultTenantId : tenantId;
            var url = _options.Value.ApiUrls.GetProjectToken;
            var response = await _client.GetObjectAsync<TokenResponseDto>(
                    $"{url}?tenantId={tenantId}&appSecret={_options.Value.AppSecret}&grantType={_options.Value.GrantType}&projectCode={_options.Value.ProjectCode}");
            ExceptionHandling(response);
            return response;
        }

        /// <summary>
        /// 启动流程
        /// </summary>
        /// <param name="request">启动参数</param>
        /// <param name="tenantId">租户id</param>
        /// <returns></returns>
        public async Task<StartResponseDto> Start(StartRequestDto request, string tenantId)
        {
            var url = _options.Value.ApiUrls.Start;
            var token = await GetProjectToken(tenantId);
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            var response = await _client.PostJsonAsync<StartRequestDto, StartResponseDto>($"{url}?accessToken={token.Data.AccessToken}", request, null, settings);
            ExceptionHandling(response);
            return response;
        }

        /// <summary>
        /// 完成(更新)流程
        /// </summary>
        /// <param name="request">更新参数</param>
        /// <param name="tenantId">租户id</param>
        /// <returns></returns>
        public async Task<CompleteResponseDto> Complete(CompleteRequestDto request, string tenantId)
        {
            var url = _options.Value.ApiUrls.Complete;
            var token = await GetProjectToken(tenantId);
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            var response = await _client.PostJsonAsync<CompleteRequestDto, CompleteResponseDto>($"{url}?accessToken={token.Data.AccessToken}", request, null, settings);
            ExceptionHandling(response);
            return response;
        }

        /// <summary>
        /// 如果遇到异常，抛出异常（目的：阻止无效流程继续执行而引发不可控结果）
        /// </summary>
        /// <param name="response">返回值</param>
        /// <exception cref="AgentCentralException">抛出异常</exception>
        private static void ExceptionHandling<T>(BaseResponseDto<T> response)
        {
            if (response == null)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "The response from the server was null.");
            }
            else if (response.Code != 0 && response.Code != 1000)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Please complete the approval flow settings first.");
            }
        }
    }
}
