using System.Net.Http.Headers;
using System.Text;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Contracts.RequestModels.IAM;
using AgentCentral.Application.Contracts.ResponseModels.IAM;
using AgentCentral.Domain.Shared.Const;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.Infrastructure.HttpUtil;
using AgentCentral.Infrastructure.Redis;
using AgentCentral.Infrastructure.Redis.Enum;
using Microsoft.Extensions.Options;

namespace AgentCentral.Application.Client
{
    public class IamClient
    {
        private readonly HttpClient _client;
        private readonly IOptionsSnapshot<IamOptions> _iamOptions;
        private readonly IOptionsSnapshot<CentralOptions> _centralOptions;
        private readonly IRedisService _redisService;

        public IamClient(
            HttpClient client,
            IOptionsSnapshot<IamOptions> hardwareOptions,
            IOptionsSnapshot<CentralOptions> centralOptions,
            IRedisService redisService)
        {
            _client = client;
            _iamOptions = hardwareOptions;
            _centralOptions = centralOptions;
            _redisService = redisService;
        }

        /// <summary>
        /// 使用密码方式获取token
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>Token响应</returns>
        public async Task<IamPasswordTokenResponseDto> GetTokenByPasswordAsync(string username, string password)
        {
            var headers = new Dictionary<string, string>
            {
                {
                    "Content-Type", "application/x-www-form-urlencoded"
                },
                {
                    "Authorization",
                    $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_centralOptions.Value.ClientId}:{_centralOptions.Value.ClientSecret}"))}"
                }
            };

            var formData = new Dictionary<string, string>
            {
                { "grant_type", "password" },
                { "username", username },
                { "password", password }
            };

            var response = await _client.PostFormUrlEncodedAsync<IamPasswordTokenResponseDto>(
                _iamOptions.Value.ApiUrls.Oauth2Token,
                formData,
                headers);

            if (response.IsError)
            {
                throw new AgentCentralException(ErrorCodeEnum.LoginFail, response.ErrorDescription);
            }

            return new IamPasswordTokenResponseDto
            {
                AccessToken = response.AccessToken,
                TokenType = response.TokenType,
                ExpiresIn = response.ExpiresIn,
                RefreshToken = response.RefreshToken
            };
        }

        /// <summary>
        /// 获取服务间认证的token
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetOauth2TokenAsync()
        {
            if (_redisService.KeyExists(RedisDataType.Token, RedisCacheConsts.IamCredential))
            {
                return await _redisService.StringGetAsync(RedisDataType.Token, RedisCacheConsts.IamCredential);
            }

            var headers = new Dictionary<string, string>
            {
                {
                    "Content-Type", "application/x-www-form-urlencoded"
                },
                {
                    "Authorization",
                    $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_iamOptions.Value.ClientIdV}:{_iamOptions.Value.ClientSecretV}"))}"
                }
            };

            var formData = new Dictionary<string, string>
            {
                {
                    "grant_type", "client_credentials"
                },
                {
                    "scope", string.Join(" ", _iamOptions.Value.Scope)
                }
            };

            var result =
                await _client.PostFormUrlEncodedAsync<IamOauth2TokenResponseDto>(_iamOptions.Value.ApiUrls.Oauth2Token,
                    formData, headers);
            await _redisService.StringSetAsync(RedisDataType.Token, RedisCacheConsts.IamCredential, result.AccessToken,
                TimeSpan.FromSeconds(result.ExpiresIn - 10));
            return result.AccessToken;
        }


        public async Task<object> GetTokenAsync(string code, string redirectUri)
        {
            Dictionary<string, string> headers = new()
            {
                {
                    "Content-Type",
                    "application/x-www-form-urlencoded"
                },
                {
                    "Authorization",
                    $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_iamOptions.Value.ClientId}:{_iamOptions.Value.ClientSecret}"))}"
                    //"Authorization", $"Basic MTExMTExOnRlc3QxITI="
                }
            };
            Dictionary<string, string> formData = new()
            {
                {
                    "grant_type",
                    "authorization_code"
                },
                {
                    "code",
                    code
                },
                {
                    "redirect_uri",
                    string.IsNullOrWhiteSpace(redirectUri) ? _iamOptions.Value.RedirectUri : redirectUri
                }
            };
            return await _client.PostFormUrlEncodedAsync<object>(url: _iamOptions.Value.ApiUrls.Token, formData,
                headers);
        }

        public async Task<string> GetJwtsAsync()
        {
            object result = await _client.GetObjectAsync<object>(_iamOptions.Value.ApiUrls.Jwts);
            return result.ToString();
        }

        public async Task<string> GetUserInfoAsync(string token)
        {
            _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            object result = await _client.GetObjectAsync<object>(_iamOptions.Value.ApiUrls.User);
            return result.ToString();
        }

        /// <summary>
        /// 获取租户信息
        /// </summary>
        /// <param name="token">访问令牌</param>
        /// <param name="userId">用户ID</param>
        /// <param name="tenantInfoRequest">租户请求</param>
        /// <returns>租户响应</returns>
        public async Task<TenantInfoResponse> GetTenantInfoAsync(string token, TenantInfoRequest tenantInfoRequest)
        {
            // 设置请求头
            Dictionary<string, string> headers = new()
            {
                { "Content-Type", "application/json" },
                { "Authorization", $"Bearer {token}" }
            };


            // 发送PUT请求
            return await _client.PostJsonAsync<TenantInfoResponse>(
                _iamOptions.Value.ApiUrls.TenantInfo,
                tenantInfoRequest,
                headers
            );
        }

        /// <summary>
        /// 启用用户
        /// </summary>
        /// <param name="token">访问令牌</param>
        /// <param name="userId">用户ID</param>
        /// <returns>基础响应</returns>
        public async Task<BaseResponse> ActivateUserAsync(string token, string userId)
        {
            // 设置请求头
            Dictionary<string, string> headers = new()
            {
                { "Content-Type", "application/json" },
                { "Authorization", $"Bearer {token}" }
            };
            
            // 构建URL，替换路径中的用户ID
            string url = _iamOptions.Value.ApiUrls.ActivateUser.Replace("{id}", userId);
            
            // 发送PUT请求，不需要请求体
            return await _client.PutJsonAsync<BaseResponse>(url, null, headers);
        }

        /// <summary>
        /// 禁用用户
        /// </summary>
        /// <param name="token">访问令牌</param>
        /// <param name="userId">用户ID</param>
        /// <returns>基础响应</returns>
        public async Task<BaseResponse> DeactivateUserAsync(string token, string userId)
        {
            // 设置请求头
            Dictionary<string, string> headers = new()
            {
                { "Content-Type", "application/json" },
                { "Authorization", $"Bearer {token}" }
            };
            
            // 构建URL，替换路径中的用户ID
            string url = _iamOptions.Value.ApiUrls.DeactivateUser.Replace("{id}", userId);
            
            // 发送PUT请求，不需要请求体
            return await _client.PutJsonAsync<BaseResponse>(url, null, headers);
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        /// <param name="token">访问令牌</param>
        /// <param name="userId">用户ID</param>
        /// <param name="request">更新用户请求</param>
        /// <returns>更新用户响应</returns>
        public async Task<UpdateUserResponse> UpdateUserAsync(string token, string userId, UpdateUserRequest request)
        {
            // 设置请求头
            Dictionary<string, string> headers = new()
            {
                { "Content-Type", "application/json" },
                { "Authorization", $"Bearer {token}" }
            };
            
            // 构建URL，替换路径中的用户ID
            string url = _iamOptions.Value.ApiUrls.UpdateUser.Replace("{id}", userId);
            
            // 发送PUT请求
            return await _client.PutJsonAsync<UpdateUserResponse>(
                url,
                request,
                headers
            );
        }
    }
}