using System.Text;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Contracts.RequestModels.Resource;
using AgentCentral.Infrastructure.HttpUtil;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace AgentCentral.Application.Client
{
    public class ResourceClient
    {
        private readonly HttpClient _client;
        private readonly IOptions<ResourceOptions> _resourceOptions;

        public ResourceClient(HttpClient client, IOptions<ResourceOptions> resourceOptions)
        {
            _client = client;
            _resourceOptions = resourceOptions;
        }

        public async Task AnalyzeResourcesAsync(List<ResourceAnalysisRequest> requests)
        {
            await _client.PostJsonAsync<string>(_resourceOptions.Value.ApiUrls.Sync, requests,
                requestSettings: new JsonSerializerSettings()
                {
                    ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
                });
        }
    }
}