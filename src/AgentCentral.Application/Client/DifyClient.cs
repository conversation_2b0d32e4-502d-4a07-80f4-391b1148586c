#nullable enable
using System.Text.Json;
using System.Text.RegularExpressions;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Contracts.RequestModels.Dify;
using AgentCentral.Application.Contracts.ResponseModels.Dify;
using AgentCentral.Infrastructure.HttpUtil;
using Item.Common.Lib.LogUtil;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace AgentCentral.Application.Client
{
    public class DifyClient
    {
        private readonly IOptionsSnapshot<DifyOptions> _difyOptions;

        private readonly HttpClient _httpClient;

        private readonly IHttpContextAccessor _httpContextAccessor;

        public DifyClient(IOptionsSnapshot<DifyOptions> difyOptions,
            HttpClient httpClient,
            IHttpContextAccessor httpContextAccessor
            )
        {
            _difyOptions = difyOptions;
            _httpClient = httpClient;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<(string? appId, string? appCode)> CreateAppAsync(CreateDifyAppRequest request)
        {
            var result = await _httpClient.PostJsonAsync<CreateDifyAppReponse>(_difyOptions.Value.ApiUrls.CreateApp, request, GetUserToken());
            return (result?.Id, result?.Site?.Code);
        }

        public async Task<(string? appId, string? appCode)> CopyAppAsync(string appId, CopyDifyAppRequest request)
        {
            var url = $"{_difyOptions.Value.ApiUrls.CopyApp.Replace("{app_id}", appId)}";
            var result = await _httpClient.PostJsonAsync<CreateDifyAppReponse>(url, request, GetUserToken());
            return (result?.Id, result?.Site?.Code);
        }

        public async Task DeleteAppAsync(string appId)
        {
            var url = $"{_difyOptions.Value.ApiUrls.DeleteApp.Replace("{app_id}", appId)}";
            await _httpClient.DeleteJsonAsync<object>(url, GetUserToken());
        }

        public async Task<(string? appId, string? appCode)> UpdateAppAsync(string appId, UpdateDifyAppRequest appRequest)
        {
            var url = $"{_difyOptions.Value.ApiUrls.UpdateApp.Replace("{app_id}", appId)}";
            var result = await _httpClient.PutJsonAsync<CreateDifyAppReponse>(url, appRequest, GetUserToken());
            return (result?.Id, result?.Site?.Code);
        }

        public async Task<CreateTagResponse> CreateTagAsync(CreateTagRequest request)
        {
            var result = await _httpClient.PostJsonAsync<CreateTagResponse>(_difyOptions.Value.ApiUrls.CreateTag, request, GetUserToken());
            return result;
        }

        public async Task<UpdateTagResponse> UpdateTagAsync(string tagId, UpdateTagRequest request)
        {
            var url = $"{_difyOptions.Value.ApiUrls.UpdateTag}{tagId}";
            var result = await _httpClient.PatchJsonAsync<UpdateTagResponse>(url, request, GetUserToken());
            return result;
        }

        public async Task<bool> DeleteTagAsync(string tagId)
        {
            var url = $"{_difyOptions.Value.ApiUrls.DeleteTag}{tagId}";
            await _httpClient.DeleteJsonAsync<object>(url, GetUserToken());
            return true;
        }

        public async Task<bool> RemoveTagBindingAsync(RemoveTagBindingRequest request)
        {
            await _httpClient.PostJsonAsync<object>(_difyOptions.Value.ApiUrls.RemoveTagBinding, request, GetUserToken());
            return true;
        }

        public async Task<bool> CreateTagBindingAsync(CreateTagBindingRequest request)
        {
            await _httpClient.PostJsonAsync<object>(_difyOptions.Value.ApiUrls.CreateTagBinding, request, GetUserToken());
            return true;
        }

        public async Task<AutoReviewResponse?> AutoReviewAsync(string appId)
        {
            try
            {
                var url = _difyOptions.Value.ApiUrls.AutoReview;
                var requestData = new { app_id = appId };
                var response = await _httpClient.PostJsonAsync<AutoReviewResponse>(url, requestData, GetUserToken());
                return response;
            }
            catch (Exception ex)
            {
                UnisLog.Error($"AutoReview error:{ex.Message},appId{appId}");
                return null;
            }
        }

        public async Task<MpAutoReviewResponse?> MpAppAutoReviewAsync(MpAppAutoReviewRequest inputs)
        {
            var jsonText = string.Empty;
            // 设置10分钟超时
            using CancellationTokenSource cancellationTokenSource = new(TimeSpan.FromMinutes(10));
            await RunWorkflowStreamingAsync(inputs, _difyOptions.Value.Workflows.MpAppAutoReviewApiKey, (eventType, resData) =>
            {
                if (!DifyStreamEventTypes.WorkflowFinished.Equals(resData.Event))
                {
                    return Task.CompletedTask;
                }
                WorkflowFinishedData? result = resData.Data.ToObject<WorkflowFinishedData>();
                if (result?.Outputs.TryGetValue("text", out var text) ?? false)
                {
                    jsonText = text as string;
                }
                return Task.CompletedTask;
            }, cancellationTokenSource.Token);
            return MpAutoReviewResponse.parseFromOutputs(jsonText);
        }

        public async Task<MpAutoReviewResponse?> MpMcpAutoReviewAsync(MpMcpAutoReviewRequest inputs)
        {
            var jsonText = string.Empty;
            // 设置10分钟超时
            using CancellationTokenSource cancellationTokenSource = new(TimeSpan.FromMinutes(10));
            await RunWorkflowStreamingAsync(inputs, _difyOptions.Value.Workflows.MpMcpAutoReviewApiKey, (eventType, resData) =>
            {
                if (!DifyStreamEventTypes.WorkflowFinished.Equals(resData.Event))
                {
                    return Task.CompletedTask;
                }
                WorkflowFinishedData? result = resData.Data.ToObject<WorkflowFinishedData>();
                if (result?.Outputs.TryGetValue("text", out var text) ?? false)
                {
                    jsonText = text as string;
                }
                return Task.CompletedTask;
            }, cancellationTokenSource.Token);
            return MpAutoReviewResponse.parseFromOutputs(jsonText);
        }

        public async Task<MpPrivacyPolicyReviewResponse?> MpAppPrivacyPolicyReviewAsync(MpAppPrivacyPolicyReviewRequest inputs)
        {
            try
            {
                var response = await RunWorkflowBlocking(inputs, _difyOptions.Value.Workflows.MpAppPolicyApiKey);
                var dataOutputs = response?.Data?.Outputs;
                if (dataOutputs == null)
                {
                    return null;
                }

                var jsonText = JsonDocument.Parse(JsonConvert.SerializeObject(dataOutputs)).RootElement.GetProperty("text").GetString() ?? string.Empty;
                // 格式处理
                var cleanJson = Regex.Replace(jsonText, @"^```json\n|\n```$", "", RegexOptions.Singleline);
                return JsonConvert.DeserializeObject<MpPrivacyPolicyReviewResponse>(cleanJson);
            }
            catch (Exception ex)
            {
                UnisLog.Error($"MpAppPrivacyPolicyReview error:{ex.Message},inputs:{JsonConvert.SerializeObject(inputs)}");
                return null;
            }
        }

        public async Task<RunDifyWorkflowResponse?> RunWorkflowBlocking(object inputs, string apiKey)
        {
            try
            {
                var request = new RunDifyWorkflowRequest
                {
                    Inputs = inputs,
                    User = "item-marketplace"
                };
                var url = _difyOptions.Value.Workflows.Url + _difyOptions.Value.Workflows.RunUrl;
                
                // 设置5分钟超时
                using CancellationTokenSource cancellationTokenSource = new(TimeSpan.FromMinutes(5));
                var response = await _httpClient.PostJsonAsync<RunDifyWorkflowResponse>(url, request, GetUserToken(apiKey), cancellationToken: cancellationTokenSource.Token);
                return response;
            }
            catch (TaskCanceledException)
            {
                UnisLog.Error($"RunWorkflowBlocking timeout, inputs:{inputs}");
                throw; // 重新抛出超时异常
            }
            catch (Exception ex)
            {
                UnisLog.Error($"RunWorkflowBlocking error:{ex.Message},inputs{inputs}");
                return null;
            }
        }

        /// <summary>
        /// 根据 workflow 执行 ID 获取 workflow 任务当前执行结果
        /// </summary>
        /// <param name="workflowId">workflow 执行 ID</param>
        /// <param name="apiKey">API密钥</param>
        /// <returns>工作流执行结果</returns>
        public async Task<WorkflowExecutionResultResponse?> GetWorkflowResult(string workflowId, string apiKey)
        {
            try
            {
                var url = $"{_difyOptions.Value.Workflows.Url}{_difyOptions.Value.Workflows.RunUrl}/{workflowId}";
                var response = await _httpClient.GetObjectAsync<WorkflowExecutionResultResponse>(url, GetUserToken(apiKey));
                return response;
            }
            catch (Exception ex)
            {
                UnisLog.Error($"GetWorkflowResult error:{ex.Message},workflowId:{workflowId}");
                return null;
            }
        }

        /// <summary>
        /// 流式执行 Dify Workflow，通过 SSE 接收实时事件
        /// </summary>
        /// <param name="inputs">输入参数</param>
        /// <param name="apiKey">API密钥</param>
        /// <param name="onEventReceived">事件接收回调函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task RunWorkflowStreamingAsync(
            object inputs,
            string apiKey,
            Func<string, DifyWorkflowStreamEventBase, Task> onEventReceived,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new RunDifyWorkflowRequest
                {
                    Inputs = inputs,
                    ResponseMode = ResponseModes.Streaming, // 设置为流式模式
                    User = "item-marketplace"
                };

                var url = _difyOptions.Value.Workflows.Url + _difyOptions.Value.Workflows.RunUrl;

                await _httpClient.PostJsonStreamAsync(
                    url,
                    request,
                    onEventReceived,
                    GetUserToken(apiKey),
                    cancellationToken: cancellationToken);
            }
            catch (TaskCanceledException)
            {
                UnisLog.Error($"RunWorkflowStreaming timeout, inputs:{inputs}");
                throw; // 重新抛出超时异常
            }
            catch (Exception ex)
            {
                UnisLog.Error($"RunWorkflowStreaming error:{ex.Message}, inputs:{inputs}");
                throw;
            }
        }

        private Dictionary<string, string> GetUserToken()
        {
            return new Dictionary<string, string>()
            {
                { "Authorization", $"{_httpContextAccessor.HttpContext?.Request.Headers.Authorization}" }
            };
        }

        private Dictionary<string, string> GetUserToken(string apiKey)
        {
            return new Dictionary<string, string> {{ "Authorization", $"Bearer {apiKey}" }};
        }
    }
}
