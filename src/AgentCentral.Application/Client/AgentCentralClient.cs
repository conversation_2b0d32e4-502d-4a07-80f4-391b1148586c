using System.Runtime.CompilerServices;
using AgentCentral.Application.Contracts.Options;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using AgentCentral.Infrastructure.HttpUtil;

namespace AgentCentral.Application.Client
{
    public class AgentCentralClient
    {
        private readonly HttpClient _client;

        private readonly IOptions<AgentCentralOptions> _agentCentralOptions;

        private readonly IHttpContextAccessor _httpContextAccessor;

        public AgentCentralClient(HttpClient client, IOptions<AgentCentralOptions> agentCentralOptions, IHttpContextAccessor httpContextAccessor)
        {
            _client = client;
            _agentCentralOptions = agentCentralOptions;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<bool> AutoReviewAsync(long agentId)
        {
            await _client.PostJsonAsync<object>($"{_agentCentralOptions.Value.ApiUrls.AutoReview}{agentId}", null, GetUserToken());
            return true;
        }

        public async Task<bool> PrivacyPolicyReviewAsync(long appId)
        {
            var template = _agentCentralOptions.Value.ApiUrls.PrivacyPolicyReview;
            await _client.PostJsonAsync<object>(string.Format(template, appId), null, GetUserToken());
            return true;
        }

        private Dictionary<string, string> GetUserToken()
        {
            return new Dictionary<string, string>()
            {
                { "Authorization", $"{_httpContextAccessor.HttpContext?.Request.Headers.Authorization}" }
            };
        }
    }
}
