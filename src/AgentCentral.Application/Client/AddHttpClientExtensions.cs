using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using Serilog;

namespace AgentCentral.Application.Client
{
    public static class AddHttpClientExtensions
    {
        private static readonly TimeSpan _timeout = TimeSpan.FromSeconds(30);

        public static void AddClient(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            serviceCollection.AddHttpClient<IamClient>(c => c.BaseAddress = new Uri(configuration["IamApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<ResourceClient>(c => c.BaseAddress = new Uri(configuration["ResourceApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<ClockApiClient>(c => c.BaseAddress = new Uri(configuration["HrmApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<CrmClient>(c => c.BaseAddress = new Uri(configuration["CrmApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<HrmClient>(c =>
                {
                    c.BaseAddress = new Uri(configuration["HrmApis:BaseUrl"]);
                    c.DefaultRequestHeaders.Add("hrm-application", "agent");
                })
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<DifyClient>(c => c.BaseAddress = new Uri(configuration["DifyApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<WorkflowClient>(c => c.BaseAddress = new Uri(configuration["WorkflowApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<AgentCentralClient>(c => c.BaseAddress = new Uri(configuration["AgentCentralApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<CallLogClient>(c => c.BaseAddress = new Uri(configuration["CallLogApiUrls:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<McpClient>(c => c.BaseAddress = new Uri(configuration["McpApis:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));

            serviceCollection
                .AddHttpClient<MessageCenterClient>(c => c.BaseAddress = new Uri(configuration["MessageCenterApiUrls:BaseUrl"]))
                .AddPolicyHandler(Policy<HttpResponseMessage>.Handle<TimeoutException>().WaitAndRetryAsync(3,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: OnRetry))
                .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(_timeout));
        }

        private static void OnRetry(DelegateResult<HttpResponseMessage> outcome, TimeSpan timespan, int retryCount,
            Context context)
        {
            Log.Error($"Delaying for {timespan.TotalSeconds} seconds, then making retry {retryCount}.");
        }
    }
}