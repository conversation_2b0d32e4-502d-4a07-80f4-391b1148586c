using System;
using System.Net.Http;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.CallLog;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Infrastructure.HttpUtil;
using Item.Common.Lib.HttpUtil;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace AgentCentral.Application.Client
{
    /// <summary>
    /// Call log client for interacting with smart-call-admin API
    /// </summary>
    public class CallLogClient
    {
        private readonly IOptionsSnapshot<CallLogOptions> _options;
        private readonly HttpClient _httpClient;
        private readonly IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="options">Call log options</param>
        /// <param name="httpClient">Http client</param>
        /// <param name="httpContextAccessor">Http context accessor</param>
        public CallLogClient(
            IOptionsSnapshot<CallLogOptions> options,
            HttpClient httpClient,
            IHttpContextAccessor httpContextAccessor)
        {
            _options = options;
            _httpClient = httpClient;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Get call support statistics
        /// </summary>
        /// <returns>Call support statistics</returns>
        public async Task<CallSupportStatisticsDto> GetCallSupportStatisticsAsync()
        {
            var headers = new Dictionary<string, string>()
            {
                { "Authorization", $"{_httpContextAccessor.HttpContext?.Request.Headers.Authorization}" }
            };

            var result = await _httpClient.GetObjectAsync<BaseCallLogResponseDto<CallSupportStatisticsDto>>(_options.Value.ApiUrls.CallLogStats);
            return result?.Data;
        }

        /// <summary>
        /// Search call logs with pagination
        /// </summary>
        /// <param name="searchParams">Search parameters</param>
        /// <returns>Page response with call log records</returns>
        public async Task<CallLogPageDto<List<CallLogRecordDto>>> SearchCallLogsAsync(CallLogSearchParamsDto searchParams)
        {
            var headers = new Dictionary<string, string>()
            {
                //{ "Authorization", $"{_httpContextAccessor.HttpContext?.Request.Headers.Authorization}" },
                { "accept", "*/*" }
            };

            var result = await _httpClient.PostJsonAsync<BaseCallLogResponseDto<CallLogPageDto<List<CallLogRecordDto>>>>(_options.Value.ApiUrls.CallLogPage, searchParams, headers);

            return result?.Data;
        }
    }
}