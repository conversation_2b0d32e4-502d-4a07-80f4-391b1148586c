using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Contracts.RequestModels.MpMcp;
using AgentCentral.Infrastructure.HttpUtil;
using AgentCentral.Infrastructure.LogUtil;
using AgentCentral.Infrastructure.Redis;
using Microsoft.Extensions.Options;

namespace AgentCentral.Application.Client
{
    /// <summary>
    /// MCP服务客户端
    /// </summary>
    public class McpClient
    {
        private readonly HttpClient _client;
        private readonly IOptionsSnapshot<McpOptions> _mcpOptions;
        private readonly IRedisService _redisService;
        private const string CACHE_KEY_PREFIX = "MCP:Marketplace:";
        private const int CACHE_MINUTES = 30;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="client">HTTP客户端</param>
        /// <param name="mcpOptions">MCP配置选项</param>
        /// <param name="redisService">Redis服务</param>
        public McpClient(HttpClient client, IOptionsSnapshot<McpOptions> mcpOptions, IRedisService redisService)
        {
            _client = client;
            _mcpOptions = mcpOptions;
            _redisService = redisService;
        }

        /// <summary>
        /// 获取市场应用列表
        /// </summary>
        /// <param name="tags">标签筛选</param>
        /// <param name="serviceName">服务名称筛选</param>
        /// <returns>市场应用列表响应</returns>
        public async Task<McpMarketplaceResponseDto> GetMarketplaceAppsAsync(string tags = "", string serviceName = "")
        {
            try
            {
                // 发起API请求
                string url = _mcpOptions.Value.BaseUrl + _mcpOptions.Value.ApiUrls.Marketplace;
                url += ($"?tags=&serviceName={serviceName}");
                McpMarketplaceResponseDto result = await _client.GetObjectAsync<McpMarketplaceResponseDto>(url);

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "获取MCP市场应用列表失败");
                throw;
            }
        }


        /// <summary>
        /// 更新Mcp服务
        /// </summary>
        /// <param name="tags">标签筛选</param>
        /// <param name="serviceName">服务名称筛选</param>
        /// <returns>市场应用列表响应</returns>
        public async Task<McpMarketplaceResponseDto> UpdateMcpServer(string serviceName, McpAppDto updateDto)
        {
            try
            {
                // 发起API请求
                var url = $"{_mcpOptions.Value.ApiUrls.UpdateMcpServer.Replace("{service_name}", serviceName)}";
                McpMarketplaceResponseDto result = await _client.PutJsonAsync<McpMarketplaceResponseDto>(url, updateDto);

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "更新MCP服务失败");
                throw;
            }
        }

    }
} 