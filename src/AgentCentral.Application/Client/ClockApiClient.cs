using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Infrastructure.HttpUtil;
using Microsoft.Extensions.Options;

namespace AgentCentral.Application.Client
{
    public class ClockApiClient
    {
        private readonly HttpClient _client;
        private readonly IOptions<ClockApiOptions> _clockApiOptions;

        public ClockApiClient(HttpClient client, IOptions<ClockApiOptions> clockApiOptions)
        {
            _client = client;
            _clockApiOptions = clockApiOptions;
        }

        public async Task<HrmEmployeeDto> GetEmployeeByIdAsync(string employeeId)
        {
            var request = new
            {
                Data = new { EmployeeID = employeeId },
                ClientID = 1,
                UserID = 1
            };

            var result = await _client.PostJsonAsync<HrmResponseDto<HrmEmployeeDto>>(
                _clockApiOptions.Value.ApiUrls.GetEmployee,
                request,
                new Dictionary<string, string>
                {
                    { "Token", _clockApiOptions.Value.Token }
                }
            );
            return result?.Result;
        }
    }
}