using AgentCentral.Application.Contracts.Dtos.MessageCenter;
using AgentCentral.Application.Contracts.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AgentCentral.Infrastructure.HttpUtil;

namespace AgentCentral.Application.Client
{
    public class MessageCenterClient
    {
        private readonly IOptionsSnapshot<MessageCenterOptions> _options;
        private readonly HttpClient _httpClient;
        private readonly ILogger<MessageCenterClient> _logger;

        public MessageCenterClient(
            IOptionsSnapshot<MessageCenterOptions> options,
            HttpClient httpClient,
            ILogger<MessageCenterClient> logger)
        {
            _options = options;
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<MessageCenterResponseDto> SendMessageAsync(SendMessageRequest request)
        {
            try
            {
                var result = await _httpClient.PostJsonAsync<MessageCenterResponseDto>(_options.Value.ApiUrls.Send, request);
                return result;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Failed to send message");
                throw;
            }
        }
    }
} 