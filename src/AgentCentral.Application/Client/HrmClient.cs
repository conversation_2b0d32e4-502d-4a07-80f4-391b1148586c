using AgentCentral.Application.Contracts.Dtos.HrmDtos;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Infrastructure.HttpUtil;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace AgentCentral.Application.Client
{
    public class HrmClient
    {
        private readonly IOptionsSnapshot<HrmOptions> _hrmOptions;

        private readonly HttpClient _httpClient;

        private readonly IHttpContextAccessor _httpContextAccessor;

        private readonly IamClient _iamClient;

        public HrmClient(IOptionsSnapshot<HrmOptions> hrmOptions,
            HttpClient httpClient,
            IHttpContextAccessor httpContextAccessor,
            IamClient iamClient)
        {
            _hrmOptions = hrmOptions;
            _httpClient = httpClient;
            _httpContextAccessor = httpContextAccessor;
            _iamClient = iamClient;
        }

        public async Task<EmployeeDetailDto> GetEmployeeInfoAsync()
        {
            var headers = new Dictionary<string, string>()
            {
                { "Authorization", $"{_httpContextAccessor.HttpContext.Request.Headers.Authorization}" }
            };
            var result = await _httpClient.GetObjectAsync<BaseHrmResponseDto<EmployeeDetailDto>>(_hrmOptions.Value.ApiUrls.EmployeeDetail, headers);
            return result.Data;
        }

        public async Task<List<CompanyDto>> GetCompaniesAsync(string keyWord, int pageIndex, int pageSize)
        {
            var headers = new Dictionary<string, string>()
            {
                { "Authorization", $"Bearer {await _iamClient.GetOauth2TokenAsync()}" }
            };

            string url = $"{_hrmOptions.Value.ApiUrls.Companies}?PageIndex={pageIndex}&PageSize={pageSize}";
            if (!string.IsNullOrEmpty(keyWord))
            {
                url += $"&KeyWord={Uri.EscapeDataString(keyWord)}";
            }

            var result = await _httpClient.GetObjectAsync<BaseHrmResponseDto<BaseHrmPageDto<CompanyDto>>>(url, headers);
            return result.Data?.Data;
        }

        public async Task<List<DepartmentDto>> GetDepartmentsAsync(string companyCode)
        {
            var headers = new Dictionary<string, string>()
            {
                { "Authorization", $"Bearer {await _iamClient.GetOauth2TokenAsync()}" }
            };

            string url = _hrmOptions.Value.ApiUrls.Departments;
            if (!string.IsNullOrEmpty(companyCode))
            {
                url += $"?CompanyCode={Uri.EscapeDataString(companyCode)}";
            }

            var result = await _httpClient.GetObjectAsync<BaseHrmResponseDto<List<DepartmentDto>>>(url, headers);
            return result.Data;
        }
    }
}
