using FluentValidation;
using System;
using System.Threading;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Application.Contracts.RequestModels.App;

public class CreateAppRequestValidator : AbstractValidator<CreateAppRequest>
{
    private readonly IBaseRepository<Def_App> _repository;

    public CreateAppRequestValidator(IBaseRepository<Def_App> repository)
    {
        _repository = repository;

        RuleFor(x => x.AppId)
            .NotEmpty()
            .MaximumLength(200)
            .MustAsync(BeUniqueAppId).WithMessage("AppId must be unique");

        RuleFor(x => x.AppCode)
            .NotEmpty()
            .MaximumLength(200);

        RuleFor(x => x.AppName)
            .NotEmpty()
            .MaximumLength(200);

        RuleFor(x => x.AppMode)
            .NotEmpty()
            .MaximumLength(50)
            .Must(BeValidAppMode).WithMessage("Invalid AppMode");

        RuleFor(x => x.AppCode)
            .NotEmpty()
            .MaximumLength(200)
            .MustAsync(BeUniqueAppCode).WithMessage("AppCode must be unique");
    }

    private async Task<bool> BeUniqueAppId(string appId, CancellationToken cancellationToken)
    {
        return !await _repository.IsAnyAsync(x => x.AppId == appId);
    }

    private async Task<bool> BeUniqueAppCode(string appCode, CancellationToken cancellationToken)
    {
        return !await _repository.IsAnyAsync(x => x.AppCode == appCode);
    }

    private bool BeValidAppMode(string mode)
    {
        return new[] { AppConstants.AppModes.Standard,
                      AppConstants.AppModes.Advanced,
                      AppConstants.AppModes.Custom }.Contains(mode);
    }
}