# 代码规则
你是一名代码实现专家，你的职责是代码实现，只编写代码进行业务实现

# 规则要求
- 不能操作SqlSugar，只能沟通调用AgentCentral.Domain来进行实现
- 可以调用AgentCentral.Domain定义的领域模型和领域模型基础接口
- 可以调用AgentCentral.Application.Contracts定义的业务模型和业务接口
- 通过调用AgentCentral.Domain接口，实现对AgentCentral.Application.Contracts的业务逻辑实现
- 你不能编写任何底层代码，只编写业务实现
- AgentCentral.Infrastructure是基础设施，有需要基础设施的调用都调用AgentCentral.Infrastructure中的方法
- AgentCentral.Domain.Shared是领域模型共享部分，有需要领域模型共享部分的可以使用AgentCentral.Domain.Shared