using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.Dtos.ReviewRecords;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AutoMapper;
using Item.Internal.ChangeLog.Models;

public class AppProfile : Profile
{
    public AppProfile()
    {
        CreateMap<Def_App, AppDetailResponse>();
        CreateMap<Def_App, AppDetailPageResponse>();
        CreateMap<CreateAppRequest, Def_App>();
        CreateMap<UpdateAppRequest, Def_App>()
            .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));

        CreateMap<CreateAppDto, Def_App>();

        CreateMap<Def_App_Permission, AppDepartmentDto>().BeforeMap((src, dest) =>
        {
            dest.CompanyId = src.Company;
            dest.DepartmentCode = src.DepartmentCode;
        });

        CreateMap<UpdateAppDto, Def_App>()
            .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
        CreateMap<AppListModel, AppDetailResponse>();
        CreateMap<AppListModel, AppDetailPageResponse>();
        CreateMap<CreateAppDto, UpdateAppDto>().ReverseMap();

        CreateMap<AppDetailResponse, CreateAppDto>()
            .ForMember(dest => dest.BannerAttachmentIds, opt => opt.MapFrom(src => src.Banners != null ? src.Banners.Select(b => b.Id).ToList() : new List<long>()))
            .ForMember(dest => dest.VideoAttachmentId, opt => opt.MapFrom(src => src.Video != null ? src.Video.Id : 0));

        CreateMap<AppChangeLogModel, RecordResultModel>().BeforeMap((src, dest) =>
        {
            dest.AgentId = src.MainAppId;
            dest.Remarks = src.Description;
            dest.Creator = src.CreateName;
            dest.VersionId = "V" + src.VersionNumber.ToString();
            dest.Status = Enum.Parse<AppStatusEnum>(src.AppStatus.ToString());
            dest.OpType = Enum.Parse<OpTypeEnum>(src.OpType.ToString());
            dest.AgentName = src.AppName;
            dest.OperationTime = src.OpTime;
            dest.Reviewer = src.ApprovalUserName;
        });
    }
}