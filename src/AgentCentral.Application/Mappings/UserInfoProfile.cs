using AgentCentral.Application.Contracts.Dtos.UserInfo;
using AgentCentral.Application.Contracts.RequestModels.UserInfo;
using AgentCentral.Application.Contracts.ResponseModels.UserInfo;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Mappings
{
    public class UserInfoProfile : Profile
    {
        public UserInfoProfile()
        {
            CreateMap<Def_User_Info, UserInfoResponse>();
            CreateMap<UpdateUserInfoRequest, Def_User_Info>();
            CreateMap<Def_User_Info, UserInfoDto>();
            CreateMap<Def_User_Info, UserInfoResponse>();
            CreateMap<Def_User_Tag, UserTagDto>()
                .ForMember(dest => dest.TagId, opt => opt.MapFrom(src => src.Id));

            // DTO -> ʵ��
            CreateMap<UpdateUserInfoRequest, Def_User_Info>();
        }
    }
}