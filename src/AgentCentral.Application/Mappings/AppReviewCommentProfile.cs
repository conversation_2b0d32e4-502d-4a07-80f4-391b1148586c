using AutoMapper;
using AgentCentral.Domain.Entities;
using AgentCentral.Application.Contracts.RequestModels.AppReviewComment;
using AgentCentral.Application.Contracts.ResponseModels.AppReviewComment;

namespace AgentCentral.Application.Mappings
{
    public class AppReviewCommentProfile : Profile
    {
        public AppReviewCommentProfile()
        {
            CreateMap<Def_App_Review_Comment, AppReviewCommentResponse>();
            CreateMap<CreateAppReviewCommentRequest, Def_App_Review_Comment>();
        }
    }
}