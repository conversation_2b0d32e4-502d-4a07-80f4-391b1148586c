using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.HrmDtos;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Mappings
{
    /// <summary>
    /// 角色映射配置
    /// </summary>
    public class RoleProfile : Profile
    {
        /// <summary>
        /// 配置角色映射
        /// </summary>
        public RoleProfile()
        {
            // 实体 -> DTO
            CreateMap<Def_Role, RoleDto>();
            CreateMap<Def_Department, DepartmentDto>();

            // DTO -> 实体
            CreateMap<RoleDto, Def_Role>();
            CreateMap<DepartmentDto, Def_Department>();
        }
    }
}
