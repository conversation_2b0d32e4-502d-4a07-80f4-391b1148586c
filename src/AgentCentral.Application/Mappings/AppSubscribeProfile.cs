using AutoMapper;
using AgentCentral.Domain.Entities;
using AgentCentral.Application.Contracts.RequestModels.AppSubscribe;
using AgentCentral.Application.Contracts.ResponseModels.AppSubscribe;

namespace AgentCentral.Application.Mappings
{
    public class AppSubscribeProfile : Profile
    {
        public AppSubscribeProfile()
        {
            CreateMap<Def_App_Subscribe, AppSubscribeResponse>();
            CreateMap<CreateAppSubscribeRequest, Def_App_Subscribe>();
        }
    }
}