using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Mappings
{
    public class AppChangeProfile : Profile
    {
        public AppChangeProfile()
        {
            CreateMap<Def_App_Change, AppChangeDto>();
            CreateMap<UpdateAppChangeDto, Def_App_Change>()
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
        }
    }
}
