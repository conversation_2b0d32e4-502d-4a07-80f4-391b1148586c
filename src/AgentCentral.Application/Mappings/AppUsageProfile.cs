using AutoMapper;
using AgentCentral.Domain.Entities;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Application.Contracts.ResponseModels.AppUsage;
using AgentCentral.Application.Contracts.RequestModels.AppUsage;

namespace AgentCentral.Application.Mappings
{
    public class AppUsageProfile : Profile
    {
        public AppUsageProfile()
        {
            CreateMap<Def_App_Usage, AppUsageResponse>();
            CreateMap<CreateAppUsageRequest, Def_App_Usage>();
            CreateMap<UpdateAppUsageRequest, Def_App_Usage>();
        }
    }
}