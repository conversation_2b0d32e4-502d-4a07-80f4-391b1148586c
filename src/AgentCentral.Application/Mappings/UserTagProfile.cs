using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.ResponseModels.UserTag;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Mappings
{
    /// <summary>
    /// 用户标签映射配置
    /// </summary>
    public class UserTagProfile : Profile
    {
        /// <summary>
        /// 初始化用户标签映射配置
        /// </summary>
        public UserTagProfile()
        {
            // 实体 -> DTO
            CreateMap<Def_User_Tag, UserTagDto>();
        }
    }
}
