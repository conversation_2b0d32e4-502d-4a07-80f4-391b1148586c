<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="ConvertApi" Version="3.1.2" />
    <PackageReference Include="Cronos" Version="0.9.0" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Item.BlobProvider" Version="8.0.4" />
    <PackageReference Include="Serilog" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AgentCentral.Application.Contracts\AgentCentral.Application.Contracts.csproj" />
    <ProjectReference Include="..\AgentCentral.Domain\AgentCentral.Domain.csproj" />
    <ProjectReference Include="..\AgentCentral.SqlSugarDB\AgentCentral.SqlSugarDB.csproj" />
    <ProjectReference Include="..\AgentCentral.Infrastructure\AgentCentral.Infrastructure.csproj" />
  </ItemGroup>
</Project>
