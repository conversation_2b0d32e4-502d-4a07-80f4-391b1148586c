using System.Linq.Expressions;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.UserInfo;
using AgentCentral.Application.Contracts.IService;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.UserInfo;
using AgentCentral.Application.Contracts.ResponseModels.UserInfo;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Const;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.Infrastructure.Redis;
using AgentCentral.Infrastructure.Redis.Enum;
using AutoMapper;
using Item.BlobProvider;
using Item.Common.Lib.LogUtil;
using Microsoft.IdentityModel.Tokens;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    public class UserInfoService : IUserInfoService
    {
        private readonly IUserInfoRepository _userInfoRepository;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly IAttachmentMappingRepository _attachmentMappingRepository;
        private readonly IBlobContainer _blobContainer;
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IUserTagRepository _userTagRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IIamService _iamService;
        private readonly IRedisService _redisService;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IBaseRepository<Def_User_Tag_Relation> _userTagRelationRepository;
        private readonly IUserTypeRelationRepository _userTypeRelationRepository;

        public UserInfoService(
            IUserInfoRepository userInfoRepository,
            IMapper mapper,
            IUserRoleRepository userRoleRepository,
            IUserTagRepository userTagRepository,
            IRoleRepository roleRepository,
            IRedisService redisService,
            IIamService iamService,
            IDepartmentRepository departmentRepository,
            IBaseRepository<Def_User_Tag_Relation> userTagRelationRepository,
            UserContext userContext, IAttachmentMappingRepository attachmentMappingRepository,
            IUserTypeRelationRepository userTypeRelationRepository,
            IAttachmentRepository attachmentRepository, IBlobContainer blobContainer)
        {
            _userInfoRepository = userInfoRepository;
            _userRoleRepository = userRoleRepository;
            _userTagRepository = userTagRepository;
            _roleRepository = roleRepository;
            _redisService = redisService;
            _iamService = iamService;
            _userTagRelationRepository = userTagRelationRepository;
            _departmentRepository = departmentRepository;
            _mapper = mapper;
            _userContext = userContext;
            _attachmentMappingRepository = attachmentMappingRepository;
            _attachmentRepository = attachmentRepository;
            _blobContainer = blobContainer;
            _userTypeRelationRepository = userTypeRelationRepository;
        }

        public async Task<UserInfoResponse> GetUserInfoAsync(long? userId)
        {
            _userContext.UserId = userId ?? _userContext.UserId;
            var userInfo = await _userInfoRepository.GetFirstAsync(f => f.UserId == _userContext.UserId);
            if (userInfo == null)
            {
                throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "User not found");
            }

            var userResponse = _mapper.Map<UserInfoResponse>(userInfo);
            var mapping = await _attachmentMappingRepository.GetFirstAsync(g =>
                g.BusinessId == _userContext.UserId && g.BusinessType == AttachmentTypeEnum.UserProfilePicture);
            if (mapping != null)
            {
                var attachment = await _attachmentRepository.GetByIdAsync(mapping.AttachmentId);
                userResponse.AccessUrl = await _blobContainer.GetAccessUrl(attachment.FileName);
            }

            return userResponse;
        }

        public async Task<bool> UpdateUserInfoAsync(UpdateUserInfoRequest request)
        {
            try
            {
                using var context = _userInfoRepository.CreateContext();

                var existingUser = await _userInfoRepository.GetFirstAsync(g => g.UserId == _userContext.UserId);
                if (existingUser == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "User not found");
                }

                existingUser.UserProfile = request.UserProfile;
                await _userInfoRepository.UpdateAsync(existingUser);


                if (request.AttachmentId.HasValue)
                {
                    await _attachmentMappingRepository.DeleteAsync(where =>
                        where.BusinessId == _userContext.UserId &&
                        where.BusinessType == AttachmentTypeEnum.UserProfilePicture);
                    await _attachmentMappingRepository.InsertAsync(new Doc_Attachment_Mapping()
                    {
                        AttachmentId = request.AttachmentId.Value,
                        BusinessType = AttachmentTypeEnum.UserProfilePicture,
                        BusinessId = _userContext.UserId
                    });
                }

                context.Commit();
                return true;
            }
            catch (Exception exception)
            {
                UnisLog.Error(exception, "UpdateUserInfoAsync");
                return false;
            }
        }

        /// <summary>
        /// 获取用户列表（分页）
        /// </summary>
        public async Task<PageModelDto<UserInfoDto>> GetUserListAsync(UserSearchDto searchDto)
        {
            // 构建查询条件
            Expression<Func<Def_User_Info, bool>> filter = Expressionable.Create<Def_User_Info>()
                .AndIF(!searchDto.TenantId.IsNullOrEmpty(), u => u.TenantId == searchDto.TenantId)
                .And(u => u.UserType != UserTypeEnum.Central && u.UserType != 0)
                .And(u => u.UserId != -1)
                .AndIF(!string.IsNullOrWhiteSpace(searchDto.UserName), u => u.UserName.Contains(searchDto.UserName) || u.FirstName.Contains(searchDto.UserName) || u.LastName.Contains(searchDto.UserName))
                .AndIF(!string.IsNullOrWhiteSpace(searchDto.Email), u => u.Email.Contains(searchDto.Email))
                .AndIF(!string.IsNullOrWhiteSpace(searchDto.TenantName), u => u.CompanyName.Contains(searchDto.TenantName))
                .AndIF(searchDto.UserType.HasValue, u => u.UserType == searchDto.UserType.Value)
                .AndIF(searchDto.IsActive.HasValue, u => u.IsActive == searchDto.IsActive.Value)
                .ToExpression();

            // 查询用户列表
            var (users, total) = await _userInfoRepository.GetUserInfoListAsync(
                filter,
                searchDto.PageIndex,
                searchDto.PageSize,
                searchDto.RoleIds,
                searchDto.TagIds);

            // 转换为DTO
            var userDtos = _mapper.Map<List<UserInfoDto>>(users);

            // 获取所有用户ID
            List<long> userIds = userDtos.Select(u => u.Id).ToList();

            // 批量获取所有用户角色关系
            Dictionary<long, List<Def_User_Role>> allUserRoles = (await _userRoleRepository.GetListAsync(ur => userIds.Contains(ur.UserId)))
                .GroupBy(ur => ur.UserId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // 收集所有角色ID
            HashSet<long> allRoleIds = new HashSet<long>();
            foreach (var roles in allUserRoles.Values)
            {
                foreach (var role in roles.Where(r => r.RoleId != 0))
                {
                    allRoleIds.Add(role.RoleId);
                }
            }

            // 批量获取所有角色信息
            Dictionary<long, Def_Role> roleDict = (await _roleRepository.GetListAsync(r => allRoleIds.Contains(r.Id) && r.IsActive == true))
                .ToDictionary(r => r.Id);

            // 收集所有部门ID
            HashSet<long> allDepartmentIds = new HashSet<long>();
            foreach (var role in roleDict.Values.Where(r => r.DepartmentId.HasValue))
            {
                allDepartmentIds.Add(role.DepartmentId.Value);
            }
            foreach (var roles in allUserRoles.Values)
            {
                foreach (var role in roles.Where(r => r.RoleId == 0 && r.DepartmentId > 0))
                {
                    allDepartmentIds.Add(role.DepartmentId);
                }
            }

            // 批量获取所有部门信息
            Dictionary<long, Def_Department> departmentDict = _departmentRepository.GetList(d => allDepartmentIds.Contains(d.Id))
                .ToDictionary(d => d.Id);

            // 批量获取所有用户标签
            Dictionary<long, List<Def_User_Tag>> allUserTags = await _userTagRepository.GetUserTagsAsync(userIds);

            // 组装数据
            foreach (var userDto in userDtos)
            {
                // 设置用户角色
                userDto.Roles = new List<UserRoleDto>();
                if (allUserRoles.TryGetValue(userDto.Id, out var userRoles) && userRoles.Any())
                {
                    // 处理正常角色
                    var normalRoles = userRoles.Where(r => r.RoleId != 0).ToList();
                    foreach (var userRole in normalRoles)
                    {
                        if (roleDict.TryGetValue(userRole.RoleId, out var role))
                        {
                            string departmentName = string.Empty;
                            if (role.DepartmentId.HasValue && departmentDict.TryGetValue(role.DepartmentId.Value, out var dept))
                            {
                                departmentName = dept.DepartmentName;
                            }

                            userDto.Roles.Add(new UserRoleDto
                            {
                                RoleId = role.Id,
                                RoleName = role.RoleName,
                                RoleCode = role.RoleCode,
                                DepartmentId = role.DepartmentId ?? 0,
                                DepartmentName = departmentName
                            });
                        }
                    }

                    // 处理部门角色
                    var departmentRoles = userRoles.Where(r => r.RoleId == 0).ToList();
                    foreach (var userRole in departmentRoles)
                    {
                        string departmentName = string.Empty;
                        if (departmentDict.TryGetValue(userRole.DepartmentId, out var dept))
                        {
                            departmentName = dept.DepartmentName;
                        }

                        userDto.Roles.Add(new UserRoleDto
                        {
                            RoleId = 0,
                            RoleName = "",
                            RoleCode = "",
                            DepartmentId = userRole.DepartmentId,
                            DepartmentName = departmentName
                        });
                    }
                }

                // 获取用户标签
                if (allUserTags.TryGetValue(userDto.Id, out var userTags))
                {
                    userDto.Tags = _mapper.Map<List<UserTagDto>>(userTags);
                }
                else
                {
                    userDto.Tags = new List<UserTagDto>();
                }

                // 设置用户类型名称
                userDto.UserTypeName = userDto.UserType.ToString();

                // 设置用户来源名称
                userDto.UserSourceName = userDto.UserSource switch
                {
                    UserSourceEnum.Historical => "-",
                    UserSourceEnum.SelfVerified => "Self-Verified",
                    UserSourceEnum.AdminCreated => "Admin-Created",
                    UserSourceEnum.AdminApproved => "Admin-Approved",
                    _ => userDto.UserSource.ToString()
                };
            }

            return new PageModelDto<UserInfoDto>(searchDto.PageIndex, searchDto.PageSize, userDtos, total);
        }

        /// <summary>
        /// 更新用户角色
        /// </summary>
        public async Task<bool> UpdateUserRolesAsync(UpdateUserRolesDto updateDto)
        {
            // 检查用户是否存在
            var existingUser = await _userInfoRepository.GetUserDetailAsync(updateDto.UserId);
            if (existingUser == null)
            {
                throw new AgentCentralException("user not found");
            }

            // 创建角色ID与部门ID的映射字典
            Dictionary<long, long> roleWithDepartments = new Dictionary<long, long>();
            // 部门id
            List<long> departmentIds = new List<long>();

            foreach (var roleItem in updateDto.Roles)
            {
                // 检查是否包含下划线(格式为：部门ID_角色ID)
                if (roleItem.ToString().Contains("_"))
                {
                    var parts = roleItem.ToString().Split('_');
                    if (parts.Length == 2 && long.TryParse(parts[0], out long departmentId) && long.TryParse(parts[1], out long roleId))
                    {
                        roleWithDepartments[roleId] = departmentId;
                    }
                }
                // 不包含下划线，表示部门id
                else
                {
                    departmentIds.Add(Convert.ToInt64(roleItem));
                }
            }

            // 保存用户角色，每个角色使用其对应的部门ID
            return await _userRoleRepository.SaveUserRolesWithDifferentDepartmentsAsync(
                updateDto.UserId,
                roleWithDepartments,
                _userContext.UserId,
                _userContext.UserName,
                existingUser.TenantId,
                departmentIds);
        }

        
        /// <summary>
        /// 更新用户状态
        /// </summary>
        public async Task<bool> UpdateUserStatusAsync(long userId, bool isActive, string accessToken)
        {
            // 检查用户是否存在
            var existingUser = await _userInfoRepository.GetUserDetailAsync(userId);
            if (existingUser == null)
            {
                throw new AgentCentralException("user not found");
            }
            if (isActive)
            {
                var iamResponse = await _iamService.ActivateUserAsync(existingUser.UserId.ToString(), accessToken);

                // 检查IAM用户更新结果
                if (!iamResponse.Success)
                {
                    throw new AgentCentralException("IAM user enable failed: " + iamResponse.Msg);
                }
            }
            else
            {
                var iamResponse = await _iamService.DeactivateUserAsync(existingUser.UserId.ToString(), accessToken);

                // 检查IAM用户更新结果
                if (!iamResponse.Success)
                {
                    throw new AgentCentralException("IAM user disable failed: " + iamResponse.Msg);
                }
            }
                
            return await _userInfoRepository.UpdateUserStatusAsync(userId, isActive);
        }

        /// <summary>
        /// 更新用户完整信息（包括角色和标签）
        /// </summary>
        public async Task<bool> UpdateUserCompleteInfoAsync(UpdateUserCompleteInfoDto updateDto)
        {
            // 检查用户是否存在
            var existingUser = await _userInfoRepository.GetUserDetailAsync(updateDto.Id);
            if (existingUser == null)
            {
                throw new AgentCentralException("update user error: user not found.");
            }

            // 检查邮箱是否已存在
            if (!string.IsNullOrWhiteSpace(updateDto.Email) && existingUser.Email != updateDto.Email)
            {
                bool emailExists = await _userInfoRepository.IsEmailExistsAsync(updateDto.Email, updateDto.Id);
                if (emailExists)
                {
                    throw new AgentCentralException("update user error: This email is already registered under another organization.");
                }
            }

            try
            {
                // 开启事务
                _userInfoRepository.BeginTran();


                // 构建IAM用户更新请求
                /*UpdateUserRequest updateUserIam = new UpdateUserRequest
                {
                    FirstName = updateDto.FirstName,
                    LastName = updateDto.LastName,
                    ContactNumber = updateDto.ContactNumber
                };

                // 调用IAM服务更新用户并获取结果
                var iamResponse = await _iamService.UpdateUserAsync(existingUser.UserId.ToString(), updateUserIam, _userContext.Token);

                // 检查IAM用户更新结果
                if (!iamResponse.Success)
                {
                    throw new AgentCentralException("IAM user update error: " + iamResponse.Msg);
                }*/

                // 权限变更，需要重新登录
                if (existingUser.UserType != updateDto.UserType)
                {
                    _redisService.StringSet(RedisDataType.Token, $"{RedisCacheConsts.UserTokenExpired}{existingUser.UserId}", existingUser.UserName, TimeSpan.FromDays(1));
                }

                // 更新用户信息
                existingUser.FirstName = updateDto.FirstName ?? existingUser.FirstName;
                existingUser.LastName = updateDto.LastName ?? existingUser.LastName;
                existingUser.UserName = updateDto.UserName ?? existingUser.UserName;
                existingUser.Email = updateDto.Email ?? existingUser.Email;
                // existingUser.ContactNumber = updateDto.ContactNumber ?? existingUser.ContactNumber;
                existingUser.UserType = updateDto.UserType;
                existingUser.IsActive = updateDto.IsActive;
                existingUser.UpdateBy = _userContext.UserId;
                existingUser.UpdateName = _userContext.UserName;

                // 保存用户信息
                await _userInfoRepository.UpdateAsync(existingUser);
                
                // 更新用户类型关系表
                var existingUserTypeRelation = await _userTypeRelationRepository.GetFirstAsync(r => 
                    r.TenantId == existingUser.TenantId && 
                    r.UserId == existingUser.UserId && 
                    r.IsActive == true);

                if (existingUserTypeRelation != null)
                {
                    // 如果存在记录，更新用户类型
                    existingUserTypeRelation.UserType = updateDto.UserType;
                    existingUserTypeRelation.UpdateBy = _userContext.UserId;
                    existingUserTypeRelation.UpdateName = _userContext.UserName;
                    existingUserTypeRelation.UpdateTime = DateTime.Now;
                    await _userTypeRelationRepository.UpdateAsync(existingUserTypeRelation);
                }
                else
                {
                    // 如果不存在记录，创建新记录
                    var newUserTypeRelation = new Def_User_Type_Relation
                    {
                        TenantId = existingUser.TenantId,
                        UserId = existingUser.UserId,
                        UserType = updateDto.UserType,
                        CreateBy = _userContext.UserId,
                        CreateName = _userContext.UserName,
                        UpdateBy = _userContext.UserId,
                        UpdateName = _userContext.UserName,
                        IsActive = true
                    };
                    await _userTypeRelationRepository.InsertAsync(newUserTypeRelation);
                }

                // 更新用户角色
                if (updateDto.Roles != null && updateDto.Roles.Any())
                {
                    // 创建角色ID与部门ID的映射字典
                    Dictionary<long, long> roleWithDepartments = new Dictionary<long, long>();
                    // 部门id
                    List<long> departmentIds = new List<long>();
                    foreach (var roleItem in updateDto.Roles)
                    {
                        // 检查是否包含下划线(格式为：部门ID_角色ID)
                        if (roleItem.ToString().Contains("_"))
                        {
                            var parts = roleItem.ToString().Split('_');
                            if (parts.Length == 2 && long.TryParse(parts[0], out long departmentId) && long.TryParse(parts[1], out long roleId))
                            {
                                roleWithDepartments[roleId] = departmentId;
                            }
                        }
                        // 不包含下划线，表示部门id
                        else
                        {
                            departmentIds.Add(Convert.ToInt64(roleItem));
                        }
                    }

                    // 保存用户角色，每个角色使用其对应的部门ID
                    await _userRoleRepository.SaveUserRolesWithDifferentDepartmentsAsync(
                        updateDto.Id,
                        roleWithDepartments,
                        _userContext.UserId,
                        _userContext.UserName,
                        existingUser.TenantId,
                        departmentIds);
                }
                else
                {
                    await _userRoleRepository.DeleteUserRolesAsync(updateDto.Id, existingUser.TenantId);
                }

                // 更新用户标签
                if (updateDto.Tags != null && updateDto.Tags.Any())
                {
                    // 将字符串标签ID转换为长整型
                    List<long> tagIds = updateDto.Tags
                        .Where(t => !string.IsNullOrEmpty(t))
                        .Select(t => Convert.ToInt64(t))
                        .ToList();

                    // 保存用户标签
                    await _userTagRepository.SaveUserTagsAsync(
                        updateDto.Id,
                        tagIds,
                        _userContext.UserId,
                        existingUser.TenantId,
                        _userContext.UserName);
                }
                else
                {
                    await _userTagRelationRepository.UpdateSetColumnsTrueAsync(
                        r => new Def_User_Tag_Relation { IsActive = false },
                        r => r.UserId == updateDto.Id && r.IsActive && r.TenantId == existingUser.TenantId
                    );
                }

                // 提交事务
                _userInfoRepository.CommitTran();
                return true;
            }
            catch (Exception ex)
            {
                // 回滚事务
                _userInfoRepository.RollbackTran();
                UnisLog.Error(ex, "UpdateUserCompleteInfoAsync");
                throw new AgentCentralException("update user error：" + ex.Message);
            }
        }
    }
}