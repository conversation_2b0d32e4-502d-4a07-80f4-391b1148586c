using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Application.Contracts.RequestModels.Dify;
using AgentCentral.Application.Contracts.ResponseModels.Dify;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Entities.Workflow;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.IRepository.Workflow;
using AgentCentral.Domain.Manager;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.EnumUtil;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AgentCentral.Application.Services.Workflow
{
    /// <summary>
    /// Mp_Mcp工作流服务实现
    /// </summary>
    public class MpMcpWorkflowService : IMpMcpWorkflowService
    {
        private readonly IMpMcpRepository _mpMcpRepository;
        private readonly IWorkflowLogRepository _workflowLogRepository;
        private readonly IWorkflowInfoRepository _workflowInfoRepository;
        private readonly WorkflowManager _workFlowManager;
        private readonly UserContext _userContext;
        private readonly IMapper _mapper;
        private readonly ILogger<MpMcpWorkflowService> _logger;
        private readonly DifyClient _difyClient;
        private readonly IAttachmentService _attachmentService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MpMcpWorkflowService(
            IMpMcpRepository mpMcpRepository,
            IWorkflowLogRepository workflowLogRepository,
            IWorkflowInfoRepository workflowInfoRepository,
            WorkflowManager workFlowManager,
            UserContext userContext,
            IMapper mapper,
            DifyClient difyClient,
            IAttachmentService attachmentService,
            ILogger<MpMcpWorkflowService> logger)
        {
            _mpMcpRepository = mpMcpRepository;
            _workflowLogRepository = workflowLogRepository;
            _workflowInfoRepository = workflowInfoRepository;
            _workFlowManager = workFlowManager;
            _userContext = userContext;
            _mapper = mapper;
            _difyClient = difyClient;
            _attachmentService = attachmentService;
            _logger = logger;
        }

        /// <summary>
        /// 审批通过Mp_Mcp
        /// </summary>
        public async Task<bool> ApproveAsync(long mcpId, string comments = null)
        {
            try
            {
                Mp_Mcp mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                if (mcp == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");
                }

                // 验证服务状态
                var validateResult = ValidateOperation(mcp, McpStatusEnum.Published);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }
                // 使用通用方法更新服务状态
                bool updateResult = await UpdateMcpStatusAsync(mcp, McpStatusEnum.Published, comments ?? "MCP service approved");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to approve MCP service");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving Mp_Mcp {McpId}", mcpId);
                throw;
            }
        }

        /// <summary>
        /// 拒绝Mp_Mcp
        /// </summary>
        public async Task<bool> RejectAsync(long mcpId, string comments = null)
        {
            try
            {
                Mp_Mcp mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                if (mcp == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");
                }

                // 验证服务状态
                var validateResult = ValidateOperation(mcp, McpStatusEnum.Rejected);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }

                // 使用通用方法更新服务状态
                bool updateResult = await UpdateMcpStatusAsync(mcp, McpStatusEnum.Rejected, comments ?? "MCP service rejected");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to reject MCP service");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting Mp_Mcp {McpId}", mcpId);
                throw;
            }
        }

        /// <summary>
        /// 获取Mp_Mcp审批历史记录
        /// </summary>
        public async Task<List<MpMcpWorkflowLogDto>> GetWorkflowLogsAsync(long mcpId)
        {
            try
            {
                // 使用WorkflowLog查询，通过BusinessId关联服务ID，Category区分MpMcp类型
                List<WorkflowLog> logs = await _workflowLogRepository.GetListAsync(x =>
                    x.BusinessId == mcpId && x.Category == WorkflowBusinessTypeEnum.MpMcpReview.GetDescription());

                return logs.OrderByDescending(x => x.ApprovalDate)
                    .Select(log => new MpMcpWorkflowLogDto
                    {
                        Id = log.Id,
                        McpId = log.BusinessId,
                        Status = (McpStatusEnum)log.ApprovalStatus,
                        Comments = log.Description,
                        ApprovalUserName = log.ApprovalUserName,
                        ApprovalDate = log.ApprovalDate,
                    }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting workflow logs for Mp_Mcp {McpId}", mcpId);
                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to get workflow logs");
            }
        }

        /// <summary>
        /// 自动审批Mp_Mcp（AI审核）
        /// </summary>
        public async Task<bool> AutoApprovalAsync(long mcpId)
        {
            try
            {
                var mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                if (mcp == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");
                }

                // 验证服务状态
                var validateResult = ValidateOperation(mcp, McpStatusEnum.Pending);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }

                // 使用通用方法更新服务状态
                bool updateResult = await UpdateMcpStatusAsync(mcp,
                    McpStatusEnum.Pending,
                    new MpMcpWorkflowCommentsDto { Comments = "MCP service AutoApproval Start" },
                    mcp.CreateBy,
                    mcp.CreateName);
                if (updateResult)
                {
                    // 执行AI审核逻辑
                    _ = PerformAutoReviewAsync(mcp.Id);
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to complete auto approval");

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing auto approval for Mp_Mcp {McpId}", mcpId);
                throw;
            }
        }

        /// <summary>
        /// 下架Mp_Mcp
        /// </summary>
        public async Task<bool> DelistAsync(long mcpId, string comments = null)
        {
            try
            {
                Mp_Mcp mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                if (mcp == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");
                }

                // 验证服务状态
                var validateResult = ValidateOperation(mcp, McpStatusEnum.Delisted);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }
                // 使用通用方法更新服务状态
                bool updateResult = await UpdateMcpStatusAsync(mcp, McpStatusEnum.Delisted, comments ?? "MCP service delisted");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to delist MCP service");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error delisting Mp_Mcp {McpId}", mcpId);
                throw;
            }
        }

        /// <summary>
        /// 重新上架Mp_Mcp
        /// </summary>
        public async Task<bool> RelistAsync(long mcpId, string comments = null)
        {
            try
            {
                Mp_Mcp mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                if (mcp == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");
                }

                // 验证服务状态
                if (mcp.Status != McpStatusEnum.Delisted)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError,
                        "Only delisted MCP services can be relisted");
                }

                // 使用通用方法更新服务状态
                bool updateResult = await UpdateMcpStatusAsync(mcp, McpStatusEnum.Published, comments ?? "MCP service relisted");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to relist MCP service");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error relisting Mp_Mcp {McpId}", mcpId);
                throw;
            }
        }

        /// <summary>
        /// 批量审批通过Mp_Mcp
        /// </summary>
        public async Task<bool> BatchApproveAsync(List<long> mcpIds, string comments = null)
        {
            if (mcpIds == null || !mcpIds.Any())
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "MCP service IDs cannot be empty");
            }

            if (mcpIds.Count > 100)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Cannot process more than 100 MCP services at once");
            }

            var failedMcps = new List<string>();

            // 逐个调用单个审核方法，确保工作流日志记录完整
            foreach (long mcpId in mcpIds)
            {
                try
                {
                    // 调用单个审批方法
                    bool result = await ApproveAsync(mcpId, comments);
                    if (!result)
                    {
                        failedMcps.Add($"McpId: {mcpId}, Error: Approval failed");
                    }
                }
                catch (Exception ex)
                {
                    failedMcps.Add($"McpId: {mcpId}, Error: {ex.Message}");
                    _logger.LogError(ex, "Error approving MCP service {McpId} in batch operation", mcpId);
                }
            }

            // 如果有失败的服务，抛出异常
            if (failedMcps.Any())
            {
                var errorMessage = string.Join("; ", failedMcps);
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Batch approval failed for some MCP services: {errorMessage}");
            }

            return true;
        }

        /// <summary>
        /// 批量拒绝Mp_Mcp
        /// </summary>
        public async Task<bool> BatchRejectAsync(List<long> mcpIds, string comments = null)
        {
            if (mcpIds == null || !mcpIds.Any())
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "MCP service IDs cannot be empty");
            }

            if (mcpIds.Count > 100)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Cannot process more than 100 MCP services at once");
            }

            var failedMcps = new List<string>();

            // 逐个调用单个审核方法，确保工作流日志记录完整
            foreach (long mcpId in mcpIds)
            {
                try
                {
                    // 调用单个拒绝方法
                    bool result = await RejectAsync(mcpId, comments ?? "Batch rejection");
                    if (!result)
                    {
                        failedMcps.Add($"McpId: {mcpId}, Error: Rejection failed");
                    }
                }
                catch (Exception ex)
                {
                    failedMcps.Add($"McpId: {mcpId}, Error: {ex.Message}");
                    _logger.LogError(ex, "Error rejecting MCP service {McpId} in batch operation", mcpId);
                }
            }

            // 如果有失败的服务，抛出异常
            if (failedMcps.Any())
            {
                var errorMessage = string.Join("; ", failedMcps);
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Batch rejection failed for some MCP services: {errorMessage}");
            }

            return true;
        }

        /// <summary>
        /// 批量下架Mp_Mcp
        /// </summary>
        public async Task<bool> BatchDelistAsync(List<long> mcpIds, string comments = null)
        {
            if (mcpIds == null || !mcpIds.Any())
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "MCP service IDs cannot be empty");
            }

            if (mcpIds.Count > 100)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Cannot process more than 100 MCP services at once");
            }

            var failedMcps = new List<string>();

            foreach (long mcpId in mcpIds)
            {
                try
                {
                    // 调用单个下架方法
                    bool result = await DelistAsync(mcpId, comments ?? "Batch delist");
                    if (!result)
                    {
                        failedMcps.Add($"McpId: {mcpId}, Error: Delist failed");
                    }
                }
                catch (Exception ex)
                {
                    failedMcps.Add($"McpId: {mcpId}, Error: {ex.Message}");
                    _logger.LogError(ex, "Error delisting MCP service {McpId} in batch operation", mcpId);
                }
            }

            // 如果有失败的服务，抛出异常
            if (failedMcps.Any())
            {
                var errorMessage = string.Join("; ", failedMcps);
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Batch delist failed for some MCP services: {errorMessage}");
            }

            return true;
        }

        /// <summary>
        /// 验证操作的有效性
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateOperation(Mp_Mcp mcp, McpStatusEnum targetStatus)
        {
            return targetStatus switch
            {
                McpStatusEnum.Pending => mcp.Status switch
                {
                    McpStatusEnum.NotSubmitted => (true, string.Empty),
                    McpStatusEnum.Rejected => (true, string.Empty),
                    McpStatusEnum.AutoRejected => (true, string.Empty),
                    McpStatusEnum.Delisted => (true, string.Empty),
                    _ => (false, $"Cannot submit MCP service from {mcp.Status} status to Pending")
                },
                McpStatusEnum.AutoApproved => mcp.Status switch
                {
                    McpStatusEnum.Pending => (true, string.Empty),
                    _ => (false, $"Cannot approve MCP service from {mcp.Status} status to AutoApproved")
                },
                McpStatusEnum.AutoRejected => mcp.Status switch
                {
                    McpStatusEnum.Pending => (true, string.Empty),
                    _ => (false, $"Cannot reject MCP service from {mcp.Status} status to AutoRejected")
                },
                McpStatusEnum.Published => mcp.Status switch
                {
                    McpStatusEnum.AutoApproved => (true, string.Empty),
                    McpStatusEnum.AutoRejected => (true, string.Empty),
                    _ => (false, $"Cannot approve MCP service from {mcp.Status} status to Published")
                },
                McpStatusEnum.Rejected => mcp.Status switch
                {
                    McpStatusEnum.AutoApproved => (true, string.Empty),
                    McpStatusEnum.AutoRejected => (true, string.Empty),
                    _ => (false, $"Cannot reject MCP service from {mcp.Status} status to Rejected")
                },
                McpStatusEnum.Delisted => mcp.Status switch
                {
                    McpStatusEnum.Published => (true, string.Empty),
                    _ => (false, $"Cannot delist MCP service from {mcp.Status} status")
                },
                _ => (false, $"Unsupported target status: {targetStatus}")
            };
        }

        #region Private Methods

        /// <summary>
        /// 更新Mp_Mcp状态的通用方法
        /// </summary>
        /// <param name="mcpId">服务ID</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="comments"></param>
        /// <returns>更新是否成功</returns>
        private async Task<bool> UpdateMcpStatusAsync(long mcpId, McpStatusEnum newStatus, string comments)
        {
            var mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
            var updateBy = _userContext.UserId;
            var updateName = _userContext.UserName;
            return await UpdateMcpStatusAsync(mcp, newStatus, new MpMcpWorkflowCommentsDto { Comments = comments }, updateBy, updateName);
        }

        /// <summary>
        /// 更新Mp_Mcp状态的通用方法
        /// </summary>
        /// <param name="mcp">服务实体</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="comments"></param>
        /// <returns>更新是否成功</returns>
        private async Task<bool> UpdateMcpStatusAsync(Mp_Mcp mcp, McpStatusEnum newStatus, string comments)
        {
            var updateBy = _userContext.UserId;
            var updateName = _userContext.UserName;
            return await UpdateMcpStatusAsync(mcp, newStatus, new MpMcpWorkflowCommentsDto { Comments = comments }, updateBy, updateName);
        }

        /// <summary>
        /// 更新Mp_Mcp状态的通用方法
        /// </summary>
        /// <param name="mcp">服务实体</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="comments">备注</param>
        /// <param name="updateBy"></param>
        /// <param name="updateName"></param>
        /// <returns>更新是否成功</returns>
        private async Task<bool> UpdateMcpStatusAsync(Mp_Mcp mcp, McpStatusEnum newStatus, MpMcpWorkflowCommentsDto comments, long updateBy = 0, string updateName = "System")
        {
            try
            {
                // 更新服务状态
                Mp_Mcp update = new Mp_Mcp { Id = mcp.Id, Status = newStatus };

                // 操作人
                update.UpdateBy = updateBy;
                update.UpdateName = updateName;
                update.UpdateTime = DateTime.Now;

                // 保存到数据库
                bool updateResult = await _mpMcpRepository.UpdateSetColumnsTrueAsync(set => new Mp_Mcp()
                {
                    Status = newStatus,
                    UpdateBy = update.UpdateBy,
                    UpdateName = update.UpdateName,
                    UpdateTime = update.UpdateTime
                }, where => where.Id == mcp.Id);
                var commentsJson = JsonConvert.SerializeObject(comments);
                if (updateResult)
                {
                    return await _workFlowManager.WorkflowComplete(mcp.Id, newStatus, WorkflowBusinessTypeEnum.MpMcpReview, commentsJson, updateBy, updateName);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Mp_Mcp {McpId} status to {Status}", mcp.Id, newStatus);
                throw;
            }
        }

        /// <summary>
        /// 执行自动审核逻辑
        /// </summary>
        private async Task PerformAutoReviewAsync(long mcpId)
        {
            try
            {
                var mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                var logoUrl = await _attachmentService.GetAccessUrlByAsync(mcp.LogoUrl);
                var mpAppAutoReviewRequest = new MpMcpAutoReviewRequest
                {
                    ServiceName = mcp.ServiceName,
                    Description = mcp.Category,
                    UseCases = mcp.ServiceType,
                    Logo = DifyFileInput.OfImageUrl(logoUrl)
                };

                var autoReviewResponse = await _difyClient.MpMcpAutoReviewAsync(mpAppAutoReviewRequest);
                await SyncAutoReviewResult(mcpId, autoReviewResponse);
            }
            catch (TaskCanceledException ex)
            {
                // 处理超时异常，标记为自动拒绝并记录超时原因
                string timeoutMessage = "Auto review timed out";
                await UpdateMcpStatusAsync(mcpId, McpStatusEnum.Rejected, timeoutMessage);
                _logger.LogError(ex, "Auto review timeout for Mp_Mcp {McpId}: {ErrorMsg}", mcpId, timeoutMessage);
            }
            catch (Exception ex)
            {
                await UpdateMcpStatusAsync(mcpId, McpStatusEnum.Rejected, ex.Message);
                _logger.LogError(ex, "Error performing auto review for Mp_Mcp {McpId} : {ErrorMsg}", mcpId, ex.Message);
            }
        }

        /// <summary>
        /// 同步自动审核结果
        /// </summary>
        private async Task SyncAutoReviewResult(long mcpId, MpAutoReviewResponse autoReviewResponse)
        {

            bool autoApproved = autoReviewResponse?.IsPass() ?? false;
            McpStatusEnum newStatus = autoApproved ? McpStatusEnum.Published : McpStatusEnum.Rejected;
            var commentsContent = autoApproved ? "AI auto approval passed" : "AI auto approval failed";
            var comments = new MpMcpWorkflowCommentsDto()
            {
                SafetyCheckScore = autoReviewResponse?.SafetyCheckScore ?? 0,
                ComplianceScore = autoReviewResponse?.ComplianceScore ?? 0,
                ContentQualityScore = autoReviewResponse?.ContentQualityScore ?? 0,
                OverallScore = autoReviewResponse?.OverallScore ?? 0,
                Comments = autoReviewResponse?.Reason ?? commentsContent,
                IsPass = autoReviewResponse?.Status ?? false
            };
            Mp_Mcp mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
            await UpdateMcpStatusAsync(mcp, newStatus, comments);
            var autoReviewResult = JsonConvert.SerializeObject(comments);
            await _mpMcpRepository.UpdateSetColumnsTrueAsync(
                set => new Mp_Mcp
                {
                    ReviewResult = autoReviewResult
                },
                where => where.Id == mcp.Id
            );
        }

        #endregion
    }
} 