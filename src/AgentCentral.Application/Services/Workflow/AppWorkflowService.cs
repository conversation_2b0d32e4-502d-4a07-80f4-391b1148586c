using System.Linq.Expressions;
using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.ChangeLog;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.ChangeLogs;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Entities.Workflow;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Manager;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.EnumUtil;
using AgentCentral.Infrastructure.Exceptions;
using EnumsNET;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace AgentCentral.Application.Services.Workflow
{
    public class AppWorkflowService : IAppWorkflowService
    {
        private readonly WorkflowManager _workFlowManager;
        
        private readonly ILogger<AppWorkflowService> _logger;

        private readonly IChangeHistoryService _changeHistoryService;

        private readonly WorkflowClient _workFlowClient;

        private readonly UserContext _userContext;

        private readonly IAppRepository _appRepository;

        private readonly DifyClient _difyClient;

        public static List<string> WorkFlowAgentTypes = ["workflow", "advanced-chat"];

        private readonly AgentCentralClient _agentClient;

        private readonly IBaseRepository<Def_App_Change> _appChangeRepository;

        private readonly IAppService _appService;

        public AppWorkflowService(WorkflowManager workFlowManager,
            WorkflowClient workFlowClient,
            UserContext userContext,
            ILogger<AppWorkflowService> logger,
            IAppRepository appRepository,
            DifyClient difyClient,
            IChangeHistoryService changeHistoryService,
            AgentCentralClient agentClient,
            IBaseRepository<Def_App_Change> appChangeRepository,
            IAppService appService)
        {
            _workFlowManager = workFlowManager;
            _workFlowClient = workFlowClient;
            _userContext = userContext;
            _logger = logger;
            _appRepository = appRepository;
            _difyClient = difyClient;
            _changeHistoryService = changeHistoryService;
            _agentClient = agentClient;
            _appChangeRepository = appChangeRepository;
            _appService = appService;
        }

        /// <summary>
        /// 提交审批
        /// </summary>
        /// <param name="model">审批操作数据传输对象</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SubmitForApprovalAsync(ApprovalActionDto model)
        {
            var result = false;
            if (model.BusinessId > 0)
            {
                var app = await _appRepository.GetFirstAsync(a => a.Id == model.BusinessId);
                if (WorkFlowAgentTypes.Contains(app.AppMode) &&
                app.AppStatus == AppStatusEnum.Unpublished &&
                !app.DifyPublished)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataStatusError, "The agent is not published. Please publish it and then proceed with the submission.");
                }

                var approvalStatus = app.AppStatus;
                if (approvalStatus == AppStatusEnum.Unpublished || approvalStatus == AppStatusEnum.Rejected || approvalStatus == AppStatusEnum.AutoRejected)
                {
                    //workflow启动流程
                    var response = await WorkflowStart(model.BusinessId, model.BusinessType, true);

                    //如果审批流程启动成功
                    if (response != null && (response.Code == 0 || response.Code == 1000))
                    {
                        string taskId = string.Empty, nodeName = string.Empty;
                        if (response.Data.TaskNodeResultList != null && response.Data.TaskNodeResultList.Length > 0)
                        {
                            taskId = response.Data.TaskNodeResultList[0].TaskId;
                            nodeName = response.Data.TaskNodeResultList[0].NodeName;
                        }

                        //保存到当前进度中
                        await _workFlowManager.SaveProgressAsync(new WorkflowInfo
                        {
                            Id = SnowFlakeSingle.Instance.NextId(),
                            BusinessId = model.BusinessId,
                            Category = (int)model.BusinessType,
                            TaskId = taskId,
                            CurrentNodeName = nodeName,
                            RoleIds = string.Empty,
                            UserIds = string.Empty,
                            IsEnd = response.Data.End,
                            TenantId = _userContext.TenantId ?? ""
                        });

                        var status = AppStatusEnum.UnderReview;
                        if (response.Data.End)
                        {
                            status = AppStatusEnum.Published;

                            var appChange = await _appChangeRepository.GetFirstAsync(x => x.AppId == model.BusinessId);
                            if (appChange != null)
                            {
                                await _appService.HandleAppApprovalAsync(model.BusinessId);
                            }
                        }
                        await _workFlowManager.AddWorkFLowLogAsync(model.BusinessId, model.Comments, model.BusinessType, (int)status);
                        await UpdateApplicationStatusAsync(model.BusinessId, status);

                        if (!response.Data.End)
                        {
                            // 如果调用DifyAutoApprovalAsync不等待异步方法完成，会导致ChangLog记录不上，等待异步方法完成，接口耗时会很长
                            // 所以修改为使用httpclient调用自身接口
                            _ = _agentClient.AutoReviewAsync(model.BusinessId);
                        }

                        result = true;
                    }
                    else
                    {
                        //抛出异常，中断无效审批
                        throw new AgentCentralException(ErrorCodeEnum.BusinessError, "Approval process start failed.");
                    }
                }
                else
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, "Only Application in the unpublished status can be submitted to the approval flow.");
                }
            }
            return result;
        }

        public async Task<bool> ApproveAsync(ApprovalActionDto model)
        {
            var isCompleted = false;
            if (model.BusinessId > 0)
            {
                // 审批流程是否已完结
                isCompleted = await WorkflowComplete(model.BusinessId, AppStatusEnum.Published, model.Comments, model.BusinessType);
                if (isCompleted)
                {
                    await UpdateApplicationStatusAsync(model.BusinessId, AppStatusEnum.Published);
                    var appChangeCount = await _appChangeRepository.CountAsync(c => c.AppId == model.BusinessId);
                    if (appChangeCount > 0) // 非首次发布
                    {
                        await _appService.HandleAppApprovalAsync(model.BusinessId);
                    }
                }
            }
            return isCompleted;
        }

        public async Task<bool> RejectAsync(ApprovalActionDto model)
        {
            var result = false;
            if (model.BusinessId > 0)
            {
                await _workFlowManager.AddWorkFLowLogAsync(model.BusinessId, model.Comments, model.BusinessType, (int)AppStatusEnum.Rejected);
                result = await UpdateApplicationStatusAsync(model.BusinessId, AppStatusEnum.Rejected);
            }
            return result;
        }

        public async Task<bool> DelistAsync(ApprovalActionDto model)
        {
            var result = false;
            if (model.BusinessId > 0)
            {
                await _workFlowManager.AddWorkFLowLogAsync(model.BusinessId, model.Comments, model.BusinessType, (int)AppStatusEnum.Delisted);
                result = await UpdateApplicationStatusAsync(model.BusinessId, AppStatusEnum.Delisted);
            }
            return result;
        }

        /// <summary>
        /// 启动审批流
        /// </summary>
        /// <param name="businessId"></param>
        /// <returns></returns>
        /// <exception cref="VRMException"></exception>
        private async Task<StartResponseDto> WorkflowStart(long businessId, WorkflowBusinessTypeEnum businessType, bool isCentralAccount = false)
        {
            //workflow启动流程
            var response = await _workFlowClient.Start(new StartRequestDto()
            {
                BusinessId = businessId.ToString(),
                BusinessNo = businessId.ToString(),
                ProcessInstanceId = string.Empty,
                ProcessName = businessType.GetDescription(),
                UserId = _userContext.UserId,
                UserName = string.IsNullOrWhiteSpace(_userContext.UserName) ? _userContext.UserId.ToString() : _userContext.UserName,
                Variables = new { IsAdmin = isCentralAccount }
            }, _userContext.TenantId);

            return response;
        }

        /// <summary>
        /// 更新审批流
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="status"></param>
        /// <param name="description"></param>
        /// <param name="businessType"></param>
        /// <param name="isSkipManualReview"></param>
        /// <returns></returns>
        /// <exception cref="AgentCentralException"></exception>
        public async Task<bool> WorkflowComplete(long businessId, AppStatusEnum status, string description, WorkflowBusinessTypeEnum businessType, bool isSkipManualReview = false)
        {
            //得到当前审批进度
            int category = (int)businessType;
            var progress = await _workFlowManager.GetProgressAsync(a => a.BusinessId == businessId && a.Category == category) ?? throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, businessId.ToString());

            //workflow更新流程
            var response = await _workFlowClient.Complete(new CompleteRequestDto()
            {
                Comment = string.Empty,
                Plan = string.Empty,
                TaskId = progress.TaskId,
                UserId = _userContext.UserId,
                UserName = string.IsNullOrWhiteSpace(_userContext.UserName)
                    ? _userContext.UserId.ToString()
                    : _userContext.UserName,
                Variables = new { IsSkipManualReview = isSkipManualReview }
            }, Convert.ToString(_userContext.TenantId));

            //如果审批流程启动成功
            if (response != null && (response.Code == 0 || response.Code == 1000))
            {
                string taskId = string.Empty, nodeName = string.Empty;
                if (response.Data.TaskNodeResultList != null && response.Data.TaskNodeResultList.Length > 0)
                {
                    taskId = response.Data.TaskNodeResultList[0].TaskId;
                    nodeName = response.Data.TaskNodeResultList[0].NodeName;
                }

                //更新到当前流程中
                await _workFlowManager.UpdateProgressAsync(new WorkflowInfo
                {
                    BusinessId = businessId,
                    Category = category,
                    TaskId = taskId,
                    CurrentNodeName = nodeName,
                    RoleIds = string.Empty,
                    UserIds = string.Empty,
                    IsEnd = response.Data.End
                });
                //记录日志
                await _workFlowManager.AddWorkFLowLogAsync(businessId, description, businessType, (int)status);
                //如果流程结束，就审批通过
                if (response.Data.End)
                {
                    return true;
                }
            }
            else
            {
                //抛出异常，中断无效审批
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "Approval process start failed.");
            }
            return false;
        }

        public async Task<AppStatusEnum> GetApplicationStatusAsync(long businessId)
        {
            var app = await _appRepository.GetFirstAsync(c => c.Id == businessId);
            return app.AppStatus;
        }

        public async Task<bool> UpdateApplicationStatusAsync(long businessId, AppStatusEnum status)
        {
            using var context = _appRepository.CreateContext();
            try
            {
                var info = await _appRepository.GetFirstAsync(v => v.Id == businessId);
                info.AppStatus = status;
                if (status is AppStatusEnum.UnderReview)
                {
                    info.SubmitDate = DateTime.Now;
                }
                else if (status is AppStatusEnum.Published or AppStatusEnum.Rejected or AppStatusEnum.Delisted)
                {
                    await _workFlowManager.UpdateWorkInfoColumnsAsync(businessId, WorkflowBusinessTypeEnum.AgentReview,
                        x => new WorkflowInfo { CurrentNodeName = status.AsString() });
                }
                else if (status is AppStatusEnum.AutoPublished)
                {
                    info.AppStatus = AppStatusEnum.Published;
                }

                var result = await _appRepository.UpdateAsync(info);
                context.Commit();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Update App Status error message:{Message}", ex.Message);
                throw new AgentCentralException("Update App Status fail");
            }
        }

        public async Task<List<WorkflowLogDto>> GetWorkflowLogAsync(string agentId)
        {
            // 获取应用信息
            Def_App app = await _appRepository.GetFirstAsync(a => a.AppId == agentId) ?? throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found.");

            var businessId = app.Id;

            // 查询该应用的所有工作流日志，按创建时间倒序排序
            Expression<Func<WorkflowLog, bool>> filterExpression = Expressionable.Create<WorkflowLog>()
                .And(x => x.BusinessId == businessId)
                .ToExpression();

            var logs = await _workFlowManager.GetWorkflowLogsAsync(businessId);

            if (logs == null || logs.Count == 0)
            {
                return [];
            }

            // 按状态分组
            var groupedLogs = logs
                .OrderByDescending(l => l.ApprovalDate)
                .ThenByDescending(l => l.Id)
                .Select(x => new WorkflowLogDto()
                {
                    BusinessId = x.BusinessId,
                    Status = (WorkflowAppStatusEnum)x.ApprovalStatus,
                    Comments = x.Description,
                    Id = x.Id,
                    ApprovalUserName = x.ApprovalUserName,
                    ApprovalDate = x.ApprovalDate,
                    ChangedFields = []
                })
                .ToList();

            // 获取应用的变更历史记录
            var changeHistories = await _changeHistoryService.GetChangeLogsAsync(businessId);

            // 获取相邻审批之间的字段变更
            if (groupedLogs.Count > 0)
            {

                // 为每个日志添加字段变更信息
                for (int i = 0; i < groupedLogs.Count - 1; i++)
                {
                    var currentLog = groupedLogs[i];
                    var previousLog = groupedLogs[i + 1];

                    // 查找两次审批之间的变更记录
                    var changes = changeHistories
                        .Where(ch => ch.OpTime >= previousLog.ApprovalDate && ch.OpTime <= currentLog.ApprovalDate)
                        .ToList();

                    currentLog.ChangedFields = await ConvertChanges(changes);
                }

                var unchanges = await ConvertChanges([.. changeHistories.Where(x => x.OpTime > groupedLogs.First().ApprovalDate)]);
                if (unchanges.Count > 0)
                {
                    groupedLogs.Insert(0, new WorkflowLogDto
                    {
                        ApprovalDate = DateTime.Now,
                        Status = WorkflowAppStatusEnum.Unpublished,
                        ApprovalUserName = string.Empty,
                        BusinessId = businessId,
                        Id = 0,
                        ChangedFields = unchanges
                    });
                }

                var lastChanges = await ConvertChanges([.. changeHistories.Where(x => x.OpTime < groupedLogs.Last().ApprovalDate)]);
                if (lastChanges.Count > 0)
                {
                    groupedLogs.Append(new WorkflowLogDto
                    {
                        ApprovalDate = DateTime.Now,
                        Status = WorkflowAppStatusEnum.Created,
                        ApprovalUserName = string.Empty,
                        BusinessId = businessId,
                        Id = 0,
                        ChangedFields = lastChanges
                    });
                }
            }
            else
            {
                var lastChanges = await ConvertChanges([.. changeHistories]);
                if (lastChanges.Count > 0)
                {
                    groupedLogs.Append(new WorkflowLogDto
                    {
                        ApprovalDate = DateTime.Now,
                        Status = WorkflowAppStatusEnum.Created,
                        ApprovalUserName = string.Empty,
                        BusinessId = businessId,
                        Id = 0,
                        ChangedFields = lastChanges
                    });
                }
            }

            return groupedLogs;
        }

        private async Task<List<FieldChangeDto>> ConvertChanges(List<ChangeLogDto> changes)
        {
            var results = new List<FieldChangeDto>();
            changes = [.. changes.OrderByDescending(x => x.OpTime)];
            foreach (var change in changes)
            {
                if (change.TableName == "def_app")
                {
                    if (change.ColumnName == nameof(Def_App.AppName))
                    {
                        results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    }
                    // else if (change.ColumnName == nameof(Def_App.AppCode))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.AppDescription))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.AppSource))
                    // {
                    //     results.Add(new FieldChangeDto
                    //     {
                    //         FieldName = change.ColumnName,
                    //         NewValue = int.TryParse(change.NewValue, out var newValue) ? ((AppSourceEnum)newValue).ToString() : string.Empty,
                    //         OldValue = int.TryParse(change.OldValue, out var oldValue) ? ((AppSourceEnum)oldValue).ToString() : string.Empty,
                    //     });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.AppTag))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.AppIcon))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.AppMode))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.IsActive))
                    // {
                    //     results.Add(new FieldChangeDto
                    //     {
                    //         FieldName = change.ColumnName,
                    //         NewValue = bool.TryParse(change.NewValue, out var newValue) ? (newValue ? "Active" : "InActive") : string.Empty,
                    //         OldValue = bool.TryParse(change.OldValue, out var oldValue) ? (oldValue ? "Active" : "InActive") : string.Empty,
                    //     });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.AppStatus))
                    // {
                    //     results.Add(new FieldChangeDto
                    //     {
                    //         FieldName = change.ColumnName,
                    //         NewValue = int.TryParse(change.NewValue, out var newValue) ? ((AppStatusEnum)newValue).ToString() : string.Empty,
                    //         OldValue = int.TryParse(change.OldValue, out var oldValue) ? ((AppStatusEnum)oldValue).ToString() : string.Empty,
                    //     });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.ComplianceScore))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.ContentQualityScore))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.OverallScore))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.SafetyCheckScore))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.Department))
                    // {
                    //     results.Add(new FieldChangeDto { FieldName = change.ColumnName, NewValue = change.NewValue, OldValue = change.OldValue });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.EnableSite))
                    // {
                    //     results.Add(new FieldChangeDto
                    //     {
                    //         FieldName = change.ColumnName,
                    //         NewValue = bool.TryParse(change.NewValue, out var newValue) ? (newValue ? "Enable" : "Disabled") : string.Empty,
                    //         OldValue = bool.TryParse(change.OldValue, out var oldValue) ? (oldValue ? "Enable" : "Disabled") : string.Empty,
                    //     });
                    // }
                    // else if (change.ColumnName == nameof(Def_App.EnableApi))
                    // {
                    //     results.Add(new FieldChangeDto
                    //     {
                    //         FieldName = change.ColumnName,
                    //         NewValue = bool.TryParse(change.NewValue, out var newValue) ? (newValue ? "Enable" : "Disabled") : string.Empty,
                    //         OldValue = bool.TryParse(change.OldValue, out var oldValue) ? (oldValue ? "Enable" : "Disabled") : string.Empty,
                    //     });
                    // }
                }
            }

            await Task.CompletedTask;
            return results;
        }

        public async Task<bool> DifyAutoApprovalAsync(long businessId)
        {
            var defApp = await _appRepository.GetFirstAsync(v => v.Id == businessId);

            var autoReviewResponse = await _difyClient.AutoReviewAsync(defApp.AppId);
            var autoApproved = false;
            defApp.AppStatus = AppStatusEnum.AutoRejected;
            var rejectReason = string.Empty;
            if (autoReviewResponse?.Data != null)
            {
                autoApproved = autoReviewResponse.Data.Status;
                defApp.AppStatus = autoApproved ? AppStatusEnum.AutoApproved : AppStatusEnum.AutoRejected;
                defApp.ContentQualityScore = autoReviewResponse.Data.ContentQualityScore;
                defApp.SafetyCheckScore = autoReviewResponse.Data.SafetyCheckScore;
                defApp.ComplianceScore = autoReviewResponse.Data.ComplianceScore;
                defApp.OverallScore = autoReviewResponse.Data.OverallScore;
                rejectReason = autoReviewResponse.Data.Reason ?? "";
            }
            else if (autoReviewResponse?.Code == 400)
            {
                rejectReason = autoReviewResponse.Message ?? "";
            }
            await _appRepository.UpdateAsync(defApp);

            var appChange = await _appChangeRepository.GetFirstAsync(c => c.AppId == defApp.Id);
            var isSkipManualReview = appChange != null && appChange.ChangeType == (int)AppChangeTypeEnum.Presentation && autoApproved;
            /*
             * 自动审批通过与否都推往下一个节点
             * 非首次提交、只修改了AgentInfo且自动审批通过，直接结束。其它情况跳转至人工审批
             * 详见workflow平台
             */
            var isCompleted = await WorkflowComplete(businessId, defApp.AppStatus, rejectReason, WorkflowBusinessTypeEnum.AgentReview, isSkipManualReview);
            if (isCompleted)
            {
                await _workFlowManager.AddWorkFLowLogAsync(businessId, "Auto Published", WorkflowBusinessTypeEnum.AgentReview, (int)AppStatusEnum.Published);
                await UpdateApplicationStatusAsync(businessId, AppStatusEnum.AutoPublished);
                await _appService.HandleAppApprovalAsync(businessId);
            }

            return autoApproved;
        }
    }
}
