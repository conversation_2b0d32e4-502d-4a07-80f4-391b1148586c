using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Application.Contracts.RequestModels.Dify;
using AgentCentral.Application.Contracts.ResponseModels.Dify;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Entities.Workflow;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.IRepository.Workflow;
using AgentCentral.Domain.Manager;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using EnumsNET;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AgentCentral.Application.Services.Workflow
{
    /// <summary>
    /// Mp_App工作流服务实现
    /// </summary>
    public class MpAppWorkflowService : IMpAppWorkflowService
    {
        private readonly IMpAppRepository _mpAppRepository;
        private readonly IMpAppReviewService _mpAppReviewService;
        private readonly IWorkflowLogRepository _workflowLogRepository;
        private readonly IWorkflowInfoRepository _workflowInfoRepository;
        private readonly WorkflowManager _workFlowManager;
        private readonly UserContext _userContext;
        private readonly IMapper _mapper;
        private readonly ILogger<MpAppWorkflowService> _logger;
        private readonly DifyClient _difyClient;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MpAppWorkflowService(
            IMpAppRepository mpAppRepository,
            IMpAppReviewService mpAppReviewService,
            IWorkflowLogRepository workflowLogRepository,
            IWorkflowInfoRepository workflowInfoRepository,
            WorkflowManager workFlowManager,
            UserContext userContext,
            IMapper mapper,
            ILogger<MpAppWorkflowService> logger,
            DifyClient difyClient)
        {
            _mpAppRepository = mpAppRepository;
            _mpAppReviewService = mpAppReviewService;
            _workflowLogRepository = workflowLogRepository;
            _workflowInfoRepository = workflowInfoRepository;
            _workFlowManager = workFlowManager;
            _userContext = userContext;
            _mapper = mapper;
            _logger = logger;
            _difyClient = difyClient;
        }

        /// <summary>
        /// 审批通过Mp_App
        /// </summary>
        public async Task<bool> ApproveAsync(long appId, string comments = null)
        {
            try
            {
                Mp_App app = await _mpAppRepository.GetByAppIdAsync(appId);
                if (app == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found");
                }

                // 验证应用状态
                var validateResult = ValidateOperation(app, MpAppStatusEnum.Published);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }
                // 使用通用方法更新应用状态
                bool updateResult = await UpdateAppStatusAsync(app, MpAppStatusEnum.Published, comments ?? "Application approved");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to approve application");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving Mp_App {AppId}", appId);
                throw;
            }
        }

        /// <summary>
        /// 拒绝Mp_App
        /// </summary>
        public async Task<bool> RejectAsync(long appId, string comments = null)
        {
            try
            {
                Mp_App app = await _mpAppRepository.GetByAppIdAsync(appId);
                if (app == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found");
                }

                // 验证应用状态
                var validateResult = ValidateOperation(app, MpAppStatusEnum.Rejected);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }

                // 使用通用方法更新应用状态
                bool updateResult = await UpdateAppStatusAsync(app, MpAppStatusEnum.Rejected, comments ?? "Application rejected");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to reject application");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting Mp_App {AppId}", appId);
                throw;
            }
        }

        /// <summary>
        /// 自动审批Mp_App（AI审核）
        /// </summary>
        public async Task<bool> AutoApprovalAsync(long appId)
        {
            try
            {
                var app = await _mpAppRepository.GetByAppIdAsync(appId);
                if (app == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found");
                }

                // 验证应用状态
                var validateResult = ValidateOperation(app, MpAppStatusEnum.Pending);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }

                // 使用通用方法更新应用状态
                bool updateResult = await UpdateAppStatusAsync(app,
                    MpAppStatusEnum.Pending,
                    new MpAppWorkflowCommentsDto { Comments = "Application AutoApproval Start" },
                    app.UpdateBy,
                    app.UpdateName);
                if (updateResult)
                {
                    // 执行AI审核逻辑
                    _ = PerformAutoReviewAsync(app.Id);
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to complete auto approval");

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing auto approval for Mp_App {AppId}", appId);
                throw;
            }
        }

        /// <summary>
        /// 下架Mp_App
        /// </summary>
        public async Task<bool> DelistAsync(long appId, string comments = null)
        {
            try
            {
                Mp_App app = await _mpAppRepository.GetByAppIdAsync(appId);
                if (app == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found");
                }

                // 验证应用状态
                var validateResult = ValidateOperation(app, MpAppStatusEnum.Delisted);
                if (!validateResult.IsValid)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, validateResult.ErrorMessage);
                }
                // 使用通用方法更新应用状态
                bool updateResult = await UpdateAppStatusAsync(app, MpAppStatusEnum.Delisted, comments ?? "Application delisted");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to delist application");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error delisting Mp_App {AppId}", appId);
                throw;
            }
        }

        /// <summary>
        /// 重新上架Mp_App
        /// </summary>
        public async Task<bool> RelistAsync(long appId, string comments = null)
        {
            try
            {
                Mp_App app = await _mpAppRepository.GetByAppIdAsync(appId);
                if (app == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found");
                }

                // 验证应用状态
                if (app.AppStatus != MpAppStatusEnum.Delisted)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError,
                        "Only delisted applications can be relisted");
                }

                // 使用通用方法更新应用状态
                bool updateResult = await UpdateAppStatusAsync(app, MpAppStatusEnum.Published, comments ?? "Application relisted");
                if (updateResult)
                {
                    return true;
                }

                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to relist application");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error relisting Mp_App {AppId}", appId);
                throw;
            }
        }

        /// <summary>
        /// 批量审批通过Mp_App
        /// </summary>
        public async Task<bool> BatchApproveAsync(List<long> appIds, string comments = null)
        {
            if (appIds == null || !appIds.Any())
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "Application IDs cannot be empty");
            }

            if (appIds.Count > 100)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Cannot process more than 100 applications at once");
            }

            var failedApps = new List<string>();

            // 逐个调用单个审核方法，确保工作流日志记录完整
            foreach (long appId in appIds)
            {
                try
                {
                    // 调用单个审批方法
                    bool result = await ApproveAsync(appId, comments);
                    if (!result)
                    {
                        failedApps.Add($"AppId: {appId}, Error: Rejection failed");
                    }
                }
                catch (Exception ex)
                {
                    failedApps.Add($"AppId: {appId}, Error: {ex.Message}");
                    _logger.LogError(ex, "Error approving application {AppId} in batch operation", appId);
                }
            }

            // 如果有失败的应用，抛出异常
            if (failedApps.Any())
            {
                var errorMessage = string.Join("; ", failedApps);
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Batch approval failed for some applications: {errorMessage}");
            }

            return true;
        }

        /// <summary>
        /// 批量拒绝Mp_App
        /// </summary>
        public async Task<bool> BatchRejectAsync(List<long> appIds, string comments = null)
        {
            if (appIds == null || !appIds.Any())
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "Application IDs cannot be empty");
            }

            if (appIds.Count > 100)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Cannot process more than 100 applications at once");
            }

            var failedApps = new List<string>();

            // 逐个调用单个审核方法，确保工作流日志记录完整
            foreach (long appId in appIds)
            {
                try
                {
                    // 调用单个拒绝方法
                    bool result = await RejectAsync(appId, comments ?? "Batch rejection");
                    if (!result)
                    {
                        failedApps.Add($"AppId: {appId}, Error: Rejection failed");
                    }
                }
                catch (Exception ex)
                {
                    failedApps.Add($"AppId: {appId}, Error: {ex.Message}");
                    _logger.LogError(ex, "Error rejecting application {AppId} in batch operation", appId);
                }
            }

            // 如果有失败的应用，抛出异常
            if (failedApps.Any())
            {
                var errorMessage = string.Join("; ", failedApps);
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Batch rejection failed for some applications: {errorMessage}");
            }

            return true;
        }

        /// <summary>
        /// 批量下架Mp_App
        /// </summary>
        public async Task<bool> BatchDelistAsync(List<long> appIds, string comments = null)
        {
            if (appIds == null || !appIds.Any())
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "Application IDs cannot be empty");
            }

            if (appIds.Count > 100)
            {
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Cannot process more than 100 applications at once");
            }

            var failedApps = new List<string>();

            foreach (long appId in appIds)
            {
                try
                {
                    // 调用单个下架方法
                    bool result = await DelistAsync(appId, comments ?? "Batch delist");
                    if (!result)
                    {
                        failedApps.Add($"AppId: {appId}, Error: Rejection failed");
                    }
                }
                catch (Exception ex)
                {
                    failedApps.Add($"AppId: {appId}, Error: {ex.Message}");
                    _logger.LogError(ex, "Error delisting application {AppId} in batch operation", appId);
                }
            }

            // 如果有失败的应用，抛出异常
            if (failedApps.Any())
            {
                var errorMessage = string.Join("; ", failedApps);
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Batch delist failed for some applications: {errorMessage}");
            }

            return true;
        }

        /// <summary>
        /// 检查应用是否可以执行指定操作
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateOperation(Mp_App app, MpAppStatusEnum newStatus)
        {
            try
            {
                if (app == null)
                {
                    return (false, "Application not found");
                }

                switch (newStatus)
                {
                    case MpAppStatusEnum.Pending:
                        if (app.AppStatus is not (MpAppStatusEnum.NotSubmitted or MpAppStatusEnum.AutoRejected or MpAppStatusEnum.Rejected or MpAppStatusEnum.Delisted))
                        {
                            return (false, "Application must be in NotSubmitted or AutoRejected or Rejected or Delist status for approval");
                        }
                        break;
                    case MpAppStatusEnum.AutoApproved:
                    case MpAppStatusEnum.AutoRejected:
                        if (app.AppStatus != MpAppStatusEnum.Pending)
                        {
                            return (false, "Application must be in Pending status for approval");
                        }
                        break;
                    case MpAppStatusEnum.Published:
                    case MpAppStatusEnum.Rejected:
                        if (app.AppStatus != MpAppStatusEnum.AutoApproved)
                        {
                            return (false, "Application must be in AutoApproved status for AutoApproved");
                        }
                        break;
                    case MpAppStatusEnum.Delisted:
                        if (app.AppStatus != MpAppStatusEnum.Published)
                        {
                            return (false, "Only published applications can be delisted");
                        }
                        break;
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating operation {Operation} for Mp_App {AppId}", newStatus, app.Id);
                return (false, "Validation error occurred");
            }
        }

        #region Private Methods

        /// <summary>
        /// 更新Mp_App状态的通用方法
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="comments"></param>
        /// <param name="updateBy"></param>
        /// <param name="updateName"></param>
        /// <returns>更新是否成功</returns>
        private async Task<bool> UpdateAppStatusAsync(long appId, MpAppStatusEnum newStatus, string comments)
        {
            var app = await _mpAppRepository.GetByAppIdAsync(appId);
            var updateBy = _userContext.UserId;
            var updateName = _userContext.FirstName + " " + _userContext.LastName;
            return await UpdateAppStatusAsync(app, newStatus, new MpAppWorkflowCommentsDto { Comments = comments }, updateBy, updateName);
        }

        /// <summary>
        /// 更新Mp_App状态的通用方法
        /// </summary>
        /// <param name="app">应用实体</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="comments"></param>
        /// <param name="updateBy"></param>
        /// <param name="updateName"></param>
        /// <returns>更新是否成功</returns>
        private async Task<bool> UpdateAppStatusAsync(Mp_App app, MpAppStatusEnum newStatus, string comments)
        {
            var updateBy = _userContext.UserId;
            var updateName = _userContext.FirstName + " " + _userContext.LastName;
            return await UpdateAppStatusAsync(app, newStatus, new MpAppWorkflowCommentsDto { Comments = comments }, updateBy, updateName);
        }

        /// <summary>
        /// 更新Mp_App状态的通用方法
        /// </summary>
        /// <param name="app">应用实体</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="comments">备注</param>
        /// <param name="updateBy"></param>
        /// <param name="updateName"></param>
        /// <returns>更新是否成功</returns>
        private async Task<bool> UpdateAppStatusAsync(Mp_App app, MpAppStatusEnum newStatus, MpAppWorkflowCommentsDto comments, long updateBy = 0, string updateName = "System")
        {
            try
            {
                // 更新应用状态
                Mp_App update = new Mp_App { Id = app.Id, AppStatus = newStatus };

                // 操作人
                update.UpdateBy = updateBy;
                update.UpdateName = updateName;
                update.UpdateTime = DateTime.Now;

                // 保存到数据库
                bool updateResult = await _mpAppRepository.UpdateSetColumnsTrueAsync(set => new Mp_App()
                {
                    AppStatus = newStatus,
                    UpdateBy = update.UpdateBy,
                    UpdateName = update.UpdateName,
                    UpdateTime = update.UpdateTime
                }, where => where.Id == app.Id);
                var commentsJson = JsonConvert.SerializeObject(comments);
                if (updateResult)
                {
                    //保存到当前进度中
                    await _workFlowManager.SaveOrUpdateProgressAsync(new WorkflowInfo
                    {
                        BusinessId = app.Id,
                        Category = (int)WorkflowBusinessTypeEnum.MpAppReview,
                        TaskId = "-1",
                        CurrentNodeName = newStatus.AsString(),
                        CurrentNodeDescription = commentsJson,
                        RoleIds = string.Empty,
                        UserIds = string.Empty,
                        TenantId = _userContext.TenantId ?? ""
                    });

                    // 记录工作流日志
                    await _workFlowManager.AddWorkFLowLogAsync(app.Id, commentsJson, WorkflowBusinessTypeEnum.MpAppReview, (int) newStatus, updateBy, updateName);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Mp_App {AppId} status to {Status}", app.Id, newStatus);
                throw;
            }
        }

        /// <summary>
        /// 执行自动审核逻辑
        /// </summary>
        private async Task PerformAutoReviewAsync(long appId)
        {
            try
            {
                var appDetail = await _mpAppReviewService.GetAppDetailAsync(appId);
                MpAppAutoReviewRequest mpAppAutoReviewRequest = new MpAppAutoReviewRequest
                {
                    AppName = appDetail.AppName,
                    ShortDescription = appDetail.ShortDescription,
                    FullDescription = appDetail.FullDescription,
                    AppIcon = DifyFileInput.OfImageUrl(appDetail.AppIcon),
                    DeveloperName = appDetail.DeveloperName,
                    DeveloperContact = appDetail.DeveloperContact,
                    PrivacyPolicy = appDetail.PrivacyPolicyDocumentType == PrivacyPolicyDocumentTypeEnum.Document? DifyFileInput.OfDocUrl(appDetail.PrivacyPolicyUrl) : null,
                    Screenshot = appDetail.Screenshots.Select(DifyFileInput.OfImageUrl).ToArray(),
                };

                var autoReviewResponse = await _difyClient.MpAppAutoReviewAsync(mpAppAutoReviewRequest);
                await SyncAutoReviewResult(appId, autoReviewResponse);
            }
            catch (TaskCanceledException ex)
            {
                // 处理超时异常，标记为自动拒绝并记录超时原因
                string timeoutMessage = "Auto review timed out";
                await UpdateAppStatusAsync(appId, MpAppStatusEnum.AutoRejected, timeoutMessage);
                _logger.LogError(ex, "Auto review timeout for Mp_App {AppId}: {ErrorMsg}", appId, timeoutMessage);
            }
            catch (Exception ex)
            {
                await UpdateAppStatusAsync(appId, MpAppStatusEnum.AutoRejected, ex.Message);
                _logger.LogError(ex, "Error performing auto review for Mp_App {AppId} : {ErrorMsg}", appId, ex.Message);
            }
        }

        private async Task SyncAutoReviewResult(long appId, MpAutoReviewResponse autoReviewResponse)
        {
            bool autoApproved = autoReviewResponse?.IsPass() ?? false;
            MpAppStatusEnum newStatus = autoApproved ? MpAppStatusEnum.AutoApproved : MpAppStatusEnum.AutoRejected;
            var commentsContent = autoApproved ? "AI auto approval passed" : "AI auto approval failed";
            var comments = new MpAppWorkflowCommentsDto
            {
                SafetyCheckScore = autoReviewResponse?.SafetyCheckScore ?? 0,
                ComplianceScore = autoReviewResponse?.ComplianceScore ?? 0,
                ContentQualityScore = autoReviewResponse?.ContentQualityScore ?? 0,
                OverallScore = autoReviewResponse?.OverallScore ?? 0,
                Comments = autoReviewResponse?.Reason ?? commentsContent,
                IsPrivacyAnalysis = autoReviewResponse?.IsPrivacyAnalysisPass() ?? true,
            };
            var app = await _mpAppRepository.GetByAppIdAsync(appId);
            await UpdateAppStatusAsync(app, newStatus, comments);
            await _mpAppRepository.UpdateSetColumnsTrueAsync(
                set => new Mp_App
                {
                    SafetyCheckScore = comments.SafetyCheckScore,
                    ComplianceScore = comments.ComplianceScore,
                    ContentQualityScore = comments.ContentQualityScore,
                    OverallScore = comments.OverallScore,
                    IsPrivacyAnalysis = comments.IsPrivacyAnalysis,
                    IsFileAnalysis = comments.IsFileAnalysis,
                },
                where => where.Id == app.Id
            );
        }

        #endregion
    }
}