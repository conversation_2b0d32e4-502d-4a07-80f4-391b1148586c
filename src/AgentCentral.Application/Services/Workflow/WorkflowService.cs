using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.ChangeLog;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.ChangeLogs;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Entities.Workflow;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Manager;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.EnumUtil;
using AgentCentral.Infrastructure.Exceptions;
using SqlSugar;
using System.Linq.Expressions;

namespace AgentCentral.Application.Services.Workflow
{
    public class WorkflowService : IWorkflowService
    {
        private readonly WorkflowManager _workFlowManager;


        private readonly WorkflowClient _workFlowClient;

        private readonly UserContext _userContext;

        private readonly IAppService _appService;

        public WorkflowService(WorkflowManager workFlowManager,
            WorkflowClient workFlowClient,
            UserContext userContext,
            IAppRepository appRepository,
            DifyClient difyClient,
            IChangeHistoryService changeHistoryService,
            AgentCentralClient agentClient,
            IBaseRepository<Def_App_Change> appChangeRepository,
            IAppService appService)
        {
            _workFlowManager = workFlowManager;
            _workFlowClient = workFlowClient;
            _userContext = userContext;
            _appService = appService;
        }

        /// <summary>
        /// 启动审批流
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="businessType"></param>
        /// <param name="variables"></param>
        /// <param name="status"></param>
        /// <param name="description"></param>
        /// <returns></returns>
        public async Task<StartResponseDto> WorkflowStart(long businessId, WorkflowBusinessTypeEnum businessType, object variables, int status = -1,  string description = null)
        {
            //workflow启动流程
            var response = await _workFlowClient.Start(new StartRequestDto()
            {
                BusinessId = businessId.ToString(),
                BusinessNo = businessId.ToString(),
                ProcessInstanceId = string.Empty,
                ProcessName = businessType.GetDescription(),
                UserId = _userContext.UserId,
                UserName = string.IsNullOrWhiteSpace(_userContext.UserName) ? _userContext.UserId.ToString() : _userContext.UserName,
                Variables = variables
            }, _userContext.TenantId);

            //如果审批流程启动成功
            if (response != null && (response.Code == 0 || response.Code == 1000))
            {
                string taskId = string.Empty, nodeName = string.Empty;
                if (response.Data.TaskNodeResultList != null && response.Data.TaskNodeResultList.Length > 0)
                {
                    taskId = response.Data.TaskNodeResultList[0].TaskId;
                    nodeName = response.Data.TaskNodeResultList[0].NodeName;
                }

                if (status >= 0)
                {
                    await _workFlowManager.AddWorkFLowLogAsync(businessId, description, businessType, status);
                }
                //保存到当前进度中
                await _workFlowManager.SaveProgressAsync(new WorkflowInfo
                {
                    Id = SnowFlakeSingle.Instance.NextId(),
                    BusinessId = businessId,
                    Category = (int)businessType,
                    TaskId = taskId,
                    CurrentNodeName = nodeName,
                    RoleIds = string.Empty,
                    UserIds = string.Empty,
                    IsEnd = response.Data.End,
                    TenantId = _userContext.TenantId ?? ""
                });
            }
            return response;
        }

        /// <summary>
        /// 更新审批流
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="status"></param>
        /// <param name="description"></param>
        /// <param name="variables"></param>
        /// <param name="businessType"></param>
        /// <returns></returns>
        /// <exception cref="AgentCentralException"></exception>
        public async Task<bool> WorkflowComplete(long businessId, int status, string description, object variables, WorkflowBusinessTypeEnum businessType)
        {
            //得到当前审批进度
            int category = (int)businessType;
            var progress = await _workFlowManager.GetProgressAsync(a => a.BusinessId == businessId && a.Category == category) ?? throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, businessId.ToString());

            //workflow更新流程
            var response = await _workFlowClient.Complete(new CompleteRequestDto()
            {
                Comment = string.Empty,
                Plan = string.Empty,
                TaskId = progress.TaskId,
                UserId = _userContext.UserId,
                UserName = string.IsNullOrWhiteSpace(_userContext.UserName)
                    ? _userContext.UserId.ToString()
                    : _userContext.UserName,
                Variables = variables
            }, Convert.ToString(_userContext.TenantId));

            //如果审批流程启动成功
            if (response != null && (response.Code == 0 || response.Code == 1000))
            {
                string taskId = string.Empty, nodeName = string.Empty;
                if (response.Data.TaskNodeResultList != null && response.Data.TaskNodeResultList.Length > 0)
                {
                    taskId = response.Data.TaskNodeResultList[0].TaskId;
                    nodeName = response.Data.TaskNodeResultList[0].NodeName;
                }

                //更新到当前流程中
                await _workFlowManager.UpdateProgressAsync(new WorkflowInfo
                {
                    BusinessId = businessId,
                    Category = category,
                    TaskId = taskId,
                    CurrentNodeName = nodeName,
                    RoleIds = string.Empty,
                    UserIds = string.Empty,
                    IsEnd = response.Data.End
                });
                //记录日志
                await _workFlowManager.AddWorkFLowLogAsync(businessId, description, businessType, (int)status);
                //如果流程结束，就审批通过
                if (response.Data.End)
                {
                    return true;
                }
            }
            else
            {
                //抛出异常，中断无效审批
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "Approval process start failed.");
            }
            return false;
        }

        public async Task<List<WorkflowLogDto>> GetWorkflowLogAsync(long businessId)
        {

            var logs = await _workFlowManager.GetWorkflowLogsAsync(businessId);

            if (logs == null || logs.Count == 0)
            {
                return [];
            }

            var groupedLogs = logs
                .OrderByDescending(l => l.ApprovalDate)
                .Select(x => new WorkflowLogDto()
                {
                    BusinessId = x.BusinessId,
                    WorkflowStatus = x.ApprovalStatus,
                    Comments = x.Description,
                    Id = x.Id,
                    ApprovalUserName = x.ApprovalUserName,
                    ApprovalDate = x.ApprovalDate,
                    ChangedFields = []
                })
                .ToList();

            return groupedLogs;
        }

    }
}
