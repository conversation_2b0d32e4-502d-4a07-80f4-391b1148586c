using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.IService;
using AgentCentral.Application.Contracts.RequestModels.IAM;
using AgentCentral.Application.Contracts.ResponseModels.IAM;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.ExcelUtil;
using AgentCentral.Infrastructure.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;

namespace AgentCentral.Application.Services
{
    public class IamService : IIamService
    {
        private readonly IamClient _iamClient;
        private readonly IConfiguration _configuration;
        private readonly IBaseRepository<Def_User_Info> _userInfoRepository;

        public IamService(IamClient iamClient, IConfiguration configuration, IBaseRepository<Def_User_Info> userInfoRepository)
        {
            _iamClient = iamClient;
            _configuration = configuration;
            _userInfoRepository = userInfoRepository;
        }
        
        /// <summary>
        /// 启用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>基础响应</returns>
        public async Task<BaseResponse> ActivateUserAsync(string userId, string token)
        {
            
            // 调用客户端启用用户
            return await _iamClient.ActivateUserAsync(token, userId);
        }

        /// <summary>
        /// 禁用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>基础响应</returns>
        public async Task<BaseResponse> DeactivateUserAsync(string userId, string token)
        {
            
            // 调用客户端禁用用户
            return await _iamClient.DeactivateUserAsync(token, userId);
        }

        public async Task<TenantInfoResponse> GetTenantInfoAsync(string token, List<string> tenantIds)
        {
            TenantInfoRequest tenantInfoRequest = new TenantInfoRequest();
            tenantInfoRequest.codes = tenantIds;

            return await _iamClient.GetTenantInfoAsync(token, tenantInfoRequest);
        }

        public async Task<object> TokenByCodeAsync(string code, string redirectUri)
        {
            return await _iamClient.GetTokenAsync(code, redirectUri);
        }

        public async Task<(bool isValid, TokenValidatedInfoOutputDto tokenInfo)> ValidateTokenAsync(string token)
        {
            try
            {
                // ParseJwtToken方法已经包含了token基础验证（包括时间、签名、issuer等）
                IamnAuthenticationDataResponse authenticationDataResponse = await ParseJwtToken(token);

                IamResponseBase<UserInfoResponse> userInfo = new();
                string userJosn = await _iamClient.GetUserInfoAsync(token);
                userInfo = JsonConvert.DeserializeObject<IamResponseBase<UserInfoResponse>>(userJosn) ?? new IamResponseBase<UserInfoResponse>();
                if (!userInfo.Success)
                {
                    throw new Exception(userInfo.Msg);
                }

                // 查找是否存在该用户
                Def_User_Info existingUser = await _userInfoRepository.GetFirstAsync(u => u.UserId == userInfo.Data.Id.ParseToLong() && u.IsActive);

                if (existingUser != null)
                {
                    // 更新用户信息
                    existingUser.TenantId = authenticationDataResponse.tenant_id;
                    existingUser.UserName = userInfo.Data.UserName;
                    existingUser.IsActive = true;
                    existingUser.UpdateBy = userInfo.Data.Id.ParseToLong();
                    _ = await _userInfoRepository.UpdateAsync(existingUser);
                }
                else
                {
                    // 新增用户信息
                    Def_User_Info newUser = new()
                    {
                        TenantId = authenticationDataResponse.tenant_id,
                        UserId = userInfo.Data.Id.ParseToLong(),
                        UserName = userInfo.Data.UserName,
                        RegisterTime = userInfo.Data.CreatedAt,
                        UserProfile = string.Empty,
                        IsActive = true
                    };
                    _ = await _userInfoRepository.InsertAsync(newUser);
                }
                // 构建用户信息
                TokenValidatedInfoOutputDto tokenInfo = new()
                {
                    UserId = authenticationDataResponse.user_id,
                    UserName = authenticationDataResponse.user_name,
                    FirstName = userInfo.Data.FirstName,
                    LastName = userInfo.Data.LastName,
                    TenantId = authenticationDataResponse.tenant_id,
                    ClientId = authenticationDataResponse.company_code,
                    UserType = userInfo.Data.UserTags.Contains("10002") ? UserTypeEnum.Admin : UserTypeEnum.User, // 根据实际业务设置用户类型 10001 - employee、10002 - admin、10003 - supervisor、10004 - dispatcher 、10005 - employee_driver、10006 - csr、10007 - accounting、10008 - master_contractor、10009 - sub_contractor、10010 - Individual
                    ValidationVersion = null,
                    CreateTime = userInfo.Data.CreatedAt,
                    Token = token
                };
                return await Task.FromResult((true, tokenInfo));
            }
            catch (Exception ex)
            {
                throw new AgentCentralException(ErrorCodeEnum.LoginFail, ex.Message);
            }
        }

        private async Task<IamnAuthenticationDataResponse> ParseJwtToken(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                throw new AgentCentralException(ErrorCodeEnum.LoginFail, "Token is empty");
            }
            string jwtsJson = await _iamClient.GetJwtsAsync();
            if (string.IsNullOrEmpty(jwtsJson))
            {
                throw new AgentCentralException(ErrorCodeEnum.LoginFail, "configuration mismatch");
            }
            JsonWebKeySet jwks = new(jwtsJson);
            if (jwks == null)
            {
                throw new AgentCentralException(ErrorCodeEnum.LoginFail, "Authentication fail");
            }
            JsonWebKey jwk = jwks.Keys.First();

            TokenValidationParameters validationParameters = new()
            {
                IssuerSigningKey = jwk,
                ValidateIssuerSigningKey = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                RequireExpirationTime = false,
            };

            JwtSecurityTokenHandler tokenHandler = new();
            _ = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

            if (validatedToken is JwtSecurityToken jwtToken)
            {
                Claim jwt_data = jwtToken.Claims.FirstOrDefault(f => f.Type == "data");
                if (jwt_data != null)
                {
                    IamnAuthenticationDataResponse jwt_data_value = JsonConvert.DeserializeObject<IamnAuthenticationDataResponse>(jwt_data.Value);
                    return jwt_data_value;
                }
            }
            throw new AgentCentralException(ErrorCodeEnum.LoginFail, "Authentication fail");
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="request">更新用户请求</param>
        /// <returns>更新用户响应</returns>
        public async Task<UpdateUserResponse> UpdateUserAsync(string userId, UpdateUserRequest request, string token)
        {
            
            // 调用客户端更新用户
            return await _iamClient.UpdateUserAsync(token, userId, request);
        }


        public class IamnAuthenticationDataResponse
        {
            public long user_id { get; set; }

            public string user_name { get; set; }

            public string company_code { get; set; }

            //public int ExpiresIn { get; set; }

            public string tenant_id { get; set; }

            //public string AccountId { get; set; }

            //public string GrantType { get; set; }

            //public string ClientType { get; set; }

            //public List<string> RoleIds { get; set; }

            //public string AccessToken { get; set; }
        }

        public class IamResponseBase<T> where T : class
        {
            public int Code { get; set; }

            public T Data { get; set; }

            public string Msg { get; set; } = "";

            public bool Success { get; set; }
        }

        public class UserInfoResponse
        {
            [JsonProperty("id")]
            public string Id { get; set; } = "";

            [JsonProperty("accountId")]
            public string AccountId { get; set; } = "";

            [JsonProperty("companyCode")]
            public string CompanyCode { get; set; } = "";

            [JsonProperty("email")]
            public string Email { get; set; } = "";

            [JsonProperty("createdAt")]
            public DateTime CreatedAt { get; set; }

            [JsonProperty("userTags")]
            public List<string> UserTags { get; set; } = [];

            [JsonProperty(propertyName: "firstName")]
            public string FirstName { get; set; }

            [JsonProperty(propertyName: "lastName")]
            public string LastName { get; set; }

            [JsonProperty(propertyName: "userName")]
            public string UserName { get; set; }

        }
    }
}
