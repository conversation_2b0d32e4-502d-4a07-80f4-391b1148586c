using System.Linq.Expressions;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IService;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// 角色服务实现
    /// </summary>
    public class RoleService : IRoleService, IScopedService
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IMapper _mapper;
        private readonly IIamService _iamService;

        public RoleService(IRoleRepository roleRepository, IDepartmentRepository departmentRepository, IUserRoleRepository userRoleRepository, IMapper mapper,
            IIamService iamService)
        {
            _roleRepository = roleRepository;
            _departmentRepository = departmentRepository;
            _userRoleRepository = userRoleRepository;
            _mapper = mapper;
            _iamService = iamService;
        }

        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <param name="keyword">关键字查询</param>
        /// <param name="departmentId">部门ID</param>
        /// <returns>角色列表</returns>
        public async Task<List<RoleDto>> GetRolesAsync(string tenantId, string keyword = null, long? departmentId = null)
        {
            Expression<Func<Def_Role, bool>> filter = Expressionable.Create<Def_Role>()
                .And(r => r.IsActive == true)
                .AndIF(!string.IsNullOrEmpty(tenantId), r => r.TenantId == tenantId)
                .AndIF(!string.IsNullOrWhiteSpace(keyword), r => r.RoleName.Contains(keyword) || r.RoleCode.Contains(keyword))
                .AndIF(departmentId.HasValue, r => r.DepartmentId == departmentId.Value)
                .ToExpression();

            List<Def_Role> roles = await _roleRepository.GetListAsync(filter);
            List<RoleDto> roleDtos = _mapper.Map<List<RoleDto>>(roles);


            // 去除RoleName重复的角色，只保留每个角色名称的第一个实例
            roleDtos = roleDtos.GroupBy(r => r.RoleName)
                              .Select(g => g.First())
                              .ToList();

            // 获取部门名称
            if (roleDtos.Any())
            {
                List<long> departmentIds = roleDtos.Where(r => r.DepartmentId.HasValue).Select(r => r.DepartmentId.Value).Distinct().ToList();
                if (departmentIds.Any())
                {
                    List<Def_Department> departments = await _departmentRepository.GetListAsync(d => departmentIds.Contains(d.Id));
                    foreach (RoleDto roleDto in roleDtos)
                    {
                        if (roleDto.DepartmentId.HasValue)
                        {
                            Def_Department department = departments.FirstOrDefault(d => d.Id == roleDto.DepartmentId.Value);
                            if (department != null)
                            {
                                roleDto.DepartmentName = department.DepartmentName;
                            }
                        }
                    }
                }
            }

            return roleDtos;
        }

        /// <summary>
        /// 分页获取角色列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页角色列表</returns>
        public async Task<PageModelDto<RoleDto>> GetRolePageListAsync(RoleSearchDto searchDto)
        {
            Expression<Func<Def_Role, bool>> filter = Expressionable.Create<Def_Role>()
                .And(r => r.TenantId == searchDto.TenantId)
                .And(r => r.IsActive == true)
                .AndIF(!string.IsNullOrWhiteSpace(searchDto.Keyword), r => r.RoleName.Contains(searchDto.Keyword) || r.RoleCode.Contains(searchDto.Keyword))
                .AndIF(searchDto.DepartmentId.HasValue, r => r.DepartmentId == searchDto.DepartmentId)
                .AndIF(searchDto.IsSystem.HasValue, r => r.IsSystem == searchDto.IsSystem)
                .ToExpression();

            (List<Def_Role> roles, int total) = await _roleRepository.GetPageListAsync(
                filter,
                searchDto.PageIndex,
                searchDto.PageSize,
                r => r.SortOrder,
                false
            );

            List<RoleDto> roleDtos = _mapper.Map<List<RoleDto>>(roles);

            // 获取部门名称
            if (roleDtos.Any())
            {
                List<long> departmentIds = roleDtos.Where(r => r.DepartmentId.HasValue && r.DepartmentId > 0).Select(r => r.DepartmentId.Value).Distinct().ToList();
                if (departmentIds.Any())
                {
                    List<Def_Department> departments = await _departmentRepository.GetListAsync(d => departmentIds.Contains(d.Id));
                    foreach (RoleDto roleDto in roleDtos)
                    {
                        if (roleDto.DepartmentId.HasValue)
                        {
                            Def_Department department = departments.FirstOrDefault(d => d.Id == roleDto.DepartmentId.Value);
                            if (department != null)
                            {
                                roleDto.DepartmentName = department.DepartmentName;
                            }
                        }
                    }
                }
            }

            return new PageModelDto<RoleDto>(searchDto.PageIndex, searchDto.PageSize, roleDtos, total);
        }

        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="roleDto">角色信息</param>
        /// <returns>角色ID</returns>
        public async Task<long> CreateRoleAsync(RoleDto roleDto)
        {
            // 检查角色编码是否已存在
            bool exists = await _roleRepository.IsRoleCodeExistsAsync(roleDto.RoleName, roleDto.TenantId, roleDto.DepartmentId);
            if (exists)
            {
                throw new AgentCentralException("角色编码已存在");
            }

            // 检查部门是否存在
            if (roleDto.DepartmentId.HasValue && roleDto.DepartmentId > 0)
            {
                Def_Department department = await _departmentRepository.GetByIdAsync(roleDto.DepartmentId.Value);
                if (department == null)
                {
                    throw new AgentCentralException("所选部门不存在");
                }
            }

            Def_Role role = _mapper.Map<Def_Role>(roleDto);
            role.Company = roleDto.TenantId;
            await _roleRepository.InsertAsync(role);
            return role.Id;
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="roleDto">角色信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateRoleAsync(RoleDto roleDto)
        {
            if (!roleDto.Id.HasValue)
            {
                throw new AgentCentralException("角色ID不能为空");
            }

            // 检查角色编码是否已存在
            bool exists = await _roleRepository.IsRoleCodeExistsAsync(roleDto.RoleName, roleDto.TenantId, roleDto.DepartmentId, roleDto.Id);
            if (exists)
            {
                throw new AgentCentralException("角色名称已存在");
            }

            // 检查部门是否存在
            if (roleDto.DepartmentId.HasValue)
            {
                Def_Department department = await _departmentRepository.GetByIdAsync(roleDto.DepartmentId.Value);
                if (department == null)
                {
                    throw new AgentCentralException("所选部门不存在");
                }
            }

            Def_Role role = await _roleRepository.GetByIdAsync(roleDto.Id.Value);
            if (role == null)
            {
                throw new AgentCentralException("角色不存在");
            }

            // 系统角色不允许修改编码
            if (role.IsSystem && role.RoleCode != roleDto.RoleCode)
            {
                throw new AgentCentralException("系统角色不允许修改编码");
            }

            _mapper.Map(roleDto, role);
            return await _roleRepository.UpdateAsync(role);
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteRoleAsync(long id)
        {
            Def_Role role = await _roleRepository.GetByIdAsync(id);
            if (role == null)
            {
                throw new AgentCentralException("角色不存在");
            }

            // 系统角色不允许删除
            if (role.IsSystem)
            {
                throw new AgentCentralException("系统角色不允许删除");
            }

            try
            {
                _roleRepository.BeginTran();

                // 删除关联的用户角色记录
                Expression<Func<Def_User_Role, bool>> filter = ur => ur.RoleId == id;
                await _userRoleRepository.DeleteAsync(filter);

                // 逻辑删除角色
                role.IsActive = false;
                bool result = await _roleRepository.UpdateAsync(role);

                _roleRepository.CommitTran();
                return result;
            }
            catch (Exception ex)
            {
                _roleRepository.RollbackTran();
                throw new AgentCentralException($"删除角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取角色详情
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>角色信息</returns>
        public async Task<RoleDto> GetRoleAsync(long id)
        {
            Def_Role role = await _roleRepository.GetByIdAsync(id);
            if (role == null)
            {
                throw new AgentCentralException("角色不存在");
            }

            RoleDto roleDto = _mapper.Map<RoleDto>(role);

            // 获取部门名称
            if (role.DepartmentId.HasValue)
            {
                Def_Department department = await _departmentRepository.GetByIdAsync(role.DepartmentId.Value);
                if (department != null)
                {
                    roleDto.DepartmentName = department.DepartmentName;
                }
            }

            return roleDto;
        }

        /// <summary>
        /// 根据部门ID获取角色列表
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>角色列表</returns>
        public async Task<List<RoleDto>> GetRolesByDepartmentAsync(long departmentId, string tenantId)
        {
            List<Def_Role> roles = await _roleRepository.GetRolesByDepartmentAsync(departmentId, tenantId);
            List<RoleDto> roleDtos = _mapper.Map<List<RoleDto>>(roles);

            // 设置部门名称
            Def_Department department = await _departmentRepository.GetByIdAsync(departmentId);
            if (department != null)
            {
                foreach (RoleDto roleDto in roleDtos)
                {
                    roleDto.DepartmentName = department.DepartmentName;
                }
            }

            return roleDtos;
        }

        /// <summary>
        /// 检查角色编码是否已存在
        /// </summary>
        /// <param name="roleCode">角色编码</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsRoleCodeExistsAsync(string tenantId, string roleCode, long? departmentId = null, long? excludeId = null)
        {
            return await _roleRepository.IsRoleCodeExistsAsync(tenantId, roleCode, departmentId, excludeId);
        }

        /// <summary>
        /// 获取部门角色树形结构
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <param name="keyword">关键字查询</param>
        /// <returns>部门角色树形结构</returns>
        public async Task<List<DepartmentRoleTreeDto>> GetDepartmentRoleTreeAsync(string tenantId, string keyword = null)
        {
            // 获取所有部门
            Expression<Func<Def_Department, bool>> departmentFilter = Expressionable.Create<Def_Department>()
                .AndIF(!string.IsNullOrWhiteSpace(tenantId), d => d.TenantId == tenantId)
                .And(d => d.IsActive == true)
                .AndIF(!string.IsNullOrWhiteSpace(keyword), d => d.DepartmentName.Contains(keyword) || d.DepartmentCode.Contains(keyword))
                .ToExpression();

            List<Def_Department> departments = await _departmentRepository.GetListAsync(departmentFilter, "SortOrder");

            // 获取所有角色
            Expression<Func<Def_Role, bool>> roleFilter = Expressionable.Create<Def_Role>()
                .AndIF(!string.IsNullOrWhiteSpace(tenantId), r => r.TenantId == tenantId)
                .And(r => r.IsActive == true)
                .AndIF(!string.IsNullOrWhiteSpace(keyword), r => r.RoleName.Contains(keyword) || r.RoleCode.Contains(keyword))
                .ToExpression();

            List<Def_Role> roles = await _roleRepository.GetListAsync(roleFilter, "SortOrder");

            // 构建部门-角色树形结构
            List<DepartmentRoleTreeDto> treeList = new List<DepartmentRoleTreeDto>();

            foreach (Def_Department department in departments)
            {
                DepartmentRoleTreeDto treeNode = new DepartmentRoleTreeDto
                {
                    Id = department.Id,
                    Label = department.DepartmentName,
                    DepartmentCode = department.DepartmentCode,
                    Children = new List<RoleNodeDto>()
                };

                // 获取当前部门下的所有角色
                List<Def_Role> departmentRoles = roles
                    .Where(r => r.DepartmentId == department.Id)
                    .ToList();

                // 添加角色节点
                foreach (Def_Role role in departmentRoles)
                {
                    RoleNodeDto roleNode = new RoleNodeDto
                    {
                        Id = role.Id,
                        Label = role.RoleName,
                        RoleCode = role.RoleCode,
                        DepartmentId = department.Id
                    };

                    treeNode.Children.Add(roleNode);
                }

                treeList.Add(treeNode);
            }

            // 添加未分配部门的角色
            List<Def_Role> unassignedRoles = roles
                .Where(r => r.DepartmentId == null || !departments.Select(d => d.Id).Contains(r.DepartmentId.Value))
                .ToList();

            if (unassignedRoles.Any())
            {
                DepartmentRoleTreeDto unassignedNode = new DepartmentRoleTreeDto
                {
                    Id = 0,
                    Label = "DEFAULT",
                    DepartmentCode = "DEFAULT",
                    Children = new List<RoleNodeDto>()
                };

                foreach (Def_Role role in unassignedRoles)
                {
                    RoleNodeDto roleNode = new RoleNodeDto
                    {
                        Id = role.Id,
                        Label = role.RoleName,
                        RoleCode = role.RoleCode,
                        DepartmentId = 0
                    };

                    unassignedNode.Children.Add(roleNode);
                }

                treeList.Add(unassignedNode);
            }

            return treeList;
        }

        /// <summary>
        /// 获取租户部门角色树形结构
        /// </summary>
        /// <param name="keyword">关键字查询</param>
        /// <returns>租户部门角色树形结构</returns>
        public async Task<List<CompanyDepartmentRoleTreeDto>> GetCompanyDepartmentRoleTreeAsync(string token, string keyword = null)
        {
            // 获取所有部门
            Expression<Func<Def_Department, bool>> departmentFilter = Expressionable.Create<Def_Department>()
                .And(d => d.IsActive == true)
                .AndIF(!string.IsNullOrWhiteSpace(keyword), d => d.DepartmentName.Contains(keyword) || d.DepartmentCode.Contains(keyword))
                .ToExpression();

            List<Def_Department> departments = await _departmentRepository.GetListAsync(departmentFilter);

            // 已存在部门租户
            var existDeptTenantIds = departments.Select(d => d.TenantId).ToHashSet();
            
            // 获取所有角色
            Expression<Func<Def_Role, bool>> roleFilter = Expressionable.Create<Def_Role>()
                .And(r => r.IsActive == true)
                .AndIF(!string.IsNullOrWhiteSpace(keyword), r => r.RoleName.Contains(keyword) || r.RoleCode.Contains(keyword))
                .ToExpression();

            List<Def_Role> roles = await _roleRepository.GetListAsync(roleFilter);


            // 按租户分组
            var tenantsWithDepartments = departments
                .GroupBy(d => d.TenantId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // 添加默认部门(未创建def_department记录)角色数据
            roles.Where(role => role.DepartmentId is null or 0 && !existDeptTenantIds.Contains(role.TenantId))
                .ToList()
                .ForEach(role =>
                {
                    if (!tenantsWithDepartments.ContainsKey(role.TenantId))
                    {
                        var defaultDepartment = new Def_Department
                        {
                            Id = 0,
                            TenantId = role.TenantId,
                            Company = role.Company,
                            DepartmentCode = "Default",
                            DepartmentName = "Default",
                            IsActive = true,
                            SortOrder = 1
                        };
                        tenantsWithDepartments[role.TenantId] = [defaultDepartment];
                    }
                });
            
            // 构建租户-部门-角色树形结构
            List<CompanyDepartmentRoleTreeDto> treeList = new List<CompanyDepartmentRoleTreeDto>();

            foreach (var tenantGroup in tenantsWithDepartments)
            {
                string tenantId = tenantGroup.Key;
                List<Def_Department> tenantDepartments = tenantGroup.Value;

                CompanyDepartmentRoleTreeDto tenantNode = new CompanyDepartmentRoleTreeDto
                {
                    Company = tenantId,
                    Label = tenantDepartments != null ? tenantDepartments.First().Company : tenantId,
                    Children = new List<DepartmentNodeDto>()
                };

                // 添加部门节点
                foreach (Def_Department department in tenantDepartments)
                {
                    DepartmentNodeDto departmentNode = new DepartmentNodeDto
                    {
                        Id = department.Id,
                        Label = department.DepartmentName,
                        DepartmentCode = department.DepartmentCode,
                        Children = new List<RoleNodeDto>()
                    };

                    // 获取当前部门下的所有角色
                    List<Def_Role> departmentRoles = roles
                        .Where(r => r.DepartmentId == department.Id && r.TenantId == tenantId)
                        .ToList();

                    // 添加角色节点
                    foreach (Def_Role role in departmentRoles)
                    {
                        RoleNodeDto roleNode = new RoleNodeDto
                        {
                            Id = role.Id,
                            Label = role.RoleName,
                            RoleCode = role.RoleCode,
                            DepartmentId = department.Id
                        };

                        departmentNode.Children.Add(roleNode);
                    }

                    tenantNode.Children.Add(departmentNode);
                }

                // 添加未分配部门的角色
                List<Def_Role> unassignedRoles = roles
                    .Where(r => r.TenantId == tenantId &&
                                (r.DepartmentId == null ||
                                 !tenantDepartments.Select(d => d.Id).Contains(r.DepartmentId.Value)))
                    .ToList();

                if (unassignedRoles.Any())
                {
                    DepartmentNodeDto unassignedNode = new DepartmentNodeDto
                    {
                        Id = 0,
                        Label = "Default",
                        DepartmentCode = "Default",
                        Children = new List<RoleNodeDto>()
                    };

                    foreach (Def_Role role in unassignedRoles)
                    {
                        RoleNodeDto roleNode = new RoleNodeDto
                        {
                            Id = role.Id,
                            Label = role.RoleName,
                            RoleCode = role.RoleCode,
                            DepartmentId = 0
                        };

                        unassignedNode.Children.Add(roleNode);
                    }

                    tenantNode.Children.Add(unassignedNode);
                }

                treeList.Add(tenantNode);
            }

            return treeList;
        }

        /// <summary>
        /// 判断角色是否与用户关联
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否存在关联关系</returns>
        public async Task<bool> HasUserAssociatedAsync(long roleId)
        {
            // 获取与该角色关联的用户ID列表
            List<long> userIds = await _userRoleRepository.GetUserIdsByRoleAsync(roleId);

            // 如果列表不为空，则存在关联关系
            return userIds.Count > 0;
        }
    }
}
