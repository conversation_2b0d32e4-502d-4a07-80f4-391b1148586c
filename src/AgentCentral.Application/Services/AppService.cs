using System.Linq.Expressions;
using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IService.App;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.RequestModels.Dify;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.IRepository.Workflow;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.Common.Lib.LogUtil;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    public class AppService : BaseAppService, IAppService
    {
        private readonly IAppRepository _appRepository;
        private readonly IAppUsageService _appUsageService;
        private readonly IMapper _mapper;
        private readonly ILogger<AppService> _logger;
        private readonly UserContext _userContext;
        private readonly IBaseRepository<Def_App_Usage> _usageRepository;
        private readonly IBaseRepository<Def_App_Usage_Stats> _statsRepository;
        private readonly IAppReviewCommentService _appReviewCommentService;
        private readonly ITagRepository _tagRepository;
        private readonly ITagMappingRepository _tagMappingRepository;
        private readonly IBaseRepository<Doc_Attachment_Mapping> _attachmentMappingRepository;
        private readonly IBaseRepository<Doc_Attachment> _attachmentRepository;
        private readonly IBaseRepository<Def_App_Permission> _appPermissionRepository;
        private readonly DifyClient _difyClient;
        private readonly IAttachmentService _attachmentService;
        private readonly IWorkflowLogRepository _workFlowLogRepository;
        private readonly IUserInfoRepository _userRepository;
        private readonly IHostEnvironment _hostEnvironment;
        private readonly IBaseRepository<Def_App_Change> _appChangeRepository;
        private readonly IAppParamService _appParamService;
        private readonly IAppSubscribeService _appSubscribeService;
        private readonly IAppIdGeneratorService _appIdGeneratorService;
        private readonly IBaseRepository<Def_App_User_Tag> _appUserTagRepository;
        private readonly IBaseRepository<Def_App_Role> _appRoleRepository;
        public AppService(
            IMapper mapper,
            ILogger<AppService> logger,
            UserContext userContext,
            IAppRepository appRepository,
            IAppUsageService appUsageService,
            IBaseRepository<Def_App_Usage> usageRepository,
            IBaseRepository<Def_App_Usage_Stats> statsRepository,
            IAppReviewCommentService appReviewCommentService,
            ITagRepository tagRepository,
            ITagMappingRepository tagMappingRepository,
            IBaseRepository<Doc_Attachment_Mapping> attachmentMappingRepository,
            IBaseRepository<Def_App_Permission> appPermissionRepository,
            DifyClient difyClient,
            IBaseRepository<Doc_Attachment> attachmentRepository,
            IAttachmentService attachmentService,
            IWorkflowLogRepository workFlowLogRepository,
            IUserInfoRepository userRepository,
            IBaseRepository<Def_App_User_Tag> appUserTagRepository,
            IBaseRepository<Def_App_Role> appRoleRepository,
            IBaseRepository<Def_App_Change> appChangeRepository,
            IAppParamService appParamService,
            IAppSubscribeService appSubscribeService,
            IAppIdGeneratorService appIdGeneratorService,
            IHostEnvironment hostEnvironment
            ) : base(appRepository)
        {
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
            _appRepository = appRepository;
            _appUsageService = appUsageService;
            _usageRepository = usageRepository;
            _statsRepository = statsRepository;
            _appReviewCommentService = appReviewCommentService;
            _tagRepository = tagRepository;
            _tagMappingRepository = tagMappingRepository;
            _attachmentMappingRepository = attachmentMappingRepository;
            _appPermissionRepository = appPermissionRepository;
            _difyClient = difyClient;
            _attachmentRepository = attachmentRepository;
            _attachmentService = attachmentService;
            _workFlowLogRepository = workFlowLogRepository;
            _userRepository = userRepository;
            _appChangeRepository = appChangeRepository;
            _appUserTagRepository = appUserTagRepository;
            _appRoleRepository = appRoleRepository;
            _appParamService = appParamService;
            _appSubscribeService = appSubscribeService;
            _appIdGeneratorService = appIdGeneratorService;
            _hostEnvironment = hostEnvironment;
        }

        public async Task<List<AppDetailResponse>> GetMyAllAsync()
        {
            List<Def_App> apps = await _appRepository.GetListAsync(l => l.CreateBy == _userContext.UserId);
            return _mapper.Map<List<AppDetailResponse>>(apps);
        }

        public async Task<PageModelDto<AppDetailPageResponse>> GetPageListAsync(AppSearchPageDto searchDto, AppQueryModeEnum queryMode = AppQueryModeEnum.Public)
        {
            Expression<Func<Def_App, bool>> filterExpression = Expressionable.Create<Def_App>()
                .And(a => !string.IsNullOrWhiteSpace(a.AppCode))
                .And(a => a.AppStatus == AppStatusEnum.Published)
                .AndIF(!string.IsNullOrWhiteSpace(searchDto.AppMode), a => a.AppMode.Equals(searchDto.AppMode))
                .AndIF(!string.IsNullOrEmpty(searchDto.SearchText), a =>
                    a.AppName.Contains(searchDto.SearchText) ||
                    a.AppDescription.Contains(searchDto.SearchText) ||
                    a.CreateName.Contains(searchDto.SearchText))
                .ToExpression();

            var dicDepartments = new Dictionary<string, List<string>>();
            if (searchDto.Departments.Count() > 0)
            {
                foreach (var company in searchDto.Departments.Select(d => d.CompanyId).Distinct())
                {
                    dicDepartments.Add(company, searchDto.Departments.Where(d => d.CompanyId == company).Select(d => d.DepartmentCode).ToList());
                }
            }

            (List<Def_App> apps, int total) = await _appRepository.GetPageListWithStatsAsync(
                filterExpression,
                searchDto.PageIndex,
                searchDto.PageSize,
                searchDto.OrderBy,
                searchDto.IsAsc,
                searchDto.TagIds,
                queryMode,
                dicDepartments
            );
            List<AppDetailPageResponse> appDtos = _mapper.Map<List<AppDetailPageResponse>>(apps);

            // 批量获取所有统计数据
            var appIds = appDtos.Select(x => x.AppId).ToList();

            // 获取统计数据
            var allStats = await _statsRepository.GetListAsync(x => appIds.Contains(x.AppId));
            var statsDict = allStats.ToDictionary(x => x.AppId);

            // 更新应用数据
            foreach (var app in appDtos)
            {
                // 获取使用时长
                app.LastDaysSeconds = await _appUsageService.GetLast15DaysUsageAsync(app.MainAppId, app.CreateTime);
                app.TotalUsageSecond = (await _usageRepository.GetListAsync(u => u.AppId == app.AppId && u.IsActive)).Sum(u => u.CostSeconds);

                // 获取统计数据
                var stats = statsDict.GetValueOrDefault(app.AppId);
                app.Runs = stats?.Runs ?? 0;
                app.Joins = stats?.Joins ?? 0;

                // 获取评论统计
                var reviewStats = await _appReviewCommentService.GetReviewStatsAsync(app.AppId);
                app.Star = reviewStats?.AverageRating ?? 0;
            }

            return new PageModelDto<AppDetailPageResponse>(searchDto.PageIndex, searchDto.PageSize, appDtos, total);
        }

        public async Task<AppDetailResponse> GetByAppIdAsync(string appId)
        {
            Def_App app = await _appRepository.GetFirstAsync(x => x.AppId == appId) ?? throw new AgentCentralException("App not found");
            var detail = _mapper.Map<AppDetailResponse>(app);
            var attachmentMappings = await _attachmentMappingRepository.GetListAsync(a => a.BusinessId == app.Id && (a.BusinessType == AttachmentTypeEnum.AgentBanner || a.BusinessType == AttachmentTypeEnum.AgentVideo));
            var attachments = await _attachmentRepository.GetListAsync(a => attachmentMappings.Select(m => m.AttachmentId).Contains(a.Id));
            var attachmentOutputs = new List<AttachmentOutputDto>();
            foreach (var attachment in attachments)
            {
                attachmentOutputs.Add(new AttachmentOutputDto
                {
                    Id = attachment.Id,
                    Name = attachment.RealName,
                    Url = await _attachmentService.GetAccessUrlAsync(attachment.Id, default)
                });
            }
            detail.Banners.AddRange(attachmentOutputs.Where(a => attachmentMappings.Where(m => m.BusinessType == AttachmentTypeEnum.AgentBanner).Select(m => m.AttachmentId).Contains(a.Id)));
            detail.Video = attachmentOutputs.FirstOrDefault(a => attachmentMappings.Where(m => m.BusinessType == AttachmentTypeEnum.AgentVideo).Select(m => m.AttachmentId).Contains(a.Id));
            var departments = await _appPermissionRepository.GetListAsync(p => p.AppId == appId);
            detail.Departments = _mapper.Map<List<AppDepartmentDto>>(departments);
            var userInfo = await _userRepository.GetFirstAsync(u => u.UserId == app.CreateBy && u.UserType != UserTypeEnum.Central);
            detail.UserCompany = userInfo?.CompanyName;
            detail.UserDepartment = userInfo?.DepartmentName;
            detail.ContentQualityScore = $"{app.ContentQualityScore:F1}%";
            detail.SafetyCheckScore = $"{app.SafetyCheckScore:F1}%";
            detail.ComplianceScore = $"{app.ComplianceScore:F1}%";
            detail.OverallScore = $"{app.OverallScore:F1}%";
            var tagMappings = await _tagMappingRepository.GetListAsync(tm => tm.ResourceId == app.Id && tm.IsActive);
            var tagIds = tagMappings.Select(tm => tm.TagId).ToList();
            var tags = await _tagRepository.GetListAsync(t => tagIds.Contains(t.Id));
            detail.Tags = tags.Select(t => t.Id.ToString()).ToList();
            var appChang = await _appChangeRepository.GetFirstAsync(x => x.AppId == detail.Id);
            if (appChang != null)
            {
                detail.ChangeDescription = appChang.ChangeDescription;
            }

            // 获取角色IDs
            var appRoles = await _appRoleRepository.GetListAsync(r => r.AppId == app.Id && r.IsActive);
            if (appRoles?.Count > 0)
            {
                detail.RoleIds = appRoles.Select(r => r.RoleId).ToList();
            }

            // 获取用户标签IDs
            var appUserTags = await _appUserTagRepository.GetListAsync(ut => ut.AppId == app.Id && ut.IsActive);
            if (appUserTags?.Count > 0)
            {
                detail.UserTagIds = appUserTags.Select(ut => ut.UserTagId).ToList();
            }
            return detail;
        }

        public async Task<AppDetailResponse> CreateAsync(CreateAppRequest request)
        {
            try
            {
                _logger.LogInformation("Creating new app with AppId: {AppId}", request.AppId);

                bool exists = await _appRepository.IsAnyAsync(x => x.AppId == request.AppId);
                if (exists)
                {
                    _logger.LogWarning("Attempted to create duplicate AppId: {AppId}", request.AppId);
                    throw new AgentCentralException("AppId already exists");
                }

                Def_App app = _mapper.Map<Def_App>(request);
                app.TenantId = _userContext.TenantId;
                app.CreateBy = _userContext.UserId;
                app.CreateName = _userContext.UserName;
                bool result = await _appRepository.InsertAsync(app);
                if (result)
                {
                    return _mapper.Map<AppDetailResponse>(app);
                }
                else
                {
                    throw new AgentCentralException("Create fail");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating app with AppId: {AppId}", request.AppId);
                throw;
            }
        }

        public async Task<AppDetailResponse> CreateAppAsync(CreateAppDto createApp)
        {
            var (difyAppId, difyAppCode) = await _difyClient.CreateAppAsync(new CreateDifyAppRequest
            {
                Name = createApp.AppName,
                Description = createApp.AppDescription,
                Mode = createApp.AppMode
            });

            if (string.IsNullOrEmpty(difyAppId) && !_hostEnvironment.IsDevelopment())
            {
                throw new AgentCentralException("Create app error.");
            }

            return await CreateOrCopyAppAsync(difyAppId, difyAppCode, createApp, async () =>
            {
                await _difyClient.DeleteAppAsync(difyAppId);
            });
        }

        public async Task<AppDetailResponse> CopyAppAsync(string appId, CreateAppDto createApp, long mainAppId = 0)
        {
            var (difyAppId, difyAppCode) = await _difyClient.CopyAppAsync(appId, new CopyDifyAppRequest
            {
                Name = createApp.AppName,
                Description = createApp.AppDescription
            });

            if (string.IsNullOrEmpty(difyAppId) && !_hostEnvironment.IsDevelopment())
            {
                throw new AgentCentralException("Copy app error.");
            }

            return await CreateOrCopyAppAsync(difyAppId, difyAppCode, createApp, async () =>
            {
                await _difyClient.DeleteAppAsync(difyAppId);
            }, mainAppId);
        }

        private async Task<AppDetailResponse> CreateOrCopyAppAsync(
            string difyAppId,
            string difyAppCode,
            CreateAppDto createApp,
            Func<Task> rollbackAction,
            long mainAppId = 0)
        {
            try
            {
                Def_App app = _mapper.Map<Def_App>(createApp);
                app.AppStatus = AppStatusEnum.Unpublished;
                app.TenantId = _userContext.TenantId;
                app.CreateBy = _userContext.UserId;
                app.CreateName = _userContext.UserName;
                app.DifyPublished = false;
                app.IsCurrentVersion = mainAppId == 0;
                // -1 为未发布的副本，发布后不一定会升级版本，可能会直接替换掉当前版本
                app.VersionNumber = mainAppId == 0 ? 1 : -1;
                app.MainAppId = mainAppId == 0 ? app.Id : mainAppId;

                if (mainAppId > 0)
                {
                    var generatorId = await _appRepository.GetFirstAsync(a => a.Id == mainAppId, a => a.GeneratorId);
                    app.GeneratorId = generatorId;
                }
                else
                {
                    app.GeneratorId = await _appIdGeneratorService.GeneratorIdAsync(app.AppPermission, app.AppMode);
                }
                app.AppId = string.IsNullOrEmpty(difyAppId) ? app.GeneratorId : difyAppId;
                app.AppCode = string.IsNullOrEmpty(difyAppCode) ? app.GeneratorId : difyAppCode;
                using var context = _appRepository.CreateContext();
                var createResult = await _appRepository.InsertReturnEntityAsync(app);
                app.Id = createResult.Id;

                await SaveAppAttachmentsAndPermissionsAsync(
                    app.Id,
                    difyAppId,
                    createApp.BannerAttachmentIds,
                    createApp.VideoAttachmentId,
                    createApp.Departments
                );

                // 保存用户标签和角色关联
                await SaveAppUserTagsAndRolesAsync(
                    app.Id,
                    createApp.UserTagIds,
                    createApp.RoleIds
                );

                context.Commit();
                return _mapper.Map<AppDetailResponse>(app);
            }
            catch (Exception ex)
            {
                if (rollbackAction != null)
                {
                    await rollbackAction();
                }
                _logger.LogError(ex, "Error creating app with AppName: {AppName}", createApp.AppName);
                throw;
            }
        }

        public async Task<AppDetailResponse> UpdateAsync(string appId, UpdateAppRequest appDto)
        {
            Def_App app = await _appRepository.GetFirstAsync(x => x.AppId == appId) ?? throw new AgentCentralException("App not found");
            var tags = new List<Def_Tag>();
            if (appDto.DifyTagIds != null && appDto.DifyTagIds.Count > 0)
            {
                tags = await _tagRepository.GetListAsync(t => appDto.DifyTagIds.Contains(t.DifyTagId) && t.IsActive);
                // Tag不存在，返回错误信息
                if (tags.Count != appDto.DifyTagIds.Count)
                {
                    var errorMessage = $"Dify tag {string.Join(",", appDto.DifyTagIds.Except(tags.Select(t => t.DifyTagId)))} not found";
                    throw new AgentCentralException(errorMessage);
                }
            }

            _ = _mapper.Map(appDto, app);
            app.TenantId = _userContext.TenantId;
            app.UpdateBy = _userContext.UserId;
            app.UpdateName = _userContext.UserName;
            try
            {
                using var context = _appRepository.CreateContext();
                bool result = await _appRepository.UpdateAsync(app);
                await _tagMappingRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag_Mapping { IsActive = false }, t => t.ResourceId == app.Id);
                var insertMappings = tags.Select(t => new Def_Tag_Mapping { ResourceId = app.Id, TagId = t.Id }).ToList();
                if (insertMappings.Count > 0)
                {
                    await _tagMappingRepository.InsertRangeAsync(insertMappings);
                }
                context.Commit();
                return _mapper.Map<AppDetailResponse>(app);
            }
            catch (Exception ex)
            {
                _logger.LogError("Update app error message:{Message}", ex.Message);
                throw new AgentCentralException("Update fail");
            }
        }

        public async Task<AppDetailResponse> UpdateAppAsync(string appId, UpdateAppDto appDto)
        {
            var app = await _appRepository.GetAppDetailByAppId(appId) ?? throw new AgentCentralException("App not found");
            if (app.AppStatus == AppStatusEnum.Published || app.AppStatus == AppStatusEnum.AutoPublished)
            {
                var createDto = _mapper.Map<CreateAppDto>(appDto);
                createDto.AppMode = app.AppMode;
                var newApp = await CopyAppAsync(appId, createDto, app.MainAppId);

                var change = new Def_App_Change
                {
                    AppId = newApp.Id,
                    ChangeType = (int)AppChangeTypeEnum.Presentation,
                    NeedUpgradeVersion = false,
                    ForceUpdate = false,
                    ChangeDescription = appDto.ChangeDescription,
                };
                await _appChangeRepository.InsertAsync(change);

                await _appParamService.CopyAppParamsAndValuesAsync(app.Id, newApp.Id);
                newApp.ChangeDescription = appDto.ChangeDescription;
                return newApp;
            }
            var tags = new List<Def_Tag>();
            if (appDto.TagIds != null && appDto.TagIds.Count > 0)
            {
                tags = await _tagRepository.GetListAsync(t => appDto.TagIds.Contains(t.Id) && t.IsActive);
                // Tag不存在，返回错误信息
                if (tags.Count != appDto.TagIds.Count)
                {
                    var errorMessage = $"Tag {string.Join(",", appDto.TagIds.Except(tags.Select(t => t.Id)))} not found";
                    throw new AgentCentralException(errorMessage);
                }
            }

            _ = _mapper.Map(appDto, app);
            app.TenantId = _userContext.TenantId;
            app.UpdateBy = _userContext.UserId;
            app.UpdateName = _userContext.UserName;
            if (_userContext.UserId == app.CreateBy)
            {
                app.AppStatus = AppStatusEnum.Unpublished;
            }
            try
            {
                if (app.AppSource == AppSourceEnum.Dify)
                {
                    var (difyAppId, difyAppCode) = await _difyClient.UpdateAppAsync(appId, new UpdateDifyAppRequest
                    {
                        Name = appDto.AppName,
                        Description = appDto.AppDescription
                    });
                    if (string.IsNullOrEmpty(difyAppId) && !_hostEnvironment.IsDevelopment())
                    {
                        throw new AgentCentralException("Update app error.");
                    }
                }
                if (appDto.AppPermission == AppPermissionEnum.Department ||  appDto.UserTagIds.Count > 0 || appDto.RoleIds.Count > 0)
                {
                    app.GuestMode = false;
                }
                using var context = _appRepository.CreateContext();
                bool result = await _appRepository.UpdateAsync(app);

                if (result)
                {
                    await _appChangeRepository.UpdateSetColumnsTrueAsync(t => new Def_App_Change { ChangeDescription = appDto.ChangeDescription }, t => t.AppId == app.Id);
                }

                await SaveAppAttachmentsAndPermissionsAsync(
                    app.Id,
                    appId,
                    appDto.BannerAttachmentIds,
                    appDto.VideoAttachmentId,
                    appDto.Departments
                );


                // 保存用户标签和角色关联
                await SaveAppUserTagsAndRolesAsync(
                    app.Id,
                    appDto.UserTagIds,
                    appDto.RoleIds
                );

                // 同步DifyTags
                var tagMappings = await _tagMappingRepository.GetListAsync(m => m.ResourceId == app.Id);
                var existsTags = await _tagRepository.GetListAsync(t => tagMappings.Select(m => m.TagId).Contains(t.Id));
                foreach (var tagMapping in tagMappings)
                {
                    var difyTagId = existsTags.FirstOrDefault(t => t.Id == tagMapping.TagId).DifyTagId;
                    await _difyClient.RemoveTagBindingAsync(new RemoveTagBindingRequest { TagId = difyTagId, TargetId = appId });
                }
                await _difyClient.CreateTagBindingAsync(new CreateTagBindingRequest
                {
                    TagIds = tags.Select(t => t.DifyTagId).ToList(),
                    TargetId = appId
                });

                await _tagMappingRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag_Mapping { IsActive = false }, t => t.ResourceId == app.Id);
                var insertMappings = tags.Select(t => new Def_Tag_Mapping { ResourceId = app.Id, TagId = t.Id }).ToList();
                if (insertMappings.Count > 0)
                {
                    await _tagMappingRepository.InsertRangeAsync(insertMappings);
                }
                context.Commit();
                var appDetail = _mapper.Map<AppDetailResponse>(app);
                appDetail.ChangeDescription = appDto.ChangeDescription;
                return appDetail;
            }
            catch (Exception ex)
            {
                _logger.LogError("Update app error message:{Message}", ex.Message);
                throw new AgentCentralException("Update fail");
            }
        }

        /// <summary>
        /// 保存应用与用户标签和角色的关联关系
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="userTagIds">用户标签ID列表</param>
        /// <param name="roleIds">角色ID列表</param>
        /// <returns></returns>
        private async Task SaveAppUserTagsAndRolesAsync(long appId, List<long> userTagIds, List<long> roleIds)
        {
            // 先删除旧的关联关系
            await _appUserTagRepository.DeleteAsync(ut => ut.AppId == appId);
            await _appRoleRepository.DeleteAsync(r => r.AppId == appId);

            // 添加用户标签关联
            if (userTagIds != null && userTagIds.Count > 0)
            {
                List<Def_App_User_Tag> userTagRelations = userTagIds.Select(tagId => new Def_App_User_Tag
                {
                    AppId = appId,
                    UserTagId = tagId,
                    TenantId = _userContext.TenantId,
                    CreateBy = _userContext.UserId,
                    CreateName = _userContext.UserName
                }).ToList();

                await _appUserTagRepository.InsertRangeAsync(userTagRelations);
            }

            // 添加角色关联
            if (roleIds != null && roleIds.Count > 0)
            {
                List<Def_App_Role> roleRelations = roleIds.Select(roleId => new Def_App_Role
                {
                    AppId = appId,
                    RoleId = roleId,
                    TenantId = _userContext.TenantId,
                    CreateBy = _userContext.UserId,
                    CreateName = _userContext.UserName
                }).ToList();

                await _appRoleRepository.InsertRangeAsync(roleRelations);
            }
        }

        public async Task<bool> DeleteAsync(string appId)
        {
            try
            {
                var app = await _appRepository.GetAppDetailByAppId(appId) ?? throw new AgentCentralException("App not found");
                var mainAppId = await GetMainAppIdAsync(appId);
                var allVersionApps = await _appRepository.GetListAsync(a => a.MainAppId == mainAppId);
                var allAppIds = allVersionApps.Select(a => a.AppId).ToList();
                foreach (var id in allAppIds)
                {
                    await _difyClient.DeleteAppAsync(appId);
                }
                using var context = _appRepository.CreateContext();
                // 删除App-标记删除
                _ = await _appRepository.UpdateSetColumnsTrueAsync(x => new Def_App { IsActive = false, AppStatus = AppStatusEnum.Deleted },
                    x => allAppIds.Contains(x.AppId));
                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete app with id {AppId}", appId);
                throw new AgentCentralException("Delete failed");
            }
        }

        public async Task<PageModelDto<AppDetailResponse>> GetUserAppsAsync(AppSearchDto searchDto)
        {
            Expression<Func<Def_App, bool>> filterExpression = Expressionable.Create<Def_App>()
                .AndIF(searchDto.AppStatus.HasValue && searchDto.AppStatus > 0 && searchDto.AppStatus != AppStatusEnum.DifyUnpublished,
                      a => a.AppStatus == searchDto.AppStatus && a.DifyPublished == true)
                .AndIF(searchDto.AppStatus.HasValue && searchDto.AppStatus == AppStatusEnum.DifyUnpublished,
                      a => a.DifyPublished == false)
                .AndIF(searchDto.PermissionType.HasValue, a => a.AppPermission == searchDto.PermissionType)
                .AndIF(!string.IsNullOrEmpty(searchDto.AppMode),
                      a => a.AppMode == searchDto.AppMode)
                .AndIF(!string.IsNullOrEmpty(searchDto.SearchText),
                      a => a.AppName.Contains(searchDto.SearchText)
                      || a.AppDescription.Contains(searchDto.SearchText)
                      || a.CreateName.Contains(searchDto.SearchText))
                .ToExpression();

            (List<AppListModel> apps, int total) = await _appRepository.GetPlatformPageListAsync(
                filterExpression,
                searchDto.PageIndex,
                searchDto.PageSize,
                searchDto.OrderBy,
                searchDto.IsAsc,
                searchDto.TagIds
            );

            List<AppDetailResponse> appDtos = _mapper.Map<List<AppDetailResponse>>(apps);
            return new PageModelDto<AppDetailResponse>(searchDto.PageIndex, searchDto.PageSize, appDtos, total);
        }

        public async Task<bool> UpdateAppStatusAsync(string appId, AppStatusEnum appStatus)
        {
            var app = await _appRepository.GetAppDetailByAppId(appId) ?? throw new AgentCentralException("App not found");

            if (app.AppStatus == AppStatusEnum.Unpublished && !app.DifyPublished)
            {
                throw new AgentCentralException(ErrorCodeEnum.DataStatusError, "The agent is not published. Please publish it and then proceed with the submission.");
            }

            return await _appRepository.UpdateSetColumnsTrueAsync(a => new Def_App
            {
                AppStatus = appStatus
            }, a => a.AppId == appId);
        }

        public async Task<bool> UpdateDifyPublishedAsync(string appId, bool difyPublished)
        {
            var app = await _appRepository.GetAppDetailByAppId(appId) ?? throw new AgentCentralException("App not found");
            return await _appRepository.UpdateSetColumnsTrueAsync(a => new Def_App
            {
                DifyPublished = difyPublished
            }, a => a.AppId == appId);
        }

        private async Task SaveAppAttachmentsAndPermissionsAsync(
            long appId,
            string difyAppId,
            List<long> bannerAttachmentIds,
            long videoAttachmentId,
            List<AppDepartmentDto> departments)
        {
            await _attachmentMappingRepository.DeleteAsync(
                a => a.BusinessId == appId &&
                    (a.BusinessType == AttachmentTypeEnum.AgentBanner ||
                     a.BusinessType == AttachmentTypeEnum.AgentVideo)
            );

            await _appPermissionRepository.UpdateSetColumnsTrueAsync(
                p => new Def_App_Permission { IsActive = false },
                p => p.AppId == difyAppId
            );

            await _attachmentMappingRepository.InsertRangeAsync(bannerAttachmentIds.Select(aid => new Doc_Attachment_Mapping
            {
                BusinessId = appId,
                AttachmentId = aid,
                BusinessType = AttachmentTypeEnum.AgentBanner
            }).ToList());

            await _attachmentMappingRepository.InsertAsync(new Doc_Attachment_Mapping
            {
                BusinessId = appId,
                AttachmentId = videoAttachmentId,
                BusinessType = AttachmentTypeEnum.AgentVideo
            });

            departments.Add(new AppDepartmentDto
            {
                CompanyId = _userContext.Company,
                DepartmentCode = _userContext.DepartmentCode
            });

            await _appPermissionRepository.InsertRangeAsync(departments.Where(d => !string.IsNullOrEmpty(d.CompanyId)).Distinct().Select(d => new Def_App_Permission
            {
                AppId = difyAppId,
                Company = d.CompanyId,
                DepartmentCode = d.DepartmentCode
            }).ToList());
        }

        public async Task<List<AppDetailResponse>> GetListAsync()
        {
            List<Def_App> apps = await _appRepository.GetListAsync();
            return _mapper.Map<List<AppDetailResponse>>(apps);
        }

        public async Task<PageModelDto<AppReviewDetailPageResponse>> GetReviewPageListAsync(AppReviewSearchPageDto searchDto)
        {
            (List<AppReviewListModel> apps, int total) = await _appRepository.GetReviewPageListAsync(
                searchDto.PageIndex,
                searchDto.PageSize,
                searchDto.OrderBy,
                searchDto.IsAsc,
                searchDto.AgentName,
                searchDto.Creator,
                searchDto.Company,
                searchDto.Department,
                searchDto.TagIds,
                searchDto.Status,
                searchDto.PermissionType,
                searchDto.Reviewer,
                searchDto.AppMode,
                searchDto.GuestMode,
                searchDto.ReviewStatus
            );

            // 收集所有应用ID
            var appIds = apps.Select(a => a.Id).ToList();
            
            // 获取所有带有用户标签的应用ID
            var appsWithUserTags = await _appUserTagRepository.GetListAsync(ut => appIds.Contains(ut.AppId) && ut.IsActive);
            var appIdsWithUserTags = appsWithUserTags.Select(ut => ut.AppId).Distinct().ToHashSet();
            
            var response = new List<AppReviewDetailPageResponse>();
            var appUserTagsCountDict = await GetAppUserTagsCountAsync(appIds);


            foreach (var app in apps)
            {
                var reviewResponse = new AppReviewDetailPageResponse
                {
                    Id = app.Id,
                    AppName = app.AppName,
                    CompanyDepartment = string.IsNullOrEmpty(app.CompanyName) && string.IsNullOrEmpty(app.DepartmentName) ? string.Empty :
                        string.IsNullOrEmpty(app.CompanyName) ? app.DepartmentName :
                        string.IsNullOrEmpty(app.DepartmentName) ? app.CompanyName :
                        $"{app.CompanyName}/{app.DepartmentName}",
                    CreateName = app.CreateName,
                    CreateTime = app.CreateTime,
                    AppStatus = app.AppStatus,
                    ContentQualityScore = $"{app.ContentQualityScore:F1}%",
                    SafetyCheckScore = $"{app.SafetyCheckScore:F1}%",
                    ComplianceScore = $"{app.ComplianceScore:F1}%",
                    OverallScore = $"{app.OverallScore:F1}%",
                    RejectReason = "",
                    AppCode = app.AppCode,
                    AppMode = app.AppMode,
                    AppSource = app.AppSource,
                    AppId = app.AppId,
                    ApprovalDate = app.ApprovalDate,
                    SubmitDate = app.SubmitDate,
                    AppPermission = app.AppPermission,
                    Reviewer = app.Reviewer,
                    // 检查应用是否有关联的用户标签，如果有则将GuestMode强制设为false
                    GuestMode = app.GuestMode,
                    GuestModeEdit = app.AppPermission == AppPermissionEnum.Public && appUserTagsCountDict.GetValueOrDefault(app.Id, 0) == 0,
                    AppType = app.AppPermission == AppPermissionEnum.Public ? AppTypeEnum.General.GetDisplayName() : AppTypeEnum.Private.GetDisplayName()
                };

                if (app.AppStatus == AppStatusEnum.Rejected || app.AppStatus == AppStatusEnum.AutoRejected || app.AppStatus == AppStatusEnum.Delisted)
                {
                    var workFlowLog = await _workFlowLogRepository.GetFirstAsync(w =>
                        w.BusinessId == app.Id &&
                        (w.ApprovalStatus == (int)AppStatusEnum.Rejected || w.ApprovalStatus == (int)AppStatusEnum.AutoRejected),
                        w => w.ApprovalDate,
                        "desc");


                    if (workFlowLog != null)
                    {
                        reviewResponse.RejectReason = workFlowLog.Description;
                    }
                }

                response.Add(reviewResponse);
            }

            return new PageModelDto<AppReviewDetailPageResponse>(searchDto.PageIndex, searchDto.PageSize, response, total);
        }

        public async Task<bool> UpdateAppPermissionAsync(UpdateAppPermissionDto dto)
        {
            try
            {
                var app = await _appRepository.GetFirstAsync(x => x.AppId == dto.AppId)
                    ?? throw new AgentCentralException("App not found");

                using var context = _appRepository.CreateContext();
                var guestMode = app.GuestMode;

                // 若权限类型为Private或设置了用户标签，关闭GuestMode
                if (dto.AppPermission == AppPermissionEnum.Department ||  dto.UserTagIds?.Count > 0 || dto.RoleIds?.Count > 0)
                {
                    guestMode = false;
                }

                await _appRepository.UpdateSetColumnsTrueAsync(
                    a => new Def_App { AppPermission = dto.AppPermission, GuestMode = guestMode },
                    a => a.AppId == dto.AppId
                );
                // 保存用户标签和角色关联
                await SaveAppUserTagsAndRolesAsync(
                    app.Id,
                    dto.UserTagIds,
                    dto.RoleIds
                );
                // 更新部门权限
                // await saveAppPermisson(dto);

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update app permission for AppId: {AppId}", dto.AppId);
                throw new AgentCentralException("Update app permission failed");
            }
        }

        private async Task updateAppPermisson(UpdateAppPermissionDto dto)
        {
            // 将现有的权限记录设置为inactive
            await _appPermissionRepository.UpdateSetColumnsTrueAsync(
                p => new Def_App_Permission { IsActive = false },
                p => p.AppId == dto.AppId
            );

            // 如果是部门权限，添加新的权限记录
            if (dto.AppPermission == AppPermissionEnum.Department)
            {
                if (dto.Departments == null || dto.Departments.Count == 0)
                {
                    throw new AgentCentralException("Departments are required for department permission");
                }

                var newPermissions = new List<Def_App_Permission>();
                foreach (var dept in dto.Departments)
                {
                    if (string.IsNullOrEmpty(dept.CompanyId) || string.IsNullOrEmpty(dept.DepartmentCode))
                    {
                        throw new AgentCentralException("CompanyId and DepartmentCode are required for each department");
                    }

                    newPermissions.Add(new Def_App_Permission
                    {
                        AppId = dto.AppId,
                        Company = dept.CompanyId,
                        DepartmentCode = dept.DepartmentCode,
                        IsActive = true,
                        CreateBy = _userContext.UserId,
                        CreateName = _userContext.UserName,
                        CreateTime = DateTime.Now,
                        TenantId = _userContext.TenantId
                    });
                }

                if (newPermissions.Count > 0)
                {
                    await _appPermissionRepository.InsertRangeAsync(newPermissions);
                }
            }
        }

        public async Task<AppDetailResponse> CreateAgentFromSourceAsync(CreateAgentFromSourceRequest request)
        {
            try
            {
                var sourceApp = await _appRepository.GetFirstAsync(a => a.AppId == request.SourceAppId) ?? throw new AgentCentralException("Source app not found");
                if (sourceApp.VersionNumber == -1) // 不能基于未发布的副本创建新Agent
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataStatusError, "Cannot create new agent based on an unpublished.");
                }

                bool exists = await _appRepository.IsAnyAsync(x => x.AppId == request.NewAppId);
                if (exists)
                {
                    throw new AgentCentralException("AppId already exists");
                }

                var appDetail = await GetByAppIdAsync(request.SourceAppId);
                var createAppDto = _mapper.Map<CreateAppDto>(appDetail);
                _userContext.UserId = sourceApp.CreateBy;
                _userContext.UserName = sourceApp.CreateName;
                _userContext.TenantId = sourceApp.TenantId;
                var createUserInfo = await _userRepository.GetFirstAsync(u => u.UserId == sourceApp.CreateBy);
                if (createUserInfo != null)
                {
                    _userContext.DepartmentCode = createUserInfo.Department;
                    _userContext.Company = createUserInfo.Company;
                }
                var newApp = await CreateOrCopyAppAsync(request.NewAppId, request.NewAppCode, createAppDto, null, sourceApp.MainAppId);
                var change = new Def_App_Change
                {
                    AppId = newApp.Id,
                    ChangeType = 0,
                    NeedUpgradeVersion = false,
                    ForceUpdate = false
                };
                await _appChangeRepository.InsertAsync(change);

                var sourceAppTags = await _tagMappingRepository.GetListAsync(t => t.ResourceId == sourceApp.Id && t.IsActive);
                if (sourceAppTags.Count > 0)
                {
                    var newAppTags = sourceAppTags.Select(t => new Def_Tag_Mapping { ResourceId = newApp.Id, TagId = t.TagId }).ToList();
                    await _tagMappingRepository.InsertRangeAsync(newAppTags);
                }
                return newApp;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating agent from source. AppId: {AppId}, SourceAppId: {SourceAppId}", request.NewAppId, request.SourceAppId);
                throw;
            }
        }

        public async Task<bool> HandleAppApprovalAsync(long appId)
        {
            var app = await _appRepository.GetFirstAsync(x => x.Id == appId) ?? throw new AgentCentralException("App not found");
            var appChange = await _appChangeRepository.GetFirstAsync(x => x.AppId == appId) ?? throw new AgentCentralException("App change record not found");

            try
            {
                using var context = _appRepository.CreateContext();
                var currentApp = await _appRepository.GetFirstAsync(a => a.MainAppId == app.MainAppId && a.IsCurrentVersion);
                if (appChange.ChangeType == (int)AppChangeTypeEnum.Presentation || appChange.ForceUpdate)
                {
                    await _appSubscribeService.CopyAllSubscriptionsAsync(app.AppId);
                }

                // 更新新版本为当前版本
                await _appRepository.UpdateSetColumnsTrueAsync(a => new Def_App
                {
                    IsCurrentVersion = true,
                    VersionNumber = appChange.ChangeType == (int)AppChangeTypeEnum.Presentation ? currentApp.VersionNumber : currentApp.VersionNumber + 1
                }, a => a.Id == appId);

                // 更新旧版本为非当前版本
                await _appRepository.UpdateSetColumnsTrueAsync(a => new Def_App
                {
                    IsCurrentVersion = false,
                    IsActive = appChange.ChangeType != (int)AppChangeTypeEnum.Presentation
                }, a => a.Id == currentApp.Id);

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, $"Error handling app approval for appId: {appId}");
                return false;
            }
        }

 		/// <summary>
        /// 更新应用的Guest Mode状态
        /// </summary>
        /// <param name="dto">Guest Mode更新请求</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateGuestModeAsync(UpdateGuestModeDto dto)
        {
            try
            {
                var app = await _appRepository.GetFirstAsync(x => x.AppId == dto.Id)
                    ?? throw new AgentCentralException("App not found");

                return await _appRepository.UpdateSetColumnsTrueAsync(
                    a => new Def_App { GuestMode = dto.GuestMode },
                    a => a.AppId == dto.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update guest mode for AppId: {AppId}", dto.Id);
                throw new AgentCentralException("Update guest mode failed");
            }
        }

        /// <summary>
        /// 批量更新应用的Guest Mode状态
        /// </summary>
        /// <param name="dto">批量Guest Mode更新请求</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> BatchUpdateGuestModeAsync(BatchUpdateGuestModeDto dto)
        {
            try
            {
                if (dto.Ids == null || dto.Ids.Count == 0)
                {
                    throw new AgentCentralException("App ids are required");
                }

                using var context = _appRepository.CreateContext();
                foreach (var appId in dto.Ids)
                {
                    var app = await _appRepository.GetFirstAsync(x => x.AppId == appId);
                    if (app != null)
                    {
                        await _appRepository.UpdateSetColumnsTrueAsync(
                            a => new Def_App { GuestMode = dto.GuestMode },
                            a => a.AppId == appId
                        );
                    }
                }
                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to batch update guest mode for Apps");
                throw new AgentCentralException("Batch update guest mode failed");
            }
        }

        /// <summary>
        /// 批量获取appIds对应的Def_App_User_Tag记录数量
        /// </summary>
        /// <param name="appIds">应用ID列表</param>
        /// <returns>每个appId对应的用户标签数量字典</returns>
        public async Task<Dictionary<long, int>> GetAppUserTagsCountAsync(List<long> appIds)
        {
            if (appIds == null || appIds.Count == 0)
            {
                return new Dictionary<long, int>();
            }
            
            // 使用GetSumByGroupAsync方法在数据库层执行分组统计
            var result = await _appUserTagRepository.GetSumByGroupAsync(
                ut => appIds.Contains(ut.AppId) && ut.IsActive,
                ut => ut.AppId,
                ut => new { ut.AppId, Count = SqlFunc.AggregateCount(1) }
            );
            
            // 转换结果为字典格式
            var countDict = result.ToDictionary(
                r => r.AppId,
                r => r.Count
            );
            
            // 确保所有传入的appId都有对应的记录，没有则设为0
            var dictionary = new Dictionary<long, int>();
            foreach (var appId in appIds)
            {
                dictionary[appId] = countDict.ContainsKey(appId) ? countDict[appId] : 0;
            }
            
            return dictionary;
        }
    }
}