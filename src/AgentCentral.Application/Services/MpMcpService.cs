using System.Linq.Expressions;
using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Application.Contracts.RequestModels.MpMcp;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Manager;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// Mp_Mcp服务实现类
    /// </summary>
    public class MpMcpService : IMpMcpService, IScopedService
    {
        private readonly IMpMcpRepository _mpMcpRepository;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly ILogger<MpMcpService> _logger;
        private readonly WorkflowManager _workFlowManager;
        private readonly IWorkflowService _workflowService;
        private readonly IAttachmentService _attachmentService;
        private readonly McpClient _mcpClient;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MpMcpService(
            IMpMcpRepository mpMcpRepository,
            IMapper mapper,
            UserContext userContext,
            ILogger<MpMcpService> logger,
            WorkflowManager workflowManager,
            IAttachmentService attachmentService,
            McpClient mcpClient,
            IWorkflowService workflowService)
        {
            _mpMcpRepository = mpMcpRepository;
            _mapper = mapper;
            _userContext = userContext;
            _logger = logger;
            _workFlowManager = workflowManager;
            _attachmentService = attachmentService;
            _mcpClient = mcpClient;
            _workflowService = workflowService;
        }

        /// <summary>
        /// 获取审核MCP服务分页列表（统一接口，支持多状态查询）
        /// </summary>
        public async Task<PageModelDto<MpMcpListDto>> GetReviewPageListAsync(MpMcpSearchPageDto searchDto)
        {
            try
            {
                Expression<Func<Mp_Mcp, bool>> filterExpression = BuildFilterExpression(searchDto);
                
                var orderBy = string.IsNullOrEmpty(searchDto.OrderBy) ? "CreateTime" : searchDto.OrderBy;
                var isAsc = searchDto.IsAsc;

                (var mcps, int total) = await _mpMcpRepository.GetReviewPageListAsync(
                    filterExpression,
                    searchDto.PageIndex,
                    searchDto.PageSize,
                    orderBy,
                    isAsc);

                var businessIds = mcps.Select(x => x.Id).ToList();

                var dtos = _mapper.Map<List<MpMcpListDto>>(mcps);
                var workflowInfosDictionary = await _workFlowManager.GetWorkflowInfosDictionaryAsync(businessIds);
                
                dtos.ForEach(item =>
                {
                    // 处理工作流备注
                    var currentNodeDescription = workflowInfosDictionary.GetValueOrDefault(item.Id)?.CurrentNodeDescription;
                    if (!string.IsNullOrEmpty(currentNodeDescription))
                    {
                        var commentsDto = JsonConvert.DeserializeObject<MpMcpWorkflowCommentsDto>(currentNodeDescription);
                        item.ReviewRemark = commentsDto?.Comments;
                    }
                });
                return new PageModelDto<MpMcpListDto>(searchDto.PageIndex, searchDto.PageSize, dtos, total);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting MCP review page list");
                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to get MCP review list");
            }
        }

        /// <summary>
        /// 获取MCP服务详情
        /// </summary>
        public async Task<MpMcpDetailDto> GetMcpDetailAsync(long mcpId)
        {
            try
            {
                Mp_Mcp mcp = await _mpMcpRepository.GetByMcpIdAsync(mcpId);
                if (mcp == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");
                }

                var result = _mapper.Map<MpMcpDetailDto>(mcp);
                await BuildMpMcpDetailDto(result);
                result.WorkflowLogs = await _workflowService.GetWorkflowLogAsync(mcpId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting MCP detail for ID: {McpId}", mcpId);
                throw;
            }
        }

        private async Task<MpMcpDetailDto> BuildMpMcpDetailDto(MpMcpDetailDto result)
        {

            if (long.TryParse(result.LogoUrl, out var logoId) && logoId > 0)
            {
                result.LogoAttachmentId = logoId;
                result.LogoUrl = await _attachmentService.GetAccessUrlAsync(logoId, CancellationToken.None);
            }
            if (long.TryParse(result.DocumentUrl, out var documentId) && documentId > 0)
            {
                result.DocumentAttachmentId = documentId;
                var documentFile = await _attachmentService.GetAttachmentByIdAsync(documentId);
                result.DocumentFileName = documentFile?.RealName;
                result.DocumentFileSize = documentFile?.FileSize ?? 0;
                result.DocumentUploadTime = documentFile?.CreateTime;
            }
            if (long.TryParse(result.VideoUrl, out var videoId) && videoId > 0)
            {
                result.VideoAttachmentId = videoId;
                var videoFile = await _attachmentService.GetAttachmentByIdAsync(videoId);
                result.VideoFileName = videoFile?.RealName;
                result.VideoFileSize = videoFile?.FileSize ?? 0;
                result.VideoUploadTime = videoFile?.CreateTime;
            }
            return result;
        }

        /// <summary>
        /// 更新MCP服务
        /// </summary>
        /// <param name="request">更新请求</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateMpMcpAsync(UpdateMpMcpDto request)
        {
            try
            {
                // 获取MCP服务
                Mp_Mcp existMcp = await _mpMcpRepository.GetByIdAsync(request.Id)
                                   ?? throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "MCP service not found");

                try
                {
                    _mpMcpRepository.BeginTran();
                    var updateMcpDto = new McpAppDto();

                    // 仅更新传入的非空字段
                    UpdateNonNullFields(request, existMcp, updateMcpDto);

                    // 处理Logo附件ID
                    if (request.LogoAttachmentId is > 0)
                    {
                        long.TryParse(existMcp.LogoUrl, out long oldLogoId);
                        if (oldLogoId != request.LogoAttachmentId)
                        {
                            existMcp.LogoUrl = request.LogoAttachmentId.ToString();
                            updateMcpDto.LogoUrl = await _attachmentService.GetAccessUrlAsync(request.LogoAttachmentId.Value, CancellationToken.None);
                        }
                    }

                    // 处理文档附件ID
                    if (request.DocumentAttachmentId is > 0)
                    {
                        long.TryParse(existMcp.DocumentUrl, out long oldDocumentId);
                        if (oldDocumentId != request.DocumentAttachmentId)
                        {
                            existMcp.DocumentUrl = request.DocumentAttachmentId.ToString();
                            updateMcpDto.DocumentUrl = await _attachmentService.GetAccessUrlAsync(request.DocumentAttachmentId.Value, CancellationToken.None);
                        }
                    }

                    // 处理视频附件ID
                    if (request.VideoAttachmentId is > 0)
                    {
                        long.TryParse(existMcp.VideoUrl, out long oldVideoId);
                        if (oldVideoId != request.VideoAttachmentId)
                        {
                            existMcp.VideoUrl = request.VideoAttachmentId.ToString();
                            updateMcpDto.VideoUrl = await _attachmentService.GetAccessUrlAsync(request.VideoAttachmentId.Value, CancellationToken.None);
                        }
                    }

                    // 设置更新人信息
                    existMcp.UpdateBy = _userContext.UserId;
                    existMcp.UpdateName = _userContext.UserName;
                    existMcp.UpdateTime = DateTime.Now;

                    // 更新数据
                    bool result = await _mpMcpRepository.UpdateAsync(existMcp);

                    _mpMcpRepository.CommitTran();

                    if (result)
                    {
                        // 更新MCP服务
                        await UpdateMcpServer(updateMcpDto, existMcp);
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    _mpMcpRepository.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Update MCP service from form failed for McpId: {McpId}", request.Id);
                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Update MCP service from form failed");
            }
        }

        private async Task<bool> UpdateMcpServer(McpAppDto updateDto, Mp_Mcp existMcp)
        {
            var response = await _mcpClient.UpdateMcpServer(existMcp.ServiceName, updateDto);
            return response.Code == 0;
        }

        /// <summary>
        /// 仅更新传入的非空字段
        /// </summary>
        /// <param name="request">更新请求</param>
        /// <param name="existMcp">现有MCP实体</param>
        private void UpdateNonNullFields(UpdateMpMcpDto request, Mp_Mcp existMcp, McpAppDto updateDto)
        {
            // 更新服务提供商（如果传入了非空值）
            if (!string.IsNullOrWhiteSpace(request.Provider))
            {
                existMcp.Provider = request.Provider;
                updateDto.Provider = request.Provider;
            }

            // 更新服务描述
            if (request.Description != null)
            {
                existMcp.Description = request.Description;
                updateDto.Description = request.Description;
            }

            // 更新服务分类
            if (request.Category != null)
            {
                existMcp.Category = request.Category;
                updateDto.category = request.Category;
            }

            // 更新使用场景
            if (request.UseCases != null)
            {
                existMcp.UseCases = request.UseCases;
            }

            // 更新服务类型
            if (request.ServiceType != null)
            {
                existMcp.ServiceType = request.ServiceType;
                updateDto.ServiceType = request.ServiceType;
            }

            // 更新服务价值
            if (request.ServiceValue != null)
            {
                existMcp.ServiceValue = request.ServiceValue;
                updateDto.ServiceValue = request.ServiceValue;
            }

            // 更新数据来源
            if (request.Source != null)
            {
                existMcp.Source = request.Source;
            }
        }

        public async Task<List<string>> ListProvieders(string keyword, int pageNumber = 1, int pageSize = 10)
        {
            return await _mpMcpRepository.ListProvieders(keyword, pageNumber, pageSize);
        }

        #region Private Methods

        /// <summary>
        /// 构建筛选条件（统一接口，支持多状态查询）
        /// </summary>
        private Expression<Func<Mp_Mcp, bool>> BuildFilterExpression(MpMcpSearchPageDto searchDto)
        {
            return Expressionable.Create<Mp_Mcp>()
                .AndIF(searchDto.Status is { Length: > 0 }, x => searchDto.Status.Contains(x.Status))
                .AndIF(searchDto.Provider is { Count: > 0 }, x => searchDto.Provider.Contains(x.Provider))
                .AndIF(!string.IsNullOrEmpty(searchDto.ServiceName), x => x.ServiceName.Contains(searchDto.ServiceName))
                .AndIF(searchDto.McpId.HasValue, x => x.McpId == searchDto.McpId.Value)
                .AndIF(searchDto.Id.HasValue, x => x.Id == searchDto.Id.Value)
                .AndIF(!string.IsNullOrEmpty(searchDto.Category), x => x.Category == searchDto.Category)
                .AndIF(!string.IsNullOrEmpty(searchDto.ServiceType), x => x.ServiceType == searchDto.ServiceType)
                .AndIF(!string.IsNullOrEmpty(searchDto.Source), x => x.Source == searchDto.Source)
                .AndIF(searchDto.CreateDateStart.HasValue, x => x.CreateTime >= searchDto.CreateDateStart.Value)
                .AndIF(searchDto.CreateDateEnd.HasValue, x => x.CreateTime <= searchDto.CreateDateEnd.Value)

                .ToExpression();
        }

        #endregion
    }


} 