using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.Common.Lib.LogUtil;
using Newtonsoft.Json;
using SqlSugar;
using System.Linq.Expressions;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// Application parameter service implementation
    /// </summary>
    public class AppParamService : BaseAppService, IAppParamService
    {
        private readonly IBaseRepository<Def_App_Param> _paramRepository;
        private readonly IBaseRepository<Def_App_Param_Value> _valueRepository;
        private readonly IAppRepository _appRepository;
        private readonly IAppParamValidationService _validationService;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly IBaseRepository<Def_App_Change> _appChangeRepository;

        public AppParamService(
            IBaseRepository<Def_App_Param> paramRepository,
            IBaseRepository<Def_App_Param_Value> valueRepository,
            IAppRepository appRepository,
            IAppParamValidationService validationService,
            IMapper mapper,
            UserContext userContext,
            IBaseRepository<Def_App_Change> appChangeRepository) : base(appRepository)
        {
            _paramRepository = paramRepository;
            _valueRepository = valueRepository;
            _appRepository = appRepository;
            _validationService = validationService;
            _mapper = mapper;
            _userContext = userContext;
            _appChangeRepository = appChangeRepository;
        }

        /// <summary>
        /// Get parameter definitions by application ID
        /// </summary>
        public async Task<List<AppParamDto>> GetParamDefinitionsAsync(long appId)
        {
            List<Def_App_Param> parameters = await _paramRepository.GetListAsync(p => p.AppId == appId && p.IsActive);
            var result = _mapper.Map<List<AppParamDto>>(parameters);
            foreach (var param in result)
            {
                if (param.ParamType == AppParamTypeEnum.Dropdown && !string.IsNullOrEmpty(param.Options))
                {
                    param.SelectOptions = JsonConvert.DeserializeObject<List<SelectOption>>(param.Options);
                }
            }
            return result;
        }

        /// <summary>
        /// Get parameter values by dify application ID
        /// </summary>
        public async Task<List<AppParamValueDto>> GetParamValuesAsync(string appId, long userId)
        {
            var app = await _appRepository.GetFirstAsync(a => a.AppId == appId) ?? throw new AgentCentralException("App not found");

            // Get current user's parameter values
            List<Def_App_Param_Value> values = await _valueRepository.GetListAsync(v =>
                v.AppId == app.Id &&
                v.IsActive &&
                v.CreateBy == userId);

            // If some parameters don't have values, get default values from parameter definitions
            var existingParamIds = values.Select(v => v.ParamId).ToList();
            var allParams = await _paramRepository.GetListAsync(p =>
                p.AppId == app.Id &&
                p.IsActive &&
                !existingParamIds.Contains(p.Id));

            // Create default value DTOs for parameters without values
            var defaultValues = allParams.Select(p => new AppParamValueDto
            {
                ParamId = p.Id,
                AppId = p.AppId,
                ParamKey = p.ParamKey,
                ParamValue = p.DefaultValue
            });

            // Merge existing values and default values
            var result = _mapper.Map<List<AppParamValueDto>>(values);
            result.AddRange(defaultValues);

            return result;
        }

        /// <summary>
        /// Create parameter definitions in batch
        /// </summary>
        public async Task<List<AppParamDto>> CreateParamDefinitionsAsync(string appId, List<CreateAppParamDto> dtos)
        {
            var app = await _appRepository.GetFirstAsync(a => a.AppId == appId);

            // Validate parameter key uniqueness
            var paramKeys = dtos.Select(d => d.ParamKey).ToList();
            var existingParams = await _paramRepository.GetListAsync(p =>
                p.AppId == app.Id &&
                paramKeys.Contains(p.ParamKey) &&
                p.IsActive);

            if (existingParams.Count != 0)
            {
                var duplicateKeys = existingParams.Select(p => p.ParamKey);
                throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, $"Parameter keys already exist: {string.Join(", ", duplicateKeys)}");
            }

            // Validate all parameter definitions
            foreach (var dto in dtos)
            {
                _validationService.ValidateParamDefinition(dto);
            }

            // Create parameters in batch
            var parameters = _mapper.Map<List<Def_App_Param>>(dtos);
            parameters.ForEach(f => f.AppId = app.Id);

            await _paramRepository.InsertRangeAsync(parameters);

            return _mapper.Map<List<AppParamDto>>(parameters);
        }

        public async Task<bool> UpdateParamDefinitionsAsync(string appId, List<CreateAppParamDto> dtos)
        {
            var app = await _appRepository.GetFirstAsync(a => a.AppId == appId);
            if (app.AppStatus == AppStatusEnum.Published || app.AppStatus == AppStatusEnum.AutoPublished)
            {
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "App has been published, parameters cannot be modified.");
            }

            try
            {
                var appParams = await _paramRepository.GetListAsync(p => p.AppId == app.Id);
                var inputKeys = dtos.Select(p => p.ParamKey);
                var existsKeys = appParams.Select(a => a.ParamKey);

                var paramsToUpdate = dtos.Where(p => existsKeys.Contains(p.ParamKey));
                var paramsToInsert = dtos.Where(p => !existsKeys.Contains(p.ParamKey));
                var paramsToDelete = appParams.Where(p => !inputKeys.Contains(p.ParamKey));

                using var context = _paramRepository.CreateContext();
                await _paramRepository.UpdateSetColumnsTrueAsync(p => new Def_App_Param { IsActive = false }, p => paramsToDelete.Select(d => d.Id).Contains(p.Id));
                await CreateParamDefinitionsAsync(appId, paramsToInsert.ToList());
                if (paramsToUpdate.Any())
                {
                    var updateModels = new List<Def_App_Param>();
                    foreach (var updateDto in paramsToUpdate)
                    {
                        _validationService.ValidateParamDefinition(updateDto);
                        var updateModel = appParams.First(d => d.ParamKey == updateDto.ParamKey);
                        _mapper.Map(updateDto, updateModel);
                        updateModels.Add(updateModel);
                    }
                    await _paramRepository.UpdateRangeAsync(updateModels);
                }
                var change = await _appChangeRepository.GetFirstAsync(c => c.AppId == app.Id && c.IsActive);
                var mainAppId = await GetMainAppIdAsync(appId);
                var isFirstPublish = (await _appRepository.CountAsync(a => a.MainAppId == mainAppId && a.IsActive)) == 1;
                if (change == null && !isFirstPublish)
                {
                    change = new Def_App_Change
                    {
                        AppId = app.Id,
                        ChangeType = 0,
                        NeedUpgradeVersion = false,
                        ForceUpdate = false
                    };
                    await _appChangeRepository.InsertAsync(change);
                }
                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error($"UpdateParamDefinitionsAsync error,message:{ex.Message}");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "Update param definitions error.");
            }
        }

        public async Task<List<AppParamValueDto>> SaveParamValuesAsync(long appId, List<SaveAppParamValueDto> values)
        {
            try
            {
                using var context = _valueRepository.CreateContext();

                var paramKeys = values.Select(v => v.ParamKey).ToList();
                var parameters = await _paramRepository.GetListAsync(x =>
                    x.AppId == appId &&
                    paramKeys.Contains(x.ParamKey) &&
                    x.IsActive);

                if (parameters.Count != paramKeys.Count)
                {
                    var missingKeys = paramKeys.Except(parameters.Select(p => p.ParamKey));
                    throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, $"Parameters not found: {string.Join(", ", missingKeys)}");
                }

                var validationErrors = new List<string>();
                foreach (var value in values)
                {
                    var param = parameters.First(p => p.ParamKey == value.ParamKey);
                    var paramDto = _mapper.Map<AppParamDto>(param);
                    var (isValid, errorMessage) = _validationService.ValidateParamValue(paramDto, value.Value);
                    if (!isValid)
                    {
                        validationErrors.Add($"Parameter '{param.ParamKey}': {errorMessage}");
                    }
                }

                if (validationErrors.Count > 0)
                {
                    var errorMessage = new
                    {
                        Message = "Invalid parameter values",
                        Details = validationErrors
                    };
                    throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, JsonConvert.SerializeObject(errorMessage));
                }

                var paramIds = parameters.Select(p => p.Id).ToList();
                var existingValues = await _valueRepository.GetListAsync(v =>
                    v.AppId == appId &&
                    paramIds.Contains(v.ParamId) &&
                    v.CreateBy == _userContext.UserId &&
                    v.IsActive);

                var valuesToInsert = new List<Def_App_Param_Value>();
                var valuesToUpdate = new List<Def_App_Param_Value>();

                foreach (var value in values)
                {
                    var param = parameters.First(p => p.ParamKey == value.ParamKey);
                    var existingValue = existingValues.FirstOrDefault(v => v.ParamId == param.Id);

                    if (existingValue == null)
                    {
                        valuesToInsert.Add(new Def_App_Param_Value
                        {
                            AppId = appId,
                            ParamId = param.Id,
                            ParamKey = param.ParamKey,
                            ParamValue = value.Value,
                            CreateBy = _userContext.UserId,
                            CreateName = _userContext.UserName
                        });
                    }
                    else
                    {
                        existingValue.ParamValue = value.Value;
                        existingValue.UpdateBy = _userContext.UserId;
                        existingValue.UpdateName = _userContext.UserName;
                        valuesToUpdate.Add(existingValue);
                    }
                }

                if (valuesToInsert.Count != 0)
                {
                    await _valueRepository.InsertRangeAsync(valuesToInsert);
                }
                if (valuesToUpdate.Count != 0)
                {
                    await _valueRepository.UpdateRangeAsync(valuesToUpdate);
                }

                context.Commit();

                var allValues = valuesToInsert.Concat(valuesToUpdate).ToList();
                return _mapper.Map<List<AppParamValueDto>>(allValues);
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Copy application parameter definitions and values
        /// </summary>
        /// <param name="sourceAppId">Source application ID</param>
        /// <param name="targetAppId">Target application ID</param>
        /// <returns>Whether the copy operation was successful</returns>
        public async Task<bool> CopyAppParamsAndValuesAsync(long sourceAppId, long targetAppId)
        {
            try
            {
                using var context = _paramRepository.CreateContext();

                var sourceParams = await _paramRepository.GetListAsync(p => p.AppId == sourceAppId && p.IsActive);
                if (sourceParams.Count == 0)
                {
                    return true;
                }

                var paramMapping = new Dictionary<long, long>();
                var newParams = new List<Def_App_Param>();
                foreach (var sourceParam in sourceParams)
                {
                    var newParam = new Def_App_Param
                    {
                        AppId = targetAppId,
                        ParamKey = sourceParam.ParamKey,
                        ParamName = sourceParam.ParamName,
                        Description = sourceParam.Description,
                        ParamType = sourceParam.ParamType,
                        IsRequired = sourceParam.IsRequired,
                        DefaultValue = sourceParam.DefaultValue,
                        Options = sourceParam.Options,
                        ValidationRules = sourceParam.ValidationRules,
                        SortOrder = sourceParam.SortOrder,
                        CreateBy = _userContext.UserId,
                        CreateName = _userContext.UserName,
                        IsActive = true
                    };
                    paramMapping[sourceParam.Id] = newParam.Id;
                    newParams.Add(newParam);
                }
                await _paramRepository.InsertRangeAsync(newParams);

                var sourceValues = await _valueRepository.GetListAsync(v => v.AppId == sourceAppId && v.IsActive);
                if (sourceValues.Count == 0)
                {
                    context.Commit();
                    return true;
                }

                var newValues = new List<Def_App_Param_Value>();
                foreach (var sourceValue in sourceValues)
                {
                    if (paramMapping.TryGetValue(sourceValue.ParamId, out var newParamId))
                    {
                        newValues.Add(new Def_App_Param_Value
                        {
                            AppId = targetAppId,
                            ParamId = newParamId,
                            ParamKey = sourceValue.ParamKey,
                            ParamValue = sourceValue.ParamValue,
                            CreateBy = sourceValue.CreateBy,
                            CreateName = sourceValue.CreateName,
                            CreateTime = sourceValue.CreateTime,
                            UpdateBy = sourceValue.UpdateBy,
                            UpdateName = sourceValue.UpdateName,
                            UpdateTime = sourceValue.UpdateTime,
                            IsActive = true
                        });
                    }
                }

                if (newValues.Count != 0)
                {
                    await _valueRepository.InsertRangeAsync(newValues);
                }

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error($"Failed to copy application parameters: {ex.Message}");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, $"Failed to copy application parameters: {ex.Message}");
            }
        }

        /// <summary>
        /// Check application parameter configuration status
        /// </summary>
        public async Task<AppParamStatusDto> CheckParamStatusAsync(long appId)
        {
            var result = new AppParamStatusDto
            {
                RequiresParameters = false,
                IsConfigured = false
            };

            // 获取应用的所有参数定义
            var parameters = await _paramRepository.CountAsync(p => p.AppId == appId && p.IsActive);
            result.RequiresParameters = parameters > 0;

            // 获取用户配置的参数值
            var paramValues = await _valueRepository.CountAsync(v => v.AppId == appId && v.CreateBy == _userContext.UserId && v.IsActive);
            result.IsConfigured = paramValues > 0;

            return result;
        }

        public async Task<Dictionary<string, string>> GetParamDictionaryAsync(List<string> keys)
        {
            // 构建查询表达式
            Expression<Func<Def_App_Param_Value, bool>> filterExpression = Expressionable.Create<Def_App_Param_Value>()
                .And(x => x.IsActive == true)
                .And(x => x.CreateBy == _userContext.UserId)
                .And(x => keys.Contains(x.ParamKey))
                .ToExpression();

            var values = await _valueRepository.GetListAsync(filterExpression);
            return values.GroupBy(v => v.ParamKey).Select(g => new KeyValuePair<string, string>(g.Key, g.Max(v => v.ParamValue))).ToDictionary();
        }
    }
}
