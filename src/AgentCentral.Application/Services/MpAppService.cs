using System.Linq.Expressions;
using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.ResponseModels.AppReviewComment;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.EnumUtil;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.BlobProvider;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Microsoft.AspNetCore.Http;
using System.Net.Http;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// 应用市场应用服务实现类
    /// </summary>
    public class MpAppService : IMpAppService, IScopedService
    {
        private readonly IMpAppRepository _mpAppRepository;
        private readonly IAttachmentService _attachmentService;
        private readonly IMapper _mapper;
        private readonly ILogger<MpAppService> _logger;
        private readonly UserContext _userContext;
        private readonly IBlobContainer _blobContainer;
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IAttachmentMappingRepository _attachmentMappingRepository;
        private readonly AgentCentralClient _agentCentralClient;

        public MpAppService(
            IMpAppRepository mpAppRepository,
            IAttachmentService attachmentService,
            IAttachmentRepository attachmentRepository,
            IAttachmentMappingRepository attachmentMappingRepository,
            IBlobContainer blobContainer,
            IMapper mapper,
            ILogger<MpAppService> logger,
            UserContext userContext,
            AgentCentralClient agentCentralClient)
        {
            _mpAppRepository = mpAppRepository;
            _attachmentService = attachmentService;
            _attachmentRepository = attachmentRepository;
            _attachmentMappingRepository = attachmentMappingRepository;
            _blobContainer = blobContainer;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
            _agentCentralClient = agentCentralClient;
        }


        /// <summary>
        /// 通过表单数据更新应用
        /// </summary>
        /// <param name="request">表单更新请求</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateMpAppFromFormAsync(UpdateMpAppFormRequest request)
        {

            // 获取应用
            Mp_App updateApp = await _mpAppRepository.GetByIdAsync(request.Id)
                               ?? throw new AgentCentralException("App not found");

            bool privacyPolicyFileUpdated = false;

            try
            {
                _mpAppRepository.BeginTran();
                string oldPrivacyPolicyFileName = updateApp.PrivacyPolicyFileName;
                var oldPrivacyType = updateApp.PrivacyPolicyDocumentType;

                // 更新应用信息
                _mapper.Map(request, updateApp);
                updateApp.PrivacyPolicyFileName = oldPrivacyPolicyFileName;
                updateApp.PrivacyPolicyDocumentType = oldPrivacyType;

                // 处理图标附件ID
                if (request.Icon is > 0)
                {
                    long.TryParse(updateApp.AppIcon, out long oldIconId);
                    if (oldIconId != request.Icon)
                    {
                        // 验证新的附件ID是否存在
                        Doc_Attachment iconAttachment = await _attachmentRepository.GetByIdAsync(request.Icon);
                        if (iconAttachment == null)
                        {
                            throw new AgentCentralException("Icon attachment not found");
                        }
                        // 删除旧的图标附件
                        if (oldIconId > 0)
                        {
                            await _attachmentRepository.DeleteByIdAsync(oldIconId);
                        }
                        updateApp.AppIcon = request.Icon.ToString();
                    }

                }

                // 删除旧的截图关联关系
                Expression<Func<Doc_Attachment_Mapping, bool>> oldMappingFilter =
                    Expressionable.Create<Doc_Attachment_Mapping>()
                        .And(x => x.BusinessId == updateApp.Id)
                        .And(x => x.BusinessType == AttachmentTypeEnum.MpScreenShot)
                        .ToExpression();

                List<Doc_Attachment_Mapping> oldMappings = await _attachmentMappingRepository.GetListAsync(oldMappingFilter);
                List<long> oldAttachmentIds = oldMappings.Select(x => x.AttachmentId).ToList();

                // 如果request.ScreenshotUrls不为空，则需要过滤掉不需要删除的截图
                if (request.ScreenshotUrls != null && request.ScreenshotUrls.Count > 0)
                {
                    // 过滤掉空字符串
                    var validScreenshotUrls = request.ScreenshotUrls.Where(url => !string.IsNullOrWhiteSpace(url)).ToList();

                    if (validScreenshotUrls.Any())
                    {
                        // 获取所有旧附件的信息
                        var oldAttachments = await _attachmentRepository.GetListAsync(a => oldAttachmentIds.Contains(a.Id));

                        // 筛选出需要保留的附件ID（文件名存在于ScreenshotUrls中的附件）
                        var attachmentsToKeep = oldAttachments
                            .Where(a => !string.IsNullOrEmpty(a.FileName) &&
                                   validScreenshotUrls.Any(url => url.Contains(a.FileName)))
                            .Select(a => a.Id)
                            .ToList();

                        // 从删除列表中移除需要保留的附件ID
                        oldAttachmentIds = oldAttachmentIds.Except(attachmentsToKeep).ToList();
                    }
                }

                // 只删除不需要保留的附件和映射关系
                if (oldAttachmentIds.Any())
                {
                    await _attachmentRepository.DeleteAsync(a => oldAttachmentIds.Contains(a.Id));
                    await _attachmentMappingRepository.DeleteAsync(a => oldAttachmentIds.Contains(a.AttachmentId));
                }

                if (request.Screenshots != null && request.Screenshots.Count != 0)
                {
                    // 上传新的截图文件并创建关联关系
                    foreach (var screenshot in request.Screenshots)
                    {
                        if (screenshot > 0)
                        {
                            // 创建附件关联关系
                            var attachmentMapping = new Doc_Attachment_Mapping
                            {
                                AttachmentId = screenshot,
                                BusinessId = updateApp.Id,
                                BusinessType = AttachmentTypeEnum.MpScreenShot
                            };

                            await _attachmentMappingRepository.InsertAsync(attachmentMapping);
                        }
                    }
                }

                // 处理APK文件
                if (!string.Equals(request.ApkFile, updateApp.ApkFilePath) && !string.IsNullOrEmpty(request.ApkFile))
                {
                    if (updateApp.UploadType == UploadTypeEnum.ExistingApp)
                    {
                        updateApp.ApkFilePath = request.ApkFile;
                    }
                    else
                    {
                        var tryParse = long.TryParse(request.ApkFile, out long newApkId);
                        if (!tryParse || newApkId <= 0)
                        {
                            throw new AgentCentralException("APK attachment id illegal");
                        }
                        // 验证新的附件ID是否存在
                        Doc_Attachment apkAttachment = await _attachmentRepository.GetByIdAsync(request.ApkFile);
                        if (apkAttachment == null)
                        {
                            throw new AgentCentralException("APK attachment not found");
                        }
                        // 删除旧的APK附件
                        if (!string.IsNullOrEmpty(updateApp.ApkFilePath) && long.TryParse(updateApp.ApkFilePath, out long oldApkId) && oldApkId > 0)
                        {
                            await _attachmentRepository.DeleteByIdAsync(oldApkId);
                        }

                        updateApp.ApkFilePath = request.ApkFile;
                        updateApp.ApkName = apkAttachment.RealName;
                        updateApp.ApkSize = apkAttachment.FileSize;
                    }
                }


                if (request.PrivacyPolicyDocumentType == PrivacyPolicyDocumentTypeEnum.Document)
                {
                    // 处理隐私政策文件附件ID
                    if (request.PrivacyPolicyFile is > 0)
                    {
                        long.TryParse(updateApp.PrivacyPolicyFileName, out long oldPrivacyId);
                        if (oldPrivacyId != request.PrivacyPolicyFile)
                        {
                            // 验证新的附件ID是否存在
                            Doc_Attachment privacyAttachment = await _attachmentRepository.GetByIdAsync(request.PrivacyPolicyFile);
                            if (privacyAttachment == null)
                            {
                                throw new AgentCentralException("Privacy policy attachment not found");
                            }
                            // 删除旧的隐私政策文件附件
                            if (oldPrivacyId > 0)
                            {
                                await _attachmentRepository.DeleteByIdAsync(oldPrivacyId);
                            }
                            privacyPolicyFileUpdated = true;
                            updateApp.PrivacyPolicyFileName = request.PrivacyPolicyFile.ToString();
                            updateApp.PrivacyPolicyFileSize = privacyAttachment.FileSize;
                            updateApp.PrivacyPolicyFileType = privacyAttachment.FileType;
                            updateApp.PrivacyPolicyDocumentType = PrivacyPolicyDocumentTypeEnum.Document;
                        }
                    }
                }
                else
                {
                    if (updateApp.PrivacyPolicyDocumentType == PrivacyPolicyDocumentTypeEnum.Document
                        && !string.IsNullOrEmpty(updateApp.PrivacyPolicyFileName)
                        && long.TryParse(updateApp.PrivacyPolicyFileName, out long oldPrivacyId) && oldPrivacyId > 0)
                    {
                        privacyPolicyFileUpdated = true;
                        await _attachmentRepository.DeleteByIdAsync(oldPrivacyId);
                    }
                    updateApp.PrivacyPolicyFileName = null;
                    updateApp.PrivacyPolicyFileSize = 0;
                    updateApp.PrivacyPolicyFileType = string.Empty;
                    updateApp.PrivacyPolicyDocumentType = PrivacyPolicyDocumentTypeEnum.Url;
                }
                
                if (!string.IsNullOrEmpty(request.StoreLink))
                {
                    updateApp.ApkFilePath = request.StoreLink;
                    updateApp.PlatformType = request.StoreLink.Contains("google")
                        ? PlatformTypeEnum.GooglePlay
                        : PlatformTypeEnum.AppleStore;
                }
                else
                {
                    updateApp.PlatformType = PlatformTypeEnum.ITEM;
                }

                updateApp.MonetizationType = MonetizationTypeEnum.Free.GetDescription().Equals(request.Monetization)
                    ? MonetizationTypeEnum.Free
                    : MonetizationTypeEnum.Paid;

                // 设置更新人信息
                updateApp.UpdateBy = _userContext.UserId;
                updateApp.UpdateName = _userContext.UserName;
                updateApp.UpdateTime = DateTime.Now;

                // 更新数据
                bool result = await _mpAppRepository.UpdateAsync(updateApp);

                _mpAppRepository.CommitTran();
                
                if (result && privacyPolicyFileUpdated)
                {
                     _ = _agentCentralClient.PrivacyPolicyReviewAsync(updateApp.Id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _mpAppRepository.RollbackTran();
                _logger.LogError(ex, "Update app from form failed for AppId: {AppId}", request.Id);
                throw new AgentCentralException("Update app from form failed");
            }
        }

    }
}