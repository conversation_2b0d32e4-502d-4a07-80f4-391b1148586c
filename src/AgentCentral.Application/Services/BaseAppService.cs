using AgentCentral.Application.Contracts.IServices;

namespace AgentCentral.Application.Services
{
    public abstract class BaseAppService : IBaseAppService
    {
        private readonly IAppRepository _appRepository;

        public BaseAppService(IAppRepository appRepository)
        {
            _appRepository = appRepository;
        }

        public virtual async Task<long> GetMainAppIdAsync(string appId)
        {
            var app = await _appRepository.GetFirstAsync(a => a.AppId == appId, a => new { a.MainAppId });
            return app.MainAppId;
        }
    }
}
