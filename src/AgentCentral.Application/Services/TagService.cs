using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.Tag;
using AgentCentral.Application.Contracts.ResponseModels.Tag;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Microsoft.Extensions.Logging;
using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.Tag;

namespace AgentCentral.Application.Services
{
    public class TagService : ITagService, IScopedService
    {
        private readonly IMapper _mapper;

        private readonly ITagRepository _tagRepository;

        private readonly ITagMappingRepository _tagMappingRepository;

        private readonly ILogger<TagService> _logger;

        private readonly DifyClient _difyClient;

        public TagService(ITagRepository tagRepository,
            IMapper mapper,
            ILogger<TagService> logger,
            ITagMappingRepository tagMappingRepository,
            DifyClient difyClient)
        {
            _tagRepository = tagRepository;
            _mapper = mapper;
            _logger = logger;
            _tagMappingRepository = tagMappingRepository;
            _difyClient = difyClient;
        }

        public async Task<List<TagResponse>> GetTags(string tagName)
        {
            if (!string.IsNullOrEmpty(tagName))
            {
                var tags = await _tagRepository.GetListAsync(t => t.TagName.Contains(tagName) && t.IsActive);
                return _mapper.Map<List<TagResponse>>(tags);
            }
            else
            {
                var tags = await _tagRepository.GetListAsync(t => t.IsActive);
                return _mapper.Map<List<TagResponse>>(tags);
            }
        }

        public async Task<bool> DeleteTagAsync(long tagId)
        {
            try
            {
                var tag = await _tagRepository.GetFirstAsync(t => t.Id == tagId);
                var difyResult = await _difyClient.DeleteTagAsync(tag.DifyTagId);
                if (difyResult)
                {
                    using var context = _tagRepository.CreateContext();
                    await _tagRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag { IsActive = false }, t => t.Id == tagId);
                    await _tagMappingRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag_Mapping { IsActive = false }, t => t.TagId == tagId && t.IsActive);
                    context.Commit();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DeleteTas Error Message:{Message}", ex.Message);
                throw new AgentCentralException("Delete failed");
            }
        }

        public async Task<bool> DeleteTagByDifyId(string difyTagId)
        {
            var tag = await _tagRepository.GetFirstAsync(t => t.DifyTagId == difyTagId && t.IsActive) ?? throw new AgentCentralException("Dify tag not found");
            try
            {
                using var context = _tagRepository.CreateContext();
                await _tagRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag { IsActive = false }, t => t.Id == tag.Id);
                await _tagMappingRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag_Mapping { IsActive = false }, t => t.TagId == tag.Id && t.IsActive);
                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Delete tag error message:{Message}", ex.Message);
                throw new AgentCentralException("Delete failed");
            }
        }

        public async Task<bool> CreateTagRequest(CreateTagRequest request)
        {
            if (!Enum.TryParse(request.TagType, true, out TagTypeEnum tagType))
            {
                throw new AgentCentralException("Tag type error.");
            }
            // dify平台name可以重复，此处只校验DifyTagId
            if (!string.IsNullOrEmpty(request.DifyTagId))
            {
                var isExists = await _tagRepository.IsAnyAsync(t => t.DifyTagId == request.DifyTagId && t.TagType == (int)tagType && t.IsActive);
                if (isExists)
                {
                    throw new AgentCentralException("The tag already exists.");
                }
            }
            var tag = _mapper.Map<Def_Tag>(request);
            return await _tagRepository.InsertAsync(tag);
        }

        public async Task<bool> UpdateTagByDify(string difyTagId, UpdateTagRequest request)
        {
            var tag = await _tagRepository.GetFirstAsync(t => t.DifyTagId == difyTagId && t.IsActive) ?? throw new AgentCentralException("Dify tag not found");
            return await _tagRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag { TagName = request.TagName }, t => t.Id == tag.Id);
        }

        public async Task<bool> CreateTagAsync(CreateTagDto createDto)
        {
            var difyResult = await _difyClient.CreateTagAsync(new Contracts.RequestModels.Dify.CreateTagRequest { Name = createDto.TagName });
            if (!string.IsNullOrEmpty(difyResult.Id))
            {
                return await _tagRepository.InsertAsync(new Def_Tag
                {
                    DifyTagId = difyResult.Id,
                    TagName = createDto.TagName,
                    TagType = (int)TagTypeEnum.App
                });
            }
            return false;
        }

        public async Task<bool> UpdateTagAsync(long tagId, CreateTagDto request)
        {
            var tag = await _tagRepository.GetFirstAsync(t => t.Id == tagId);
            var difyResult = await _difyClient.UpdateTagAsync(tag.DifyTagId, new Contracts.RequestModels.Dify.UpdateTagRequest { Name = request.TagName });
            if (!string.IsNullOrEmpty(difyResult.Id))
            {
                return await _tagRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag
                {
                    TagName = request.TagName
                }, t => t.Id == tagId);
            }
            return false;
        }

        public async Task<bool> DeleteTag(string difyTagId)
        {
            var tag = await _tagRepository.GetFirstAsync(t => t.DifyTagId == difyTagId && t.IsActive) ?? throw new AgentCentralException("Dify tag not found");
            return await _tagRepository.UpdateSetColumnsTrueAsync(t => new Def_Tag { IsActive = false }, t => t.Id == tag.Id);
        }
    }
}
