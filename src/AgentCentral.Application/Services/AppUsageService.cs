using System.Linq.Expressions;
using AgentCentral.Application.Contracts.IService.App;
using AgentCentral.Application.Contracts.RequestModels.AppUsage;
using AgentCentral.Application.Contracts.ResponseModels.AppUsage;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    public class AppUsageService : IAppUsageService
    {
        private readonly IBaseRepository<Def_App_Usage> _repository;
        private readonly IBaseRepository<Def_App_Usage_Stats> _statsRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<AppUsageService> _logger;
        private readonly UserContext _userContext;

        public AppUsageService(
            IBaseRepository<Def_App_Usage> repository,
            IBaseRepository<Def_App_Usage_Stats> statsRepository,
            IMapper mapper,
            ILogger<AppUsageService> logger,
            UserContext userContext)
        {
            _repository = repository;
            _statsRepository = statsRepository;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
        }

        public async Task<AppUsageResponse> GetByAppIdAsync(string appId)
        {
            Def_App_Usage usage = await _repository.GetFirstAsync(x => x.AppId == appId);
            if (usage == null)
            {
                throw new AgentCentralException("AppUsage not found");
            }
            return _mapper.Map<AppUsageResponse>(usage);
        }

        public async Task<AppUsageResponse> CreateAsync(CreateAppUsageRequest request)
        {
            try
            {
                Def_App_Usage_Stats stats = await _statsRepository.GetFirstAsync(x => x.AppId == request.AppId);

                using var context = _repository.CreateContext();
                // 创建使用记录
                Def_App_Usage usage = _mapper.Map<Def_App_Usage>(request);
                usage.CreateBy = request.UserId;//dify给不了token，故只能用透传
                _ = await _repository.InsertAsync(usage);

                // 更新或创建统计记录
                if (stats == null)
                {
                    stats = new Def_App_Usage_Stats
                    {
                        AppId = request.AppId,
                        Runs = 1,
                        Joins = 0
                    };
                    _ = await _statsRepository.InsertAsync(stats);
                }
                else
                {
                    _ = await _statsRepository.UpdateAsync(it => new Def_App_Usage_Stats
                    {
                        Runs = it.Runs + 1
                    },
                    it => it.AppId == request.AppId);
                }
                context.Commit();
                return _mapper.Map<AppUsageResponse>(usage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create app usage for AppId {AppId}", request.AppId);
                throw new AgentCentralException("Create failed");
            }

        }

        public async Task<AppUsageResponse> UpdateAsync(string appId, UpdateAppUsageRequest request)
        {
            Def_App_Usage usage = await _repository.GetFirstAsync(x => x.AppId == appId);
            if (usage == null)
            {
                throw new AgentCentralException("AppUsage not found");
            }

            _ = _mapper.Map(request, usage);
            bool result = await _repository.UpdateAsync(usage);
            if (!result)
            {
                throw new AgentCentralException("Update failed");
            }

            return _mapper.Map<AppUsageResponse>(usage);
        }

        public async Task<bool> DeleteAsync(string appId)
        {
            return await _repository.DeleteAsync(x => x.AppId == appId);
        }

        public async Task<long> GetLast24HoursUsageAsync(string appId)
        {
            DateTime startTime = DateTime.UtcNow.AddHours(-24);
            Expression<Func<Def_App_Usage, bool>> expression = x =>
                x.AppId == appId &&
                x.CreateTime >= startTime &&
                x.IsActive;

            int totalSeconds = await _repository.GetSumAsync(expression, x => x.CostSeconds);
            return totalSeconds;
        }

        public async Task<Dictionary<string, long>> GetLast15DaysUsageAsync(long mainAppId, DateTime createTime)
        {
            Dictionary<string, long> result = [];
            DateTime today = DateTime.UtcNow.Date;

            // 构建查询表达式
            Expression<Func<Def_App_Usage, bool>> expression = x =>
                x.MainAppId == mainAppId &&
                x.IsActive;

            // 获取所有数据
            List<Def_App_Usage> usageList = await _repository.GetListAsync(expression);
            DateTime startDate = createTime.Date;

            // 按日期分组统计
            for (int i = 0; i < (today - startDate).Days + 1; i++)
            {
                DateTime date = startDate.AddDays(i);
                DateTime nextDate = date.AddDays(1);
                string dateStr = date.ToString("yyyy-MM-dd");

                int dayUsage = usageList
                    .Where(x => x.CreateTime >= date && x.CreateTime < nextDate)
                    .Sum(x => x.CostSeconds);

                result.Add(dateStr, dayUsage);
            }

            return result;
        }

    }
}