using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ReviewRecords;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure;
using AutoMapper;

namespace AgentCentral.Application.Services;

public class ReviewRecordService : IReviewRecordService, IScopedService
{
    private readonly IDataChangeLogRepository _dataChangeLogRepository;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IMapper _mapper;

    public ReviewRecordService(IDataChangeLogRepository dataChangeLogRepository,
        IUserInfoRepository userInfoRepository,
        IMapper mapper)
    {
        _dataChangeLogRepository = dataChangeLogRepository;
        _userInfoRepository = userInfoRepository;
        _mapper = mapper;
    }

    public async Task<PageModelDto<RecordResultModel>> GetAgentHistoryAsync(SearchReviewRecordDto search)
    {
        var list = await _dataChangeLogRepository.GetAppChangeHistoryAsync(_mapper.Map<SearchReviewRecordModel>(search));

        var records = _mapper.Map<List<RecordResultModel>>(list.Item1);

        // 获取所有创建人的详细信息
        var creatorIds = records.Where(r => r.CreatorId > 0)
                                 .Select(r => r.CreatorId)
                                 .Distinct()
                                 .ToList();

        if (creatorIds.Count > 0)
        {
            var userInfos = await _userInfoRepository.GetListAsync(u => creatorIds.Contains(u.UserId));
            var userInfoDict = userInfos.ToDictionary(u => u.UserId, u => u);

            foreach (var record in records)
            {
                if (record.CreatorId > 0 && userInfoDict.TryGetValue(record.CreatorId, out var userInfo))
                {
                    record.Email = userInfo.Email;
                    record.Creator = userInfo.UserName;
                    record.CompanyName = userInfo.CompanyName;
                    record.DepartmentName = userInfo.DepartmentName;
                }
            }
        }

        var result = new PageModelDto<RecordResultModel>(
            search.PageIndex,
            search.PageSize,
            records,
            list.Item2
        );

        return result;
    }
}
