using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.UserTag;
using AgentCentral.Application.Contracts.ResponseModels.UserTag;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// 用户标签服务实现
    /// </summary>
    public class UserTagService : IUserTagService, IScopedService
    {
        private readonly IMapper _mapper;
        private readonly IUserTagRepository _userTagRepository;
        private readonly IBaseRepository<Def_User_Tag_Relation> _userTagRelationRepository;
        private readonly ILogger<UserTagService> _logger;
        private readonly IUserInfoRepository _userInfoRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserTagService(
            IMapper mapper,
            IUserTagRepository userTagRepository,
            IBaseRepository<Def_User_Tag_Relation> userTagRelationRepository,
            ILogger<UserTagService> logger,
            IUserInfoRepository userInfoRepository)
        {
            _mapper = mapper;
            _userTagRepository = userTagRepository;
            _userTagRelationRepository = userTagRelationRepository;
            _logger = logger;
            _userInfoRepository = userInfoRepository;
        }

        /// <summary>
        /// 创建用户标签
        /// </summary>
        public async Task<bool> CreateUserTagAsync(CreateUserTagRequest request)
        {
            // 检查标签编码是否已存在
            bool exists = await IsTagCodeExistsAsync(request.TagCode);
            if (exists)
            {
                throw new AgentCentralException("标签编码已存在");
            }

            try
            {
                Def_User_Tag userTag = new Def_User_Tag
                {
                    TagName = request.TagName,
                    TagCode = request.TagCode,
                    TagType = request.TagType,
                };

                return await _userTagRepository.InsertAsync(userTag);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户标签失败: {Message}", ex.Message);
                throw new AgentCentralException("创建用户标签失败");
            }
        }

        /// <summary>
        /// 更新用户标签
        /// </summary>
        public async Task<bool> UpdateUserTagAsync(long id, UpdateUserTagRequest request)
        {
            Def_User_Tag userTag = await _userTagRepository.GetByIdAsync(id);
            if (userTag == null)
            {
                throw new AgentCentralException("用户标签不存在");
            }

            try
            {
                userTag.TagName = request.TagName;
                userTag.TagType = request.TagType;
                userTag.UpdateTime = DateTime.UtcNow;

                return await _userTagRepository.UpdateAsync(userTag);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户标签失败: {Message}", ex.Message);
                throw new AgentCentralException("更新用户标签失败");
            }
        }

        /// <summary>
        /// 删除用户标签
        /// </summary>
        public async Task<bool> DeleteUserTagAsync(long id)
        {
            try
            {
                using var context = _userTagRepository.CreateContext();

                // 更新标签状态为无效
                await _userTagRepository.UpdateSetColumnsTrueAsync(
                    t => new Def_User_Tag { IsActive = false },
                    t => t.Id == id
                );

                // 更新关联关系状态为无效
                await _userTagRelationRepository.UpdateSetColumnsTrueAsync(
                    r => new Def_User_Tag_Relation { IsActive = false },
                    r => r.TagId == id && r.IsActive
                );

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户标签失败: {Message}", ex.Message);
                throw new AgentCentralException("删除用户标签失败");
            }
        }

        /// <summary>
        /// 获取用户标签
        /// </summary>
        public async Task<UserTagDto> GetUserTagAsync(long id)
        {
            Def_User_Tag userTag = await _userTagRepository.GetByIdAsync(id);
            if (userTag == null)
            {
                throw new AgentCentralException("用户标签不存在");
            }

            return _mapper.Map<UserTagDto>(userTag);
        }

        /// <summary>
        /// 分页查询用户标签
        /// </summary>
        public async Task<PageModelDto<UserTagDto>> SearchUserTagsAsync(SearchUserTagRequest request)
        {
            Expression<Func<Def_User_Tag, bool>> filterExpression = Expressionable.Create<Def_User_Tag>()
                .AndIF(!string.IsNullOrEmpty(request.TagName), x => x.TagName.Contains(request.TagName))
                .AndIF(!string.IsNullOrEmpty(request.TagCode), x => x.TagCode.Contains(request.TagCode))
                .AndIF(!string.IsNullOrEmpty(request.TagType), x => x.TagType == request.TagType)
                .And(x => x.IsActive == true)
                .ToExpression();

            (List<Def_User_Tag> tags, int total) = await _userTagRepository.GetPageListAsync(
                filterExpression,
                request.PageIndex,
                request.PageSize,
                tag => tag.CreateTime,
                false
            );

            List<UserTagDto> userTagDtos = _mapper.Map<List<UserTagDto>>(tags);
            return new PageModelDto<UserTagDto>(request.PageIndex, request.PageSize, userTagDtos, total);
        }

        /// <summary>
        /// 保存用户标签关联
        /// </summary>
        public async Task<bool> SaveUserTagRelationAsync(UserTagRelationRequest request)
        {
            try
            {
                using var context = _userTagRelationRepository.CreateContext();

                // 先将该用户的所有标签关联设为无效
                await _userTagRelationRepository.UpdateSetColumnsTrueAsync(
                    r => new Def_User_Tag_Relation { IsActive = false },
                    r => r.UserId == request.UserId && r.IsActive
                );

                // 添加新的标签关联
                List<Def_User_Tag_Relation> relations = new List<Def_User_Tag_Relation>();
                foreach (long tagId in request.TagIds)
                {
                    relations.Add(new Def_User_Tag_Relation
                    {
                        UserId = request.UserId,
                        TagId = tagId
                    });
                }

                if (relations.Any())
                {
                    await _userTagRelationRepository.InsertRangeAsync(relations);
                }

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存用户标签关联失败: {Message}", ex.Message);
                throw new AgentCentralException("保存用户标签关联失败");
            }
        }

        /// <summary>
        /// 获取用户标签列表
        /// </summary>
        public async Task<List<UserTagDto>> GetUserTagsAsync(long userId)
        {
            try
            {
                // 检查用户是否存在
                var existingUser = await _userInfoRepository.GetUserDetailByUserIdAsync(userId);
                if (existingUser == null)
                {
                    throw new AgentCentralException("用户不存在");
                }
                List<Def_User_Tag> userTags = await _userTagRepository.GetUserTagsAsync(existingUser.Id);
                return _mapper.Map<List<UserTagDto>>(userTags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户标签列表失败: {Message}", ex.Message);
                throw new AgentCentralException("获取用户标签列表失败");
            }
        }

        /// <summary>
        /// 检查标签编码是否存在
        /// </summary>
        public async Task<bool> IsTagCodeExistsAsync(string tagCode, long? excludeId = null)
        {
            try
            {
                return await _userTagRepository.IsTagCodeExistsAsync(tagCode, excludeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查标签编码是否存在失败: {Message}", ex.Message);
                throw new AgentCentralException("检查标签编码是否存在失败");
            }
        }

        /// <summary>
        /// 获取所有标签列表（用于下拉选择）
        /// </summary>
        /// <param name="tagType">标签类型（可选）</param>
        /// <returns>标签列表</returns>
        public async Task<List<UserTagDto>> GetAllTagsForSelectAsync(string tenantId, string tagType = null)
        {
            try
            {
                Expression<Func<Def_User_Tag, bool>> filterExpression = Expressionable.Create<Def_User_Tag>()
                    .AndIF(!string.IsNullOrEmpty(tagType), x => x.TagType == tagType)
                    .And(x => x.IsActive == true)
                    .ToExpression();

                List<Def_User_Tag> tags = await _userTagRepository.GetListAsync(filterExpression, "TagName", true);
                return _mapper.Map<List<UserTagDto>>(tags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有标签列表失败: {Message}", ex.Message);
                throw new AgentCentralException("获取所有标签列表失败");
            }
        }
    }
}
