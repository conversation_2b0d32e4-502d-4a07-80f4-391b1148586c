using System.Linq.Expressions;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Application.Contracts.ResponseModels.Dify;
using AgentCentral.Application.Services.Workflow;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Manager;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.BlobProvider;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// Mp_App审核服务实现类
    /// </summary>
    public class MpAppReviewService : IMpAppReviewService, IScopedService
    {
        private readonly IMpAppRepository _mpAppRepository;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly ILogger<MpAppReviewService> _logger;
        private readonly WorkflowManager _workFlowManager;
        private readonly IWorkflowService _workflowService;
        private readonly IAttachmentService _attachmentService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MpAppReviewService(
            IMpAppRepository mpAppRepository,
            IMapper mapper,
            UserContext userContext,
            ILogger<MpAppReviewService> logger,
            WorkflowManager workflowManager,
            IWorkflowService workflowService,
            IAttachmentService attachmentService)
        {
            _mpAppRepository = mpAppRepository;
            _mapper = mapper;
            _userContext = userContext;
            _logger = logger;
            _workFlowManager = workflowManager;
            _workflowService = workflowService;
            _attachmentService = attachmentService;
        }

        /// <summary>
        /// 获取审核应用分页列表（统一接口，支持多状态查询）
        /// </summary>
        public async Task<PageModelDto<MpAppReviewListDto>> GetReviewPageListAsync(MpAppReviewSearchPageDto searchDto)
        {
            try
            {
                Expression<Func<Mp_App, bool>> filterExpression = BuildFilterExpression(searchDto);
                
                var orderBy = string.IsNullOrEmpty(searchDto.OrderBy) ? "CreateTime" : searchDto.OrderBy;
                var isAsc = searchDto.IsAsc;

                (var apps, int total) = await _mpAppRepository.GetReviewPageListAsync(
                    filterExpression,
                    searchDto.PageIndex,
                    searchDto.PageSize,
                    orderBy,
                    isAsc);

                var businessIds = apps.Select(x => x.Id).ToList();

                var dtos = _mapper.Map<List<MpAppReviewListDto>>(apps);
                var workflowInfosDictionary = await _workFlowManager.GetWorkflowInfosDictionaryAsync(businessIds);
                dtos.ForEach(item =>
                {
                    var currentNodeDescription = workflowInfosDictionary.GetValueOrDefault(item.Id)?.CurrentNodeDescription;
                    if (!string.IsNullOrEmpty(currentNodeDescription))
                    {
                        var commentsDto = JsonConvert.DeserializeObject<MpAppWorkflowCommentsDto>(currentNodeDescription);
                        item.ReviewRemark = commentsDto?.Comments;
                    }
                });
                return new PageModelDto<MpAppReviewListDto>(searchDto.PageIndex, searchDto.PageSize, dtos, total);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting review page list");
                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to get review list");
            }
        }

        /// <summary>
        /// 获取应用详情
        /// </summary>
        public async Task<MpAppDetailDto> GetAppDetailAsync(long appId)
        {
            try
            {
                Mp_App app = await _mpAppRepository.GetByAppIdAsync(appId);
                if (app == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "Application not found");
                }

                var result = _mapper.Map<MpAppDetailDto>(app);
                await BuildMpAppDetailDto(result);
                result.WorkflowLogs = await _workflowService.GetWorkflowLogAsync(appId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting app detail for ID: {AppId}", appId);
                throw;
            }
        }

        /// <summary>
        /// 更新隐私策略审核结果
        /// </summary>
        public async Task<bool> UpdatePrivacyPolicyReviewResultAsync(long appId, MpPrivacyPolicyReviewResponse reviewResult)
        {
            try
            {
                string reviewResultJson = reviewResult != null ? JsonConvert.SerializeObject(reviewResult) : null;
                
                bool updateResult = await _mpAppRepository.UpdateSetColumnsTrueAsync(
                    set => new Mp_App
                    {
                        PrivacyPolicyReviewResult = reviewResultJson,
                    },
                    where => where.Id == appId && where.IsActive == true
                );
                
                if (updateResult)
                {
                    _logger.LogInformation("Successfully updated privacy policy review result for App {AppId}", appId);
                }
                else
                {
                    _logger.LogWarning("Failed to update privacy policy review result for App {AppId}", appId);
                }
                
                return updateResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating privacy policy review result for App {AppId}", appId);
                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to update privacy policy review result");
            }
        }

        /// <summary>
        /// 清空隐私策略审核结果
        /// </summary>
        public async Task<bool> ClearPrivacyPolicyReviewResultAsync(long appId)
        {
            try
            {
                bool updateResult = await _mpAppRepository.UpdateSetColumnsTrueAsync(
                    set => new Mp_App
                    {
                        PrivacyPolicyReviewResult = null,
                    },
                    where => where.Id == appId
                );
                
                if (updateResult)
                {
                    _logger.LogInformation("Successfully cleared privacy policy review result for App {AppId}", appId);
                }
                else
                {
                    _logger.LogWarning("Failed to clear privacy policy review result for App {AppId}", appId);
                }
                
                return updateResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing privacy policy review result for App {AppId}", appId);
                throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to clear privacy policy review result");
            }
        }

        private async Task<MpAppDetailDto> BuildMpAppDetailDto(MpAppDetailDto result)
        {
            if (long.TryParse(result.AppIcon, out var iconId) && iconId > 0)
            {
                result.AppIcon = await _attachmentService.GetAccessUrlAsync(iconId, CancellationToken.None);
            }
            if ( result.PrivacyPolicyDocumentType == PrivacyPolicyDocumentTypeEnum.Document && long.TryParse(result.PrivacyPolicyFileName, out var privacyId) && privacyId > 0)
            {
                var privacyAttachment = await _attachmentService.GetAttachmentByIdAsync(privacyId);
                result.PrivacyPolicyUrl = await _attachmentService.GetAccessUrlAsync(privacyAttachment?.FileName);
                result.PrivacyPolicyFileName = privacyAttachment?.RealName;
            }
            if (long.TryParse(result.ApkFilePath, out var apkId) && apkId > 0)
            {
                result.ApkFilePath = await _attachmentService.GetAccessUrlAsync(apkId, CancellationToken.None);
            }

            var screenShotAttachments = await _attachmentService.GetAttachmentsAsync(result.Id, AttachmentTypeEnum.MpScreenShot);
            var tasks = screenShotAttachments.Select(item => _attachmentService.GetAccessUrlAsync(item.FileName)).ToList();
            result.Screenshots = await Task.WhenAll(tasks);
            return result;
        }

        #region Private Methods

        /// <summary>
        /// 构建筛选条件（统一接口，支持多状态查询）
        /// </summary>
        private Expression<Func<Mp_App, bool>> BuildFilterExpression(MpAppReviewSearchPageDto searchDto)
        {
            return Expressionable.Create<Mp_App>()
                .AndIF(searchDto.AppStatus is { Length: > 0 }, x => searchDto.AppStatus.Contains(x.AppStatus))
                .AndIF(!string.IsNullOrEmpty(searchDto.AppName), x => x.AppName.Contains(searchDto.AppName))
                .AndIF(!string.IsNullOrEmpty(searchDto.DeveloperName), x => x.DeveloperName.Contains(searchDto.DeveloperName))
                .AndIF(!string.IsNullOrEmpty(searchDto.AppId), x => x.AppId == searchDto.AppId)
                .AndIF(searchDto.Id.HasValue, x => x.Id == searchDto.Id.Value)
                .AndIF(searchDto.UploadType.HasValue, x => x.UploadType == searchDto.UploadType.Value)
                .AndIF(searchDto.PlatformType.HasValue, x => x.PlatformType == searchDto.PlatformType.Value)
                .AndIF(searchDto.MonetizationType.HasValue, x => x.MonetizationType == searchDto.MonetizationType.Value)
                .AndIF(searchDto.SubmitDateStart.HasValue, x => x.SubmitDate >= searchDto.SubmitDateStart.Value)
                .AndIF(searchDto.SubmitDateEnd.HasValue, x => x.SubmitDate <= searchDto.SubmitDateEnd.Value)
                .AndIF(searchDto.MinOverallScore.HasValue, x => x.OverallScore >= searchDto.MinOverallScore.Value)
                .AndIF(searchDto.MaxOverallScore.HasValue, x => x.OverallScore <= searchDto.MaxOverallScore.Value)
                .ToExpression();
        }

        #endregion
    }
} 