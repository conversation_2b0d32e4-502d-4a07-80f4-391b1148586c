using AgentCentral.Application.Contracts.Dtos.ChangeLog;
using AgentCentral.Application.Contracts.IServices.ChangeLogs;
using AgentCentral.Domain.IRepository;
using AutoMapper;
using Item.Internal.ChangeLog;
using Item.Internal.ChangeLog.Models;

namespace AgentCentral.Application.Services.ChangeLogs;

/// <summary>
/// Change History API Service
/// </summary>
public class ChangeHistoryService(
    IMapper mapper,
    IChangeLogService changeLogService,
    IDataChangeLogRepository changeLogRepository) : IChangeHistoryService
{
    private readonly IChangeLogService _changeLogService = changeLogService;
    private readonly IDataChangeLogRepository _changeLogRepository = changeLogRepository;
    private readonly IMapper _mapper = mapper;

    /// <summary>
    /// Retrieves change logs asynchronously based on the search model
    /// </summary>
    /// <param name="searchModel">The search criteria for change logs</param>
    /// <returns>A paged result of change log data</returns>
    public async Task<PageingResultModel<ChangeLogData>> GetChangeLogsAsync(SearchModel searchModel)
    {
        var pageResult = await _changeLogService.GetChangeLogAsync(searchModel);
        return new PageingResultModel<ChangeLogData>()
        {
            PageIndex = pageResult.PageIndex,
            PageSize = pageResult.PageSize,
            TotalCount = pageResult.TotalCount,
            Data = _mapper.Map<List<ChangeLogData>>(pageResult.Data)
        };
    }

    public async Task<List<ChangeLogDto>> GetChangeLogsAsync(long businessId)
    {
        var datas = await _changeLogRepository.GetListAsync(x => x.AppId == businessId);
        return _mapper.Map<List<ChangeLogDto>>(datas);
    }
}
