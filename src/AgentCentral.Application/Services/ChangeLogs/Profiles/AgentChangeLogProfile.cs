using AgentCentral.Domain.Entities;
using AgentCentral.Infrastructure;
using Item.Internal.ChangeLog;
using Item.Internal.ChangeLog.Attributes;

namespace AgentCentral.Application.Services.ChangeLogs.Profiles;

[CategoryProfile(CategoryType.EDIT_AGNENT_INFO)]
public class AgentChangeLogProfile : ChangeLogProfile, IScopedService
{
    public AgentChangeLogProfile()
    {
        CreateMap<Def_App>()
            .AddMap(x => x.AppStatus)
            .AddMap(x => x.AppName)
            .AddMap(x => x.AppTag)
            .AddMap(x => x.AppIcon)
            .AddMap(x => x.AppMode)
            .AddMap(x => x.Department)
            .AddMap(x => x.AppDescription);
    }
}
