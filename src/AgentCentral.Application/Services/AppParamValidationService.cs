using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.Exceptions;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// Parameter validation service implementation
    /// </summary>
    public class AppParamValidationService : IAppParamValidationService
    {
        public AppParamValidationService()
        { }

        /// <summary>
        /// Validate parameter definition
        /// </summary>
        public void ValidateParamDefinition(CreateAppParamDto createAppParam)
        {
            if (!string.IsNullOrEmpty(createAppParam.ValidationRules))
            {
                var result = JsonConvert.DeserializeObject<ValidationRuleModel>(createAppParam.ValidationRules);

                if (IsAllPropertiesDefault(result))
                {
                    throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Invalid validation rules format: The JSON structure does not match validation rule definition");
                }
                createAppParam.ValidationRules = JsonConvert.SerializeObject(
                    result,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore,
                        ContractResolver = new DefaultContractResolver
                        {
                            NamingStrategy = new CamelCaseNamingStrategy()
                        }
                    });
            }

            if (createAppParam.ParamType == AppParamTypeEnum.Dropdown && !string.IsNullOrEmpty(createAppParam.Options))
            {
                var optionsList = JsonConvert.DeserializeObject<List<SelectOption>>(createAppParam.Options);

                if (optionsList?.Any(x => string.IsNullOrEmpty(x.Value) || string.IsNullOrEmpty(x.Label)) == true)
                {
                    throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "Options must contain both Value and Label");
                }

                createAppParam.Options = JsonConvert.SerializeObject(
                    optionsList,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore,
                        ContractResolver = new DefaultContractResolver
                        {
                            NamingStrategy = new CamelCaseNamingStrategy()
                        }
                    });
            }
        }

        /// <summary>
        /// Validate parameter value
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateParamValue(AppParamDto param, string value)
        {
            if (string.IsNullOrEmpty(param.ValidationRules))
            {
                return (true, string.Empty);
            }

            try
            {
                var rules = JsonConvert.DeserializeObject<ValidationRuleModel>(param.ValidationRules);

                // Required field validation
                if (rules.Required && string.IsNullOrEmpty(value))
                {
                    return (false, rules.RequiredMessage ?? "This field is required");
                }

                // If value is empty and not required, validation passes
                if (string.IsNullOrEmpty(value) && !rules.Required)
                {
                    return (true, string.Empty);
                }

                // Validate based on parameter type
                switch (param.ParamType)
                {
                    case AppParamTypeEnum.Dropdown: // Dropdown
                        return ValidateSelect(value, rules, param);
                    case AppParamTypeEnum.LongText: // Long text
                    case AppParamTypeEnum.ShortText: // Short text
                        return ValidateText(value, rules);
                    case AppParamTypeEnum.Number: // Number
                        return ValidateNumber(value, rules);
                    default:
                        return (false, "Unsupported parameter type");
                }
            }
            catch (JsonException)
            {
                return (false, "Invalid validation rules format");
            }
        }

        /// <summary>
        /// Validate text
        /// </summary>
        private static (bool IsValid, string ErrorMessage) ValidateText(string value, ValidationRuleModel rules)
        {
            if (rules.MinLength.HasValue && value.Length < rules.MinLength.Value)
            {
                return (false, $"Length cannot be less than {rules.MinLength.Value} characters");
            }

            if (rules.MaxLength.HasValue && value.Length > rules.MaxLength.Value)
            {
                return (false, $"Length cannot exceed {rules.MaxLength.Value} characters");
            }

            if (!string.IsNullOrEmpty(rules.Pattern))
            {
                if (!System.Text.RegularExpressions.Regex.IsMatch(value, rules.Pattern))
                {
                    return (false, rules.PatternMessage ?? "Invalid format");
                }
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// Validate number
        /// </summary>
        private static (bool IsValid, string ErrorMessage) ValidateNumber(string value, ValidationRuleModel rules)
        {
            if (!decimal.TryParse(value, out decimal numValue))
            {
                return (false, "Please enter a valid number");
            }

            if (rules.Min.HasValue && numValue < rules.Min.Value)
            {
                return (false, $"Cannot be less than {rules.Min.Value}");
            }

            if (rules.Max.HasValue && numValue > rules.Max.Value)
            {
                return (false, $"Cannot exceed {rules.Max.Value}");
            }

            if (rules.Precision.HasValue)
            {
                string[] parts = value.Split('.');
                if (parts.Length > 1 && parts[1].Length > rules.Precision.Value)
                {
                    return (false, $"Decimal places cannot exceed {rules.Precision.Value}");
                }
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// Validate dropdown selection
        /// </summary>
        private static (bool IsValid, string ErrorMessage) ValidateSelect(string value, ValidationRuleModel rules, AppParamDto param)
        {
            try
            {
                var options = JsonConvert.DeserializeObject<List<SelectOption>>(param.Options);
                var allowedValues = options.Select(o => o.Value).ToList();

                if (rules.Multiple.HasValue && rules.Multiple.Value)
                {
                    if (!IsValidJson(value))
                    {
                        return (false, "Invalid option value format");
                    }

                    var selectedValues = JsonConvert.DeserializeObject<List<string>>(value);

                    if (rules.Required && (selectedValues == null || selectedValues.Count == 0))
                    {
                        return (false, rules.RequiredMessage ?? "Please select at least one option");
                    }

                    if (rules.MaxSelect.HasValue && selectedValues?.Count > rules.MaxSelect.Value)
                    {
                        return (false, $"Cannot select more than {rules.MaxSelect.Value} options");
                    }

                    if (selectedValues?.Any(v => !allowedValues.Contains(v)) == true)
                    {
                        return (false, "Contains invalid option values");
                    }
                }
                else
                {
                    if (rules.Required && string.IsNullOrEmpty(value))
                    {
                        return (false, rules.RequiredMessage ?? "Please select an option");
                    }

                    if (!string.IsNullOrEmpty(value) && !allowedValues.Contains(value))
                    {
                        return (false, "Invalid option value");
                    }
                }

                return (true, string.Empty);
            }
            catch (JsonException)
            {
                return (false, "Invalid option value format");
            }
        }

        /// <summary>
        /// Validate if the string is valid JSON format
        /// </summary>
        private static bool IsValidJson(string value)
        {
            try
            {
                JsonConvert.DeserializeObject<List<string>>(value);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private static bool IsAllPropertiesDefault(ValidationRuleModel model)
        {
            if (model == null) return true;

            foreach (var prop in typeof(ValidationRuleModel).GetProperties())
            {
                var value = prop.GetValue(model);
                if (value != null && !value.Equals(GetDefaultValue(prop.PropertyType)))
                {
                    return false;
                }
            }
            return true;
        }

        private static object GetDefaultValue(Type type)
        {
            return type.IsValueType ? Activator.CreateInstance(type) : null;
        }
    }
}
