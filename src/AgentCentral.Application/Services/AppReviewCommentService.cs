using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.AppReviewComment;
using AgentCentral.Application.Contracts.ResponseModels.AppReviewComment;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Models;
using AutoMapper;

using Item.BlobProvider;

using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace AgentCentral.Application.Services
{
    public class AppReviewCommentService : IAppReviewCommentService
    {
        private readonly IAppReviewCommentRepository _appReviewCommentRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<AppReviewCommentService> _logger;
        private readonly UserContext _userContext;
        private readonly IBlobContainer _blobContainer;

        public AppReviewCommentService(
            IAppReviewCommentRepository appReviewCommentRepository,
            IMapper mapper,
            ILogger<AppReviewCommentService> logger,
            UserContext userContext, IBlobContainer blobContainer)
        {
            _appReviewCommentRepository = appReviewCommentRepository;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
            _blobContainer = blobContainer;
        }

        public async Task<PageModelDto<AppReviewCommentResponse>> GetReviewCommentsAsync(string appId,
            BaseSearchPageDto searchPageDto)
        {
            var (reviews, total) =
                await _appReviewCommentRepository.GetReviewCommentsAsync(appId,
                    int.TryParse(searchPageDto.SearchText, out var ratingStar) ? ratingStar : null,
                    searchPageDto.PageIndex,
                    searchPageDto.PageSize, searchPageDto.OrderBy, searchPageDto.IsAsc);
            foreach (var review in reviews.Where(w => !w.FileName.IsNullOrEmpty()))
            {
                review.AccessUrl = await _blobContainer.GetAccessUrl(review.FileName);
            }

            var responses = _mapper.Map<List<AppReviewCommentResponse>>(reviews);
            return new PageModelDto<AppReviewCommentResponse>(searchPageDto.PageIndex, searchPageDto.PageSize,
                responses, total);
        }

        public async Task<AppReviewStatsResponse> GetReviewStatsAsync(string appId)
        {
            var (avgRating, totalCount, ratingDistribution) =
                await _appReviewCommentRepository.GetReviewStatsAsync(appId);
            return new AppReviewStatsResponse
            {
                AverageRating = avgRating,
                TotalCount = totalCount,
                RatingDistribution = ratingDistribution
            };
        }

        public async Task<bool> CreateReviewAsync(string appId, CreateAppReviewCommentRequest request)
        {
            var review = _mapper.Map<Def_App_Review_Comment>(request);
            review.AppId = appId;
            return await _appReviewCommentRepository.InsertAsync(review);
        }

        public async Task<bool> DeleteByIdsAsync(List<long> ids)
        {
            var comments = await _appReviewCommentRepository.GetListAsync(p => ids.Contains(p.Id));
            return await _appReviewCommentRepository.DeleteAsync(comments);
        }

        public async Task<List<AppReviewCommentResponse>> GetReviewCommentsListAsync(string appId)
        {
            var data = (await _appReviewCommentRepository.GetListAsync()).Where(p => p.AppId == appId);
            var response = _mapper.Map<List<AppReviewCommentResponse>>(data);
            return response;
        }
    }
}