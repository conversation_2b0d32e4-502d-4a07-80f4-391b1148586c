using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.CodeGenerator;
using AgentCentral.Infrastructure.Redis;
using Item.Common.Lib.Common;

namespace AgentCentral.Application.Services
{
    public class AppIdGeneratorService : CodeGeneratorAbstract, IAppIdGeneratorService
    {
        private string _codePrefix;

        public AppIdGeneratorService(IRedisService redisService) : base(redisService)
        { }

        protected override string CodeSeparator => string.Empty;

        protected override int CodeMaxNum => 8;

        protected override int MinCodeLength => 8;

        protected override string CodePrefix { get { return _codePrefix; } set { _codePrefix = value; } }

        private readonly Dictionary<string, string> AppModeMapping = new()
        {
            { "workflow","W" },
            { "advanced-chat","C" },
            { "agent-chat","A" },
            { "chat","C" },
            { "completion","T" }
        };

        public async Task<string> GeneratorIdAsync(AppPermissionEnum appPermission, string appMode)
        {
            CodePrefix = AppModeMapping.GetValue(appMode);
            CodePrefix += appPermission == AppPermissionEnum.Public ? "G" : "P";
            var code = await GenerateCodeAsync();
            return code;
        }
    }
}
