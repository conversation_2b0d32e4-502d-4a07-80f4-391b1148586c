using AutoMapper;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.ResponseModels.AppSubscribe;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure.Exceptions;
using Item.BlobProvider;
using Item.Common.Lib.LogUtil;
using Microsoft.IdentityModel.Tokens;
using AgentCentral.Application.Contracts.ResponseModels.App;
using System.Linq.Expressions;
using SqlSugar;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Application.Contracts.IService.App;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Application.Contracts.Dtos.App;

namespace AgentCentral.Application.Services
{
    public class AppSubscribeService : BaseAppService, IAppSubscribeService
    {
        private readonly IAppSubscribeRepository _appSubscribeRepository;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly IBaseRepository<Def_App_Usage_Stats> _userStatsRepository;
        private readonly IBlobContainer _blobContainer;
        private readonly IAppUsageService _appUsageService;
        private readonly IBaseRepository<Def_App_Usage> _usageRepository;
        private readonly IAppReviewCommentService _appReviewCommentService;
        private readonly IAppRepository _appRepository;
        private readonly IBaseRepository<Def_App_Param_Value> _valueRepository;

        public AppSubscribeService(
            IAppSubscribeRepository appSubscribeRepository,
            IMapper mapper,
            UserContext userContext,
            IBaseRepository<Def_App_Usage_Stats> userStatsRepository,
            IBlobContainer blobContainer,
            IAppUsageService appUsageService,
            IBaseRepository<Def_App_Usage> usageRepository,
            IAppReviewCommentService appReviewCommentService,
            IAppRepository appRepository,
            IBaseRepository<Def_App_Param_Value> valueRepository) : base(appRepository)
        {
            _appSubscribeRepository = appSubscribeRepository;
            _mapper = mapper;
            _userContext = userContext;
            _userStatsRepository = userStatsRepository;
            _blobContainer = blobContainer;
            _appUsageService = appUsageService;
            _usageRepository = usageRepository;
            _appReviewCommentService = appReviewCommentService;
            _appRepository = appRepository;
            _valueRepository = valueRepository;
        }

        public async Task<PageModelDto<AppSubscribeResponse>> GetSubscribeUsersAsync(string appId, int pageIndex,
            int pageSize)
        {
            var (subscriptions, total) =
                await _appSubscribeRepository.GetSubscribeUsersAsync(appId, pageIndex, pageSize);

            foreach (var subscription in subscriptions.Where(subscription => !subscription.FileName.IsNullOrEmpty()))
            {
                subscription.AccessUrl = await _blobContainer.GetAccessUrl(subscription.FileName);
            }

            var responses = _mapper.Map<List<AppSubscribeResponse>>(subscriptions);

            return new PageModelDto<AppSubscribeResponse>(pageIndex, pageSize, responses, total);
        }

        public async Task<int> GetSubscribeCountAsync(string appId)
        {
            return await _appSubscribeRepository.CountAsync(c => c.AppId == appId);
        }

        public async Task<bool> SubscribeAsync(string appId)
        {
            // 检查是否已订阅
            if (await CheckSubscribedAsync(appId))
            {
                throw new AgentCentralException("Already subscribed");
            }

            try
            {
                Def_App_Usage_Stats stats = await _userStatsRepository.GetFirstAsync(x => x.AppId == appId);

                using var context = _appSubscribeRepository.CreateContext();

                await _appSubscribeRepository.InsertAsync(new Def_App_Subscribe()
                {
                    AppId = appId
                });

                // 更新或创建统计记录
                if (stats == null)
                {
                    stats = new Def_App_Usage_Stats
                    {
                        AppId = appId,
                        Runs = 0,
                        Joins = 1
                    };
                    _ = await _userStatsRepository.InsertAsync(stats);
                }
                else
                {
                    _ = await _userStatsRepository.UpdateAsync(it => new Def_App_Usage_Stats
                    {
                        Joins = it.Joins + 1
                    },
                    it => it.AppId == appId);
                }

                context.Commit();
                return true;
            }
            catch (Exception exception)
            {
                UnisLog.Error(exception, "SubscribeAsync");
                return false;
            }
        }

        public async Task<PageModelDto<AppDetailPageResponse>> GetUserSubscribedAppsAsync(AppSearchPageDto searchDto)
        {
            Expression<Func<Def_App_Subscribe, Def_App, bool>> filterExpression = Expressionable.Create<Def_App_Subscribe, Def_App>()
                .And((s, a) => s.CreateBy == _userContext.UserId)
                .And((s, a) => s.IsActive == true)
                .And((s, a) => a.AppStatus == AppStatusEnum.Published || a.AppStatus == AppStatusEnum.AutoPublished)
                .AndIF(!string.IsNullOrWhiteSpace(searchDto.AppMode), (s, a) => a.AppMode.Equals(searchDto.AppMode))
                .AndIF(!string.IsNullOrEmpty(searchDto.SearchText), (s, a) =>
                a.AppName.Contains(searchDto.SearchText) ||
                a.AppDescription.Contains(searchDto.SearchText))
            .ToExpression();

            (List<AppListModel> apps, int total) = await _appSubscribeRepository.GetUserSubscribedAppsAsync(
               filterExpression,
               searchDto.PageIndex,
               searchDto.PageSize,
               searchDto.OrderBy,
               searchDto.IsAsc,
               searchDto.TagIds
           );

            List<AppDetailPageResponse> appDtos = _mapper.Map<List<AppDetailPageResponse>>(apps);

            // 批量获取所有统计数据
            var appIds = appDtos.Select(x => x.AppId).ToList();

            // 获取统计数据
            var allStats = await _userStatsRepository.GetListAsync(x => appIds.Contains(x.AppId));
            var statsDict = allStats.ToDictionary(x => x.AppId);

            // 更新应用数据
            foreach (var app in appDtos)
            {
                // 获取使用时长
                app.LastDaysSeconds = await _appUsageService.GetLast15DaysUsageAsync(app.MainAppId, app.CreateTime);
                app.TotalUsageSecond = (await _usageRepository.GetListAsync(u => u.AppId == app.AppId && u.IsActive)).Sum(u => u.CostSeconds);

                // 获取统计数据
                var stats = statsDict.GetValueOrDefault(app.AppId);
                app.Runs = stats?.Runs ?? 0;
                app.Joins = stats?.Joins ?? 0;

                // 获取评论统计
                var reviewStats = await _appReviewCommentService.GetReviewStatsAsync(app.AppId);
                app.Star = reviewStats?.AverageRating ?? 0;
            }

            return new PageModelDto<AppDetailPageResponse>(searchDto.PageIndex, searchDto.PageSize, appDtos, total);
        }

        public async Task<bool> CheckSubscribedAsync(string appId)
        {
            var mainAppId = await GetMainAppIdAsync(appId);
            var allVersions = await _appRepository.GetListAsync(a => a.MainAppId == mainAppId && a.IsActive);
            return
                await _appSubscribeRepository.IsAnyAsync(i =>
                    allVersions.Select(a => a.AppId).Contains(i.AppId) && i.CreateBy == _userContext.UserId && i.IsActive);
        }

        public async Task<bool> UnsubscribeAsync(string appId)
        {
            // 检查是否已订阅
            if (!await CheckSubscribedAsync(appId))
            {
                throw new AgentCentralException("Not subscribed");
            }

            try
            {
                var app = await _appRepository.GetFirstAsync(a => a.AppId == appId);
                using var context = _appSubscribeRepository.CreateContext();

                // 将订阅记录标记为无效
                await _appSubscribeRepository.UpdateSetColumnsTrueAsync(
                    set => new Def_App_Subscribe { IsActive = false },
                    where => where.AppId == appId && where.CreateBy == _userContext.UserId
                );

                // 解除用户配置的value与app的关系
                await _valueRepository.UpdateSetColumnsTrueAsync(
                    set => new Def_App_Param_Value { AppId = 0 },
                    where => where.AppId == app.Id && where.CreateBy == _userContext.UserId
                );

                // 更新应用统计信息
                await _userStatsRepository.UpdateSetColumnsTrueAsync(
                    set => new Def_App_Usage_Stats { Joins = set.Joins - 1 },
                    where => where.AppId == appId
                );

                context.Commit();
                return true;
            }
            catch (Exception exception)
            {
                UnisLog.Error(exception, "UnsubscribeAsync");
                return false;
            }
        }

        public async Task<bool> CopySubscriptionAsync(string appId, long userId)
        {
            try
            {
                var mainAppId = await GetMainAppIdAsync(appId);
                var allVersionApps = await _appRepository.GetListAsync(a => a.MainAppId == mainAppId && a.IsActive);
                // Get old subscription record
                var oldSubscription = await _appSubscribeRepository.GetFirstAsync(s =>
                    allVersionApps.Select(a => a.AppId).Contains(s.AppId) &&
                    s.CreateBy == userId &&
                    s.IsActive);

                if (oldSubscription == null)
                {
                    return false;
                }

                // Check if already subscribed to new version
                var existingSubscription = await _appSubscribeRepository.GetFirstAsync(s =>
                    s.AppId == appId &&
                    s.CreateBy == userId &&
                    s.IsActive);

                if (existingSubscription != null)
                {
                    return true;
                }

                using var context = _appSubscribeRepository.CreateContext();

                // Delete old subscription and create new one
                await _appSubscribeRepository.UpdateSetColumnsTrueAsync(set => new Def_App_Subscribe
                {
                    IsActive = false
                }, where => where.Id == oldSubscription.Id);

                var newSubscription = new Def_App_Subscribe
                {
                    AppId = appId,
                    CreateBy = userId,
                    CreateName = oldSubscription.CreateName,
                    TenantId = oldSubscription.TenantId
                };
                await _appSubscribeRepository.InsertAsync(newSubscription);

                // Update subscription statistics
                await _userStatsRepository.UpdateSetColumnsTrueAsync(set => new Def_App_Usage_Stats
                {
                    Joins = set.Joins - 1
                }, where => where.AppId == oldSubscription.AppId);

                var stats = await _userStatsRepository.GetFirstAsync(x => x.AppId == appId);
                if (stats == null)
                {
                    stats = new Def_App_Usage_Stats
                    {
                        AppId = appId,
                        Runs = 0,
                        Joins = 1
                    };
                    await _userStatsRepository.InsertAsync(stats);
                }
                else
                {
                    await _userStatsRepository.UpdateSetColumnsTrueAsync(set => new Def_App_Usage_Stats
                    {
                        Joins = set.Joins + 1
                    }, where => where.AppId == appId);
                }

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, "CopySubscriptionAsync");
                return false;
            }
        }

        public async Task<bool> CopyAllSubscriptionsAsync(string appId)
        {
            try
            {
                var mainAppId = await GetMainAppIdAsync(appId);
                var allVersionApps = await _appRepository.GetListAsync(a => a.MainAppId == mainAppId && a.IsActive);
                // Get all active subscriptions
                var oldSubscriptions = await _appSubscribeRepository.GetListAsync(s =>
                    allVersionApps.Select(a => a.AppId).Contains(s.AppId) &&
                    s.IsActive);

                if (oldSubscriptions.Count == 0)
                {
                    return true;
                }

                using var context = _appSubscribeRepository.CreateContext();

                // Soft delete old subscriptions and create new ones
                await _appSubscribeRepository.UpdateSetColumnsTrueAsync(set => new Def_App_Subscribe
                {
                    IsActive = false
                }, where => oldSubscriptions.Select(s => s.Id).Contains(where.Id));

                var newSubscriptions = oldSubscriptions.Select(s => new Def_App_Subscribe
                {
                    AppId = appId,
                    CreateBy = s.CreateBy,
                    CreateName = s.CreateName,
                    TenantId = s.TenantId
                }).ToList();

                await _appSubscribeRepository.InsertRangeAsync(newSubscriptions);

                // Update subscription statistics
                await _userStatsRepository.UpdateSetColumnsTrueAsync(set => new Def_App_Usage_Stats
                {
                    Joins = 0
                }, where => where.AppId == oldSubscriptions.Select(a => a.AppId).FirstOrDefault());

                var newStats = await _userStatsRepository.GetFirstAsync(x => x.AppId == appId);
                if (newStats == null)
                {
                    newStats = new Def_App_Usage_Stats
                    {
                        AppId = appId,
                        Runs = 0,
                        Joins = oldSubscriptions.Count
                    };
                    await _userStatsRepository.InsertAsync(newStats);
                }
                else
                {
                    await _userStatsRepository.UpdateSetColumnsTrueAsync(set => new Def_App_Usage_Stats
                    {
                        Joins = set.Joins + oldSubscriptions.Count
                    }, where => where.AppId == appId);
                }

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, "CopyAllSubscriptionsAsync");
                return false;
            }
        }

        /// <summary>
        /// 批量复制订阅
        /// </summary>
        /// <param name="dto">批量复制订阅DTO</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchCopySubscriptionAsync(BatchCopySubscriptionDto dto)
        {
            try
            {
                if (dto == null || dto.AppIds == null || dto.AppIds.Count == 0)
                {
                    return false;
                }
                using var context = _appSubscribeRepository.CreateContext();
                var successCount = 0;

                foreach (var appId in dto.AppIds)
                {
                    var result = await CopySubscriptionAsync(appId, _userContext.UserId);
                    if (result)
                    {
                        successCount++;
                    }
                }

                context.Commit();
                return successCount > 0;
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, "BatchUpdateSubscriptionAsync");
                return false;
            }
        }
    }
}