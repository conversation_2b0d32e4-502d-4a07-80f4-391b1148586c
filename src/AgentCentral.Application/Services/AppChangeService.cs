using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.Common.Lib.LogUtil;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// Application change record service implementation
    /// </summary>
    public class AppChangeService : IAppChangeService
    {
        private readonly IBaseRepository<Def_App_Change> _appChangeRepository;
        private readonly IAppRepository _appRepository;
        private readonly IMapper _mapper;

        public AppChangeService(
            IBaseRepository<Def_App_Change> appChangeRepository,
            IAppRepository appRepository,
            IMapper mapper)
        {
            _appChangeRepository = appChangeRepository;
            _appRepository = appRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// Get application change record
        /// </summary>
        public async Task<AppChangeDto> GetAsync(long appId)
        {
            var change = await _appChangeRepository.GetFirstAsync(x => x.AppId == appId && x.IsActive) ?? throw new AgentCentralException("Change record not found");
            var result = _mapper.Map<AppChangeDto>(change);
            if ((change.ChangeType & (int)AppChangeTypeEnum.Configuration) == (int)AppChangeTypeEnum.Configuration
                || (change.ChangeType & (int)AppChangeTypeEnum.Functional) == (int)AppChangeTypeEnum.Functional)
            {
                var app = await _appRepository.GetFirstAsync(a => a.Id == appId && a.IsActive) ?? throw new AgentCentralException("App not found");
                var lastVersion = await _appRepository.GetMaxAsync(a => a.MainAppId == app.MainAppId, a => a.VersionNumber);
                result.NewVersion = lastVersion + 1;
            }
            return result;
        }

        /// <summary>
        /// Update application change record
        /// </summary>
        public async Task<bool> UpdateAsync(long appId, UpdateAppChangeDto updateDto)
        {
            var change = await _appChangeRepository.GetFirstAsync(x => x.AppId == appId && x.IsActive) ?? throw new AgentCentralException("Change record not found");
            try
            {
                _mapper.Map(updateDto, change);
                return await _appChangeRepository.UpdateAsync(change);
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, $"Failed to update app change record with appid {appId}");
                throw new AgentCentralException("Update failed");
            }
        }

        /// <summary>
        /// Update application change type
        /// </summary>
        public async Task<bool> UpdateChangeTypeAsync(string appId, UpdateAppChangeTypeDto updateDto)
        {
            var app = await _appRepository.GetFirstAsync(a => a.AppId == appId && a.IsActive) ?? throw new AgentCentralException("App not found");
            var change = await _appChangeRepository.GetFirstAsync(x => x.AppId == app.Id & x.IsActive) ?? throw new AgentCentralException("Change record not found");
            try
            {
                if ((change.ChangeType & (int)AppChangeTypeEnum.Presentation) == (int)AppChangeTypeEnum.Presentation)
                {
                    change.ChangeType = (int)AppChangeTypeEnum.Presentation | updateDto.ChangeType;
                }
                else
                {
                    change.ChangeType = updateDto.ChangeType;
                }
                change.NeedUpgradeVersion = true;
                return await _appChangeRepository.UpdateAsync(change);
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, $"Failed to update app change type with appid {app.Id}");
                throw new AgentCentralException("Update failed");
            }
        }
    }
}
