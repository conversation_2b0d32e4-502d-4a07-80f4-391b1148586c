using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.MessageCenter;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Globalization;

namespace AgentCentral.Application.Services
{
    public class MessageCenterService : IMessageCenterService
    {
        private readonly MessageCenterClient _messageCenterClient;
        private readonly ILogger<MessageCenterService> _logger;
        private readonly IAppRepository _appRepository;
        private readonly IOptionsSnapshot<MessageCenterOptions> _options;

        public MessageCenterService(
            MessageCenterClient messageCenterClient,
            ILogger<MessageCenterService> logger,
            IAppRepository appRepository,
            IOptionsSnapshot<MessageCenterOptions> options)
        {
            _messageCenterClient = messageCenterClient;
            _logger = logger;
            _appRepository = appRepository;
            _options = options;
        }

        public async Task<bool> SendAppNotificationAsync(long appId)
        {
            try
            {
                var app = await _appRepository.GetByIdAsync(appId);
                if (app == null)
                {
                    _logger.LogWarning($"App not found, AppId: {appId}");
                    return false;
                }

                var request = new SendMessageRequest
                {
                    JobCode = _options.Value.ReviewEmailJobCode,
                    BodySpel = new List<SpelContent>
                    {
                        new SpelContent
                        {
                            Key = "{SubmittedName}",
                            Value = !string.IsNullOrEmpty(app.UpdateName) == true ? app.UpdateName : app.CreateName
                        },
                        new SpelContent
                        {
                            Key = "{AgentName}",
                            Value = app.AppName
                        },
                        new SpelContent
                        {
                            Key = "{SubmittedTime}",
                            Value = app.UpdateTime.AddHours(8).ToString("yyyy-MM-dd HH:mm:ss")
                        },
                        new SpelContent
                        {
                            Key = "{SystemUrl}",
                            Value = _options.Value.AgentCentralUrl
                        }
                    }
                };

                List<Recipients> recipients = new List<Recipients>();
                if (!string.IsNullOrEmpty(_options.Value.ReviewEmail))
                {
                    var reviewEmail = _options.Value.ReviewEmail.Split(',');
                    foreach (var item in reviewEmail)
                    {
                        recipients.Add(new Recipients
                        {
                            Recipient = item,
                            RecipientType = "EMAIL"
                        });
                    }
                }
                else
                {
                    recipients = new List<Recipients>
                    {
                        new Recipients
                        {
                            Recipient = "<EMAIL>",
                            RecipientType = "EMAIL"
                        }
                    };
                }
                request.Recipients = recipients;

                var result = await _messageCenterClient.SendMessageAsync(request);

                return result.Success;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, $"Failed to send App notification, AppId: {appId}");
                throw;
            }
        }
    }
}