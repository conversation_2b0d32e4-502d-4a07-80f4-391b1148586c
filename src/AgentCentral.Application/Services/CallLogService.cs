using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.CallLog;
using AgentCentral.Application.Contracts.IServices;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// Call log service implementation
    /// </summary>
    public class CallLogService : ICallLogService
    {
        private readonly CallLogClient _callLogClient;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="callLogClient">Call log client</param>
        public CallLogService(CallLogClient callLogClient)
        {
            _callLogClient = callLogClient;
        }

        /// <summary>
        /// Get call support statistics
        /// </summary>
        /// <returns>Call support statistics</returns>
        public async Task<CallSupportStatisticsDto> GetCallSupportStatisticsAsync()
        {
            return await _callLogClient.GetCallSupportStatisticsAsync();
        }

        /// <summary>
        /// Search call logs with pagination
        /// </summary>
        /// <param name="searchParams">Search parameters</param>
        /// <returns>Page response with call log records</returns>
        public async Task<CallLogPageDto<List<CallLogRecordDto>>> SearchCallLogsAsync(CallLogSearchParamsDto searchParams)
        {
            // Validate pagination parameters
            if (searchParams.PageSize <= 0)
            {
                throw new ArgumentException("Page size must be greater than 0", nameof(searchParams.PageSize));
            }

            if (searchParams.PageNo <= 0)
            {
                throw new ArgumentException("Page number must be greater than 0", nameof(searchParams.PageNo));
            }

            // Set default values
            searchParams.PageSize = Math.Min(searchParams.PageSize, 100); // Limit maximum page size to 100
            searchParams.PageNo = Math.Max(1, searchParams.PageNo); // Ensure page number is at least 1

            return await _callLogClient.SearchCallLogsAsync(searchParams);
        }
    }
}