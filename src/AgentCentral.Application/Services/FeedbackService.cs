using System.Linq.Expressions;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.Feedback;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.Common.Lib.LogUtil;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    /// <summary>
    /// 反馈服务实现
    /// </summary>
    public class FeedbackService : IFeedbackService
    {
        private readonly IFeedbackRepository _feedbackRepository;
        private readonly IMapper _mapper;
        private readonly IAttachmentMappingRepository _attachmentMappingRepository;
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IAttachmentService _attachmentService;
        private readonly IUserInfoRepository _userInfoRepository;

        public FeedbackService(
            IFeedbackRepository feedbackRepository,
            IMapper mapper,
            IAttachmentMappingRepository attachmentMappingRepository,
            IAttachmentRepository attachmentRepository,
            IAttachmentService attachmentService,
            IUserInfoRepository userInfoRepository)
        {
            _feedbackRepository = feedbackRepository;
            _mapper = mapper;
            _attachmentMappingRepository = attachmentMappingRepository;
            _attachmentRepository = attachmentRepository;
            _attachmentService = attachmentService;
            _userInfoRepository = userInfoRepository;
        }

        /// <summary>
        /// 更新反馈状态
        /// </summary>
        public async Task<bool> UpdateStatusAsync(long id, FeedbackStatusEnum status)
        {
            var entity = await _feedbackRepository.GetByIdAsync(id);
            if (entity == null)
            {
                throw new AgentCentralException("反馈不存在");
            }
            return await _feedbackRepository.UpdateSetColumnsTrueAsync(f => new Def_Feedback { Status = status }, f => f.Id == id);
        }

        /// <summary>
        /// 删除反馈
        /// </summary>
        public async Task<bool> DeleteAsync(long id)
        {
            var entity = await _feedbackRepository.GetByIdAsync(id);
            if (entity == null)
            {
                throw new AgentCentralException("反馈不存在");
            }
            return await _feedbackRepository.UpdateSetColumnsTrueAsync(f => new Def_Feedback { IsActive = false }, f => f.Id == id);
        }

        /// <summary>
        /// 获取反馈详情
        /// </summary>
        public async Task<FeedbackOutputDto> GetAsync(long id)
        {
            var entity = await _feedbackRepository.GetByIdAsync(id);
            if (entity == null)
            {
                throw new AgentCentralException("反馈不存在");
            }
            var userInfo = await _userInfoRepository.GetFirstAsync(u => u.UserId == entity.CreateBy);
            var result = _mapper.Map<FeedbackOutputDto>(entity);
            var mappings = await _attachmentMappingRepository.GetListAsync(m => m.BusinessId == id && m.BusinessType == AttachmentTypeEnum.Feedback);
            if (mappings.Count > 0)
            {
                var attachments = await _attachmentRepository.GetListAsync(a => mappings.Select(m => m.AttachmentId).Contains(a.Id));
                foreach (var attachment in attachments)
                {
                    result.Files.Add(new AttachmentOutputDto
                    {
                        Id = attachment.Id,
                        Name = attachment.RealName,
                        Url = await _attachmentService.GetAccessUrlAsync(attachment.Id, default)
                    });
                }
            }
            result.Company = userInfo?.CompanyName;
            result.Department = userInfo?.DepartmentName;
            result.Title = string.IsNullOrEmpty(result.Title) ? result.Comments : result.Title;
            result.CreateName = userInfo?.UserName;
            return result;
        }

        /// <summary>
        /// 分页查询反馈
        /// </summary>
        public async Task<PageModelDto<FeedbackOutputDto>> GetPageListAsync(FeedbackSearchDto input)
        {
            Expression<Func<Def_Feedback, Def_User_Info, bool>> filterExpression = Expressionable.Create<Def_Feedback, Def_User_Info>()
                .AndIF(!string.IsNullOrEmpty(input.FeedbackId), (f, u) => f.FeedbackId.Contains(input.FeedbackId))
                .AndIF(input.IssueType.HasValue, (f, u) => f.IssueType == input.IssueType)
                .AndIF(input.Status.HasValue, (f, u) => f.Status == input.Status)
                .AndIF(!string.IsNullOrEmpty(input.ModuleType),(f, u) => f.Module == input.ModuleType)
                .AndIF(!string.IsNullOrEmpty(input.Comments), (f, u) => f.Comments.Contains(input.Comments))
                .AndIF(input.StartTime.HasValue, (f, u) => f.CreateTime >= input.StartTime.Value)
                .AndIF(input.EndTime.HasValue, (f, u) => f.CreateTime <= input.EndTime.Value.AddDays(1).AddSeconds(-1))
                .AndIF(!string.IsNullOrEmpty(input.CompanyOrDepartment), (f, u) => u.CompanyName.Contains(input.CompanyOrDepartment) || u.DepartmentName.Contains(input.CompanyOrDepartment))
                .ToExpression();

            var (list, total) = await _feedbackRepository.GetFeedbackPageList(
                filterExpression,
                input.PageIndex,
                input.PageSize);

            var dtoList = _mapper.Map<List<FeedbackOutputDto>>(list);
            return new PageModelDto<FeedbackOutputDto>(input.PageIndex, input.PageSize, dtoList, total);
        }
    }
}