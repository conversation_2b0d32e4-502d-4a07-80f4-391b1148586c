using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.Attachment;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ResourceManagement;
using AgentCentral.Application.Contracts.RequestModels.ResourceManagement;
using AgentCentral.Application.Contracts.ResponseModels.ResourceManagement;
using AgentCentral.Application.Contracts.Services;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using AgentCentral.Application.Contracts.RequestModels.Resource;
using Item.Common.Lib.LogUtil;
using Item.BlobProvider;
using Microsoft.Extensions.Configuration;
using AgentCentral.Infrastructure.Exceptions;
using System.Linq.Expressions;
using AgentCentral.Domain.Shared.Models;
using SqlSugar;

namespace AgentCentral.Application.Services
{
    public class ResourceManagementService : IResourceManagementService
    {
        private readonly IResourcePeopleRepository _peopleRepository;
        private readonly IResourceProcessRepository _processRepository;
        private readonly IBaseRepository<Doc_Resource_Customer_Process> _customerProcessRepository;
        private readonly IBaseRepository<Doc_Attachment_Mapping> _attachmentMappingRepository;
        private readonly IBaseRepository<Doc_Attachment> _attachmentRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<ResourceManagementService> _logger;
        private readonly UserContext _userContext;
        private readonly ClockApiClient _clockApiClient;
        private const int DefaultBatchSize = 300;
        private readonly IBlobContainer _blobContainer;
        private readonly ResourceClient _resourceClient;
        private readonly IConfiguration _configuration;

        public ResourceManagementService(
            IResourcePeopleRepository peopleRepository,
            IResourceProcessRepository processRepository,
            IBaseRepository<Doc_Resource_Customer_Process> customerProcessRepository,
            IBaseRepository<Doc_Attachment_Mapping> attachmentMappingRepository,
            IBaseRepository<Doc_Attachment> attachmentRepository,
            IMapper mapper,
            ILogger<ResourceManagementService> logger,
            UserContext userContext, ClockApiClient clockApiClient,
            IBlobContainer blobContainer,
            ResourceClient resourceClient,
            IConfiguration configuration)
        {
            _peopleRepository = peopleRepository;
            _processRepository = processRepository;
            _customerProcessRepository = customerProcessRepository;
            _attachmentMappingRepository = attachmentMappingRepository;
            _attachmentRepository = attachmentRepository;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
            _clockApiClient = clockApiClient;
            _blobContainer = blobContainer;
            _resourceClient = resourceClient;
            _configuration = configuration;
        }

        public async Task<bool> CreateResourceAsync(CreateResourceRequest request)
        {
            try
            {
                using var context = _peopleRepository.CreateContext();

                var employee = string.IsNullOrEmpty(request.People.EmployeeId)
                    ? null
                    : await _clockApiClient.GetEmployeeByIdAsync(request.People.EmployeeId);
                if (employee == null || employee.Department.IsNullOrEmpty())
                {
                    request.People.EmployeeId = _userContext.UserId.ToString();
                }

                await CreateResourcePeopleAsync(request.People);
                await HandleProcessesAsync(request.Processes);

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create resource");
                return false;
            }
        }

        public async Task<bool> UpdateResourceAsync(UpdateResourceRequest request)
        {
            try
            {
                using var context = _peopleRepository.CreateContext();

                var people = await _peopleRepository.GetFirstAsync(p => p.EmployeeId == request.People.EmployeeId);
                if (people == null)
                {
                    throw new Exception($"Resource not found for employee {request.People.EmployeeId}");
                }

                await UpdateResourcePeopleAsync(people, request.People);
                await HandleProcessesAsync(request.Processes, true, request.People.EmployeeId);

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update resource");
                return false;
            }
        }

        public async Task<ResourceResponse> GetResourceAsync(string employeeId)
        {
            Expression<Func<Doc_Resource_People, bool>> peopleWhere = p => p.EmployeeId == (string.IsNullOrEmpty(employeeId) ? _userContext.UserId.ToString() : employeeId);
            var people = await _peopleRepository.GetFirstAsync(peopleWhere);
            if (people == null) return null;

            Expression<Func<Doc_Resource_Process, bool>> processWhere = string.IsNullOrEmpty(employeeId)
            ? p => p.CreateBy == _userContext.UserId
            : p => p.EmployeeId == employeeId;
            var processes = await _processRepository.GetListAsync(processWhere);
            var attachmentsDic = await GetAttachmentsDicAsync(processes);

            var peopleDto = _mapper.Map<ResourcePeopleDto>(people);

            var processListDto = _mapper.Map<List<ResourceProcessDto>>(processes);
            var peoples = await _peopleRepository.GetListAsync(p => processListDto.Select(l => l.EmployeeId).Contains(p.EmployeeId));
            foreach (var processDto in processListDto)
            {
                processDto.Attachments = attachmentsDic.TryGetValue(processDto.Id ?? 0, out var att2) ? att2 : null;
                processDto.ContributorName = peoples.FirstOrDefault(p => p.EmployeeId == processDto.EmployeeId)?.Name;
            }


            var response = new ResourceResponse
            {
                People = peopleDto,
                Processes = processListDto
            };

            return response;
        }

        #region Private Methods

        private async Task<long> CreateResourcePeopleAsync(ResourcePeopleDto peopleDto)
        {
            var peopleModel = await _peopleRepository.GetFirstAsync(p => p.EmployeeId == peopleDto.EmployeeId);
            if (peopleModel != null)
            {
                throw new AgentCentralException("The people already existed.");
            }
            var people = _mapper.Map<Doc_Resource_People>(peopleDto);
            people.InitNewId();
            await _peopleRepository.InsertAsync(people);

            return people.Id;
        }

        private async Task UpdateResourcePeopleAsync(Doc_Resource_People people, ResourcePeopleDto peopleDto)
        {
            _mapper.Map(peopleDto, people);
            await _peopleRepository.UpdateAsync(people);
        }

        private async Task HandleProcessesAsync(List<ResourceProcessDto> processes, bool isUpdate = false, string employeeId = "")
        {
            if (isUpdate)
            {
                Expression<Func<Doc_Resource_Process, bool>> where = p => p.EmployeeId == employeeId && p.IsActive;
                // 前端列表数据查询条件不固定，可能是某个Employee的所有数据，也可能CreateBy为当前用户的所有数据
                if (employeeId == _userContext.UserId.ToString())
                {
                    where = p => p.CreateBy == _userContext.UserId && p.IsActive;
                }
                var existingIds = (await _processRepository.GetListAsync(where)).Select(p => p.Id).ToList();
                var requestIds = processes!.Where(p => p.Id > 0).Select(p => (long)p.Id).ToList();
                await DeleteUnusedProcesses(existingIds, requestIds);
            }

            foreach (var processDto in processes!)
            {
                var process = _mapper.Map<Doc_Resource_Process>(processDto);

                if (string.IsNullOrEmpty(process.EmployeeId))
                {
                    process.EmployeeId = _userContext.UserId.ToString();
                }

                if (isUpdate && process.Id > 0)
                {
                    await _processRepository.UpdateAsync(process);
                    await _attachmentMappingRepository.DeleteAsync(m => m.BusinessId == process.Id && m.BusinessType == AttachmentTypeEnum.DocResourceProcess);
                }
                else
                {
                    process.InitNewId();
                    await _processRepository.InsertAsync(process);
                }

                await CreateAttachmentMappingsAsync(process.Id, processDto.AttachmentIds,
                    AttachmentTypeEnum.DocResourceProcess);
            }
        }

        private async Task DeleteUnusedProcesses(List<long> existingIds, List<long> requestIds)
        {
            var idsToDelete = existingIds.Except(requestIds).ToList();
            if (idsToDelete.Count == 0) return;

            await _processRepository.UpdateSetColumnsTrueAsync(set => new Doc_Resource_Process { IsActive = false },
                p => idsToDelete.Contains(p.Id));
            await _attachmentMappingRepository.DeleteAsync(m => idsToDelete.Contains(m.BusinessId) && m.BusinessType == AttachmentTypeEnum.DocResourceProcess);
        }

        private async Task DeleteUnusedCustomerProcesses(List<long> existingIds, List<long> requestIds)
        {
            var idsToDelete = existingIds.Except(requestIds).ToList();
            if (!idsToDelete.Any()) return;

            await _customerProcessRepository.UpdateSetColumnsTrueAsync(
                set => new Doc_Resource_Customer_Process { IsActive = false }, p => idsToDelete.Contains(p.Id));
            await _attachmentMappingRepository.DeleteAsync(m => idsToDelete.Contains(m.BusinessId) && m.BusinessType == AttachmentTypeEnum.DocResourceCustomerProcess);
        }

        private async Task CreateAttachmentMappingsAsync(long businessId, List<long> attachmentIds,
            AttachmentTypeEnum businessType)
        {
            if (attachmentIds?.Count == 0) return;

            foreach (var attachmentId in attachmentIds)
            {
                await _attachmentMappingRepository.InsertAsync(new Doc_Attachment_Mapping
                {
                    BusinessId = businessId,
                    AttachmentId = attachmentId,
                    BusinessType = businessType
                });
            }
        }

        private async Task<Dictionary<long, List<AttachmentDto>>> GetAttachmentsDicAsync(List<Doc_Resource_Process> processes)
        {
            var businessIds = processes.Select(p => p.Id);

            var attachmentMappings =
                await _attachmentMappingRepository.GetListAsync(m => businessIds.Contains(m.BusinessId));
            var attachmentIds = attachmentMappings.Select(m => m.AttachmentId).Distinct().ToList();

            if (!attachmentIds.Any()) return [];

            var attachments = await _attachmentRepository.GetListAsync(a => attachmentIds.Contains(a.Id));

            return attachmentMappings.GroupBy(g => g.BusinessId).ToDictionary(d => d.Key,
                d => _mapper.Map<List<AttachmentDto>>(attachments
                    .Where(w => d.Select(s => s.AttachmentId).Contains(w.Id)).ToList()));
        }

        #endregion

        /// <summary>
        /// 获取资源流程列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageModelDto<ResourceProcessListDto>> GetResourceProcessListAsync(ResourceProcessSearchDto searchDto)
        {
            try
            {
                Expression<Func<Doc_Resource_Process, Doc_Resource_People, bool>> whereExpression = Expressionable.Create<Doc_Resource_Process, Doc_Resource_People>()
                    .AndIF(!string.IsNullOrEmpty(searchDto.Process), (r, p) => r.Process.Contains(searchDto.Process))
                    .AndIF(!string.IsNullOrEmpty(searchDto.ProcessEnvironment), (r, p) => r.ProcessEnvironment.Contains(searchDto.ProcessEnvironment))
                    .AndIF(!string.IsNullOrEmpty(searchDto.Customer), (r, p) => r.Customer.Contains(searchDto.Customer))
                    .AndIF(!string.IsNullOrEmpty(searchDto.ProcessName), (r, p) => r.ProcessName.Contains(searchDto.ProcessName))
                    .AndIF(!string.IsNullOrEmpty(searchDto.Retailer), (r, p) => r.Retailer.Contains(searchDto.Retailer))
                    .AndIF(searchDto.UploadTimeStart.HasValue && searchDto.UploadTimeEnd.HasValue, (r, p) => r.CreateTime >= searchDto.UploadTimeStart.Value && r.CreateTime <= searchDto.UploadTimeEnd.Value)
                    .AndIF(!string.IsNullOrEmpty(searchDto.UserName), (r, p) => r.CreateName.Contains(searchDto.UserName))
                    .AndIF(!string.IsNullOrEmpty(searchDto.ContributorName), (r, p) => p.Name.Contains(searchDto.ContributorName))
                    .ToExpression();

                // 使用仓储方法获取分页数据

                var (pageResult, total) = await _processRepository.GetResourcePageListAsync(
                    whereExpression,
                    searchDto.PageSize,
                    searchDto.PageIndex,
                    searchDto.OrderBy,
                    searchDto.IsAsc
                );

                if (pageResult.Count == 0)
                {
                    return new PageModelDto<ResourceProcessListDto>(searchDto.PageIndex, searchDto.PageSize, new List<ResourceProcessListDto>(), total);
                }

                var processIds = pageResult.Select(p => p.Id).ToList();
                var attachmentMappings = await _attachmentMappingRepository.GetListAsync(
                    m => processIds.Contains(m.BusinessId) && m.BusinessType == AttachmentTypeEnum.DocResourceProcess);

                // 如果有附件映射，获取附件详情
                List<Doc_Attachment> allAttachments = new List<Doc_Attachment>();
                if (attachmentMappings.Any())
                {
                    var attachmentIds = attachmentMappings.Select(m => m.AttachmentId).Distinct().ToList();
                    allAttachments = await _attachmentRepository.GetListAsync(a => attachmentIds.Contains(a.Id) && a.IsActive);
                }

                var result = _mapper.Map<List<ResourceProcessListDto>>(pageResult);

                foreach (var process in result)
                {
                    // 获取当前流程的附件
                    var processAttachmentMappings = attachmentMappings.Where(m => m.BusinessId == process.Id).ToList();

                    if (processAttachmentMappings.Count != 0)
                    {
                        var processAttachmentIds = processAttachmentMappings.Select(m => m.AttachmentId).ToList();
                        var processAttachments = allAttachments.Where(a => processAttachmentIds.Contains(a.Id)).ToList();
                        var attachmentDtos = _mapper.Map<List<AttachmentDto>>(processAttachments);

                        process.Attachments = attachmentDtos;
                    }
                }

                return new PageModelDto<ResourceProcessListDto>(searchDto.PageIndex, searchDto.PageSize, result, total);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资源流程列表失败");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "获取资源流程列表失败");
            }
        }

        public async Task SyncResourcesAsync(CancellationToken stoppingToken)
        {
            var batchSize = _configuration.GetValue("ResourceSync:BatchSize", DefaultBatchSize);

            // 获取所有资源
            var processesAttachments = await _processRepository.GetProcessAttachments(stoppingToken);

            var requests = new List<ResourceAnalysisRequest>();

            var processAttachmentsGroups = processesAttachments.GroupBy(p => p.ProcessEnvironment).ToList();
            foreach (var attachmentGroup in processAttachmentsGroups)
            {
                var groupTitle = attachmentGroup.Key;

                var attachmentGroups = attachmentGroup.Chunk(batchSize).ToList();
                for (int i = 0; i < attachmentGroups.Count; i++)
                {
                    var title = attachmentGroups.Count == 1
                        ? groupTitle
                        : $"{groupTitle}-{i + 1}";

                    var urlList = new List<ResourceWeb>();
                    foreach (var attachment in attachmentGroups[i])
                    {
                        var url = await _blobContainer.GetAccessUrl(attachment.FileName);
                        if (!string.IsNullOrEmpty(url))
                        {
                            urlList.Add(new ResourceWeb { Url = url, Filename = attachment.EmployeeName + "-" + attachment.RealName });
                        }
                    }

                    requests.Add(new ResourceAnalysisRequest
                    {
                        Title = title,
                        Article = new List<string>(),
                        Web = urlList,
                        Youtube = new List<string>()
                    });
                }
            }

            if (requests is { Count: > 0 })
            {
                try
                {
                    await _resourceClient.AnalyzeResourcesAsync(requests.ToList());
                    UnisLog.Information($"Successfully analyzed {requests.Count} resources");
                }
                catch (Exception ex)
                {
                    UnisLog.Error(ex, "Failed to analyze resources for departments");
                    throw;
                }
            }
        }

        /// <summary>
        /// 删除资源流程
        /// </summary>
        /// <param name="id">流程ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteResourceProcessAsync(long id)
        {
            try
            {
                // 检查流程是否存在
                var process = await _processRepository.GetByIdAsync(id);
                if (process == null || !process.IsActive)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "资源流程不存在");
                }

                // 软删除流程（将IsActive设为false）
                await _processRepository.UpdateSetColumnsTrueAsync(set => new Doc_Resource_Process { IsActive = false },
                    p => p.Id == id);

                // 删除相关的附件映射
                await _attachmentMappingRepository.DeleteAsync(m => m.BusinessId == id && m.BusinessType == AttachmentTypeEnum.DocResourceProcess);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除资源流程失败");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "删除资源流程失败");
            }
        }

        /// <summary>
        /// 批量删除资源流程
        /// </summary>
        /// <param name="ids">流程ID列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchDeleteResourceProcessAsync(List<long> ids)
        {
            try
            {
                if (ids == null || !ids.Any())
                {
                    throw new AgentCentralException(ErrorCodeEnum.ParamIsNullError, "请选择要删除的流程");
                }

                // 软删除流程（将IsActive设为false）
                await _processRepository.UpdateSetColumnsTrueAsync(set => new Doc_Resource_Process { IsActive = false },
                    p => ids.Contains(p.Id));

                // 删除相关的附件映射
                //                await _attachmentMappingRepository.DeleteAsync(m => ids.Contains(m.BusinessId) && m.BusinessType == AttachmentTypeEnum.DocResourceProcess);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除资源流程失败");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "批量删除资源流程失败");
            }
        }

        /// <summary>
        /// 获取资源流程的文件信息
        /// </summary>
        /// <param name="processId">流程ID</param>
        /// <returns>文件列表</returns>
        public async Task<List<AttachmentDto>> GetResourceProcessFilesAsync(long processId)
        {
            try
            {
                // 检查流程是否存在
                var process = await _processRepository.GetByIdAsync(processId);
                if (process == null || !process.IsActive)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "资源流程不存在");
                }

                // 获取附件映射关系
                var attachmentMappings = await _attachmentMappingRepository.GetListAsync(
                    m => m.BusinessId == processId && m.BusinessType == AttachmentTypeEnum.DocResourceProcess);

                if (!attachmentMappings.Any())
                {
                    return new List<AttachmentDto>();
                }

                // 获取附件ID列表
                var attachmentIds = attachmentMappings.Select(m => m.AttachmentId).ToList();

                // 获取附件详细信息
                var attachments = await _attachmentRepository.GetListAsync(a => attachmentIds.Contains(a.Id) && a.IsActive);

                // 转换为DTO
                var result = _mapper.Map<List<AttachmentDto>>(attachments);

                // 为附件添加访问URL
                foreach (var attachment in result)
                {
                    var fileInfo = attachments.FirstOrDefault(a => a.Id == attachment.Id);
                    if (fileInfo != null && !string.IsNullOrEmpty(fileInfo.FileName))
                    {
                        attachment.AccessUrl = await _blobContainer.GetAccessUrl(fileInfo.FileName);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资源流程文件信息失败");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "获取资源流程文件信息失败");
            }
        }

        /// <summary>
        /// 删除资源流程文件
        /// </summary>
        /// <param name="processId">流程ID</param>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteResourceProcessFileAsync(long processId, long attachmentId)
        {
            try
            {
                // 检查流程是否存在
                var process = await _processRepository.GetByIdAsync(processId);
                if (process == null || !process.IsActive)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "资源流程不存在");
                }

                // 检查附件是否存在
                var attachment = await _attachmentRepository.GetByIdAsync(attachmentId);
                if (attachment == null || !attachment.IsActive)
                {
                    throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "文件不存在");
                }

                // 检查附件是否属于该流程
                var mapping = await _attachmentMappingRepository.GetFirstAsync(
                    m => m.BusinessId == processId &&
                         m.AttachmentId == attachmentId &&
                         m.BusinessType == AttachmentTypeEnum.DocResourceProcess);

                if (mapping == null)
                {
                    throw new AgentCentralException(ErrorCodeEnum.BusinessError, "该文件不属于此资源流程");
                }

                // 删除附件映射关系
                await _attachmentMappingRepository.DeleteAsync(m =>
                    m.BusinessId == processId &&
                    m.AttachmentId == attachmentId &&
                    m.BusinessType == AttachmentTypeEnum.DocResourceProcess);

                // 软删除附件记录（如果该附件仅被此流程引用）
                var otherMappings = await _attachmentMappingRepository.CountAsync(
                    m => m.AttachmentId == attachmentId && m.BusinessId != processId);

                if (otherMappings == 0)
                {
                    await _attachmentRepository.UpdateSetColumnsTrueAsync(
                        set => new Doc_Attachment { IsActive = false },
                        a => a.Id == attachmentId);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除资源流程文件失败");
                throw new AgentCentralException(ErrorCodeEnum.BusinessError, "删除资源流程文件失败");
            }
        }
    }
}