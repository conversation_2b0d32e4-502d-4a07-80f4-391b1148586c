using Newtonsoft.Json;
using AgentCentral.Application.Contracts;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Infrastructure.Redis;
using AgentCentral.Infrastructure.Redis.Enum;

namespace AgentCentral.Application.Service
{
    public class TokenValidatedInfoService : ITokenValidatedInfoService
    {
        private readonly IRedisService _redisService;

        public TokenValidatedInfoService(IRedisService redisService)
        {
            _redisService = redisService;
        }

        public async Task<bool> AddTokenInfoAsync(TokenValidatedInfoOutputDto info)
        {
            string cacheValue = JsonConvert.SerializeObject(info);
            TimeSpan timeSpan = TimeSpan.FromMinutes(50);
            return await _redisService.StringSetAsync(RedisDataType.Token, info.Token, cacheValue, timeSpan);
        }

        public async Task<bool> InvalidTokenInfoAsync(string token)
        {
            return await _redisService.KeyDelAsync(RedisDataType.Token, token);
        }

        public async Task<TokenValidatedInfoOutputDto> GetTokenInfoAsync(string token)
        {
            string info = await _redisService.StringGetAsync(RedisDataType.Token, token);
            return info != null ? JsonConvert.DeserializeObject<TokenValidatedInfoOutputDto>(info) : null;
        }

        public async Task<bool> IsExistTokenInfoAsync(string token)
        {
            return await _redisService.KeyExistsAsync(RedisDataType.Token, token);
        }

        public async Task<bool> InvalidTokenInfosByUserIdAsync(string userId)
        {
            //var pattern = $"{RedisCacheConsts.UserValidatedInfoKeyPrefix}:{userId}:*";
            string pattern = $"{userId}:*";
            string[] keys = _redisService.Keys(_redisService.AddKeyPrefix(RedisDataType.Token, pattern));
            IEnumerable<Task<bool>> tasks = keys.Select(key => _redisService.KeyDelAsync(RedisDataType.Token, key));
            _ = await Task.WhenAll(tasks);
            return await Task.FromResult(true);
        }

        public async Task<bool> UpdateExpireTimeAsync(string version, string userId)
        {
            //var cacheKey = $"{RedisCacheConsts.UserValidatedInfoKeyPrefix}:{userId}:{version}";
            string cacheKey = $"{userId}:{version}";
            int expireSecond = 60 * 60;
            return await _redisService.KeyExpireAsync(RedisDataType.Token, cacheKey, expireSecond);
        }

        public async Task<bool> UpdateTokenInfoAsync(string version, string userId, TokenValidatedInfoOutputDto tokenInfo)
        {
            //var cacheKey = $"{RedisCacheConsts.UserValidatedInfoKeyPrefix}:{userId}:{version}";
            string cacheKey = $"{userId}:{version}";
            return _redisService.KeyExists(RedisDataType.Token, cacheKey)
                ? await _redisService.StringSetAsync(RedisDataType.Token, cacheKey, JsonConvert.SerializeObject(tokenInfo))
                : await Task.FromResult(true);
        }
    }
}