using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.DocResource;
using AgentCentral.Application.Contracts.ResponseModels.DocResource;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using Item.BlobProvider;
using Item.Common.Lib.LogUtil;

namespace AgentCentral.Application.Services
{
    public class ResourceService : IResourceService
    {
        private readonly IBaseRepository<Doc_Resource> _repository;
        private readonly IMapper _mapper;
        private readonly IAttachmentMappingRepository _attachmentMappingRepository;
        private readonly IBlobContainer _blobContainer;
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly UserContext _userContext;

        public ResourceService(
            IBaseRepository<Doc_Resource> repository,
            IMapper mapper,
            IAttachmentMappingRepository attachmentMappingRepository,
            IAttachmentRepository attachmentRepository,
            IBlobContainer blobContainer, UserContext userContext)
        {
            _repository = repository;
            _mapper = mapper;
            _attachmentMappingRepository = attachmentMappingRepository;
            _attachmentRepository = attachmentRepository;
            _blobContainer = blobContainer;
            _userContext = userContext;
        }

        public async Task<bool> CreateAsync(CreateDocResourceRequest request)
        {
            try
            {
                using var context = _repository.CreateContext();

                var resource = _mapper.Map<Doc_Resource>(request);

                await _repository.InsertAsync(resource);

                // 添加附件映射
                if (request.AttachmentIds != null && request.AttachmentIds.Count > 0)
                {
                    foreach (var attachmentId in request.AttachmentIds)
                    {
                        await _attachmentMappingRepository.InsertAsync(new Doc_Attachment_Mapping
                        {
                            AttachmentId = attachmentId,
                            BusinessType = AttachmentTypeEnum.DocResource,
                            BusinessId = resource.Id
                        });
                    }
                }

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, "Failed to create resource");
                return false;
            }
        }

        public async Task<DocResourceResponse> GetAsync(long id)
        {
            var resource = await _repository.GetByIdAsync(id);
            if (resource == null)
            {
                throw new AgentCentralException("Resource not found");
            }

            var response = _mapper.Map<DocResourceResponse>(resource);

            // 获取附件信息
            var attachmentMappings = await _attachmentMappingRepository.GetListAsync(x =>
                x.BusinessId == id &&
                x.BusinessType == AttachmentTypeEnum.DocResource);

            if (attachmentMappings != null && attachmentMappings.Any())
            {
                var attachments = await _attachmentRepository.GetListAsync(g =>
                    attachmentMappings.Select(x => x.AttachmentId).Contains(g.Id));
                response.Attachments = new List<AttachmentInfo>();
                foreach (var attachment in attachmentMappings
                             .Select(mapping => attachments.FirstOrDefault(x => x.Id == mapping.AttachmentId))
                             .Where(attachment => attachment != null))
                {
                    response.Attachments.Add(new AttachmentInfo
                    {
                        Id = attachment.Id,
                        RealName = attachment.RealName,
                        FileType = attachment.FileType,
                        FileSize = attachment.FileSize,
                        CreateTime = attachment.CreateTime
                    });
                }
            }

            return response;
        }

        public async Task<bool> UpdateAsync(long resourceId, UpdateDocResourceRequest request)
        {
            try
            {
                using var context = _repository.CreateContext();

                // 检查资源是否存在
                var resource = await _repository.GetByIdAsync(resourceId);
                if (resource == null)
                {
                    throw new AgentCentralException("Resource not found");
                }

                // 更新基本信息
                resource = _mapper.Map<Doc_Resource>(request);
                await _repository.UpdateAsync(resource);

                // 更新附件映射
                // 1. 删除旧的映射
                await _attachmentMappingRepository.DeleteAsync(x =>
                    x.BusinessId == resourceId &&
                    x.BusinessType == AttachmentTypeEnum.DocResource);

                // 2. 添加新的映射
                if (request.AttachmentIds is { Count: > 0 })
                {
                    foreach (var attachmentId in request.AttachmentIds)
                    {
                        await _attachmentMappingRepository.InsertAsync(new Doc_Attachment_Mapping
                        {
                            AttachmentId = attachmentId,
                            BusinessType = AttachmentTypeEnum.DocResource,
                            BusinessId = resourceId
                        });
                    }
                }

                context.Commit();
                return true;
            }
            catch (Exception ex)
            {
                UnisLog.Error(ex, "Failed to update resource");
                return false;
            }
        }

        public async Task<List<DocResourceResponse>> GetList()
        {
            var list = await _repository.GetListAsync(g => g.CreateBy == _userContext.UserId);
            return _mapper.Map<List<DocResourceResponse>>(list);
        }
    }
}