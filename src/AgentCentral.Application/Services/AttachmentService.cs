using System.Security.Cryptography;
using System.Text;
using System.Xml;
using System.IO.Compression;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Contracts.ResponseModels.Attachment;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure.Exceptions;
using AutoMapper;
using ConvertApiDotNet.Model;
using ConvertApiDotNet;
using Item.BlobProvider;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Domain.Shared.Enums;
using Microsoft.IdentityModel.Tokens;

namespace AgentCentral.Application.Services;

public class AttachmentService : IAttachmentService
{
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly IAttachmentMappingRepository _attachmentMappingRepository;
    private readonly IBlobContainer _blobContainer;
    private readonly IOptions<GlobalConfigOptions> _globalOptions;
    private readonly IMapper _mapper;
    private readonly UserContext _userContext;

    private readonly IOptionsSnapshot<FileConversionOptions> _fileConversionOptions;

    public AttachmentService(
        IAttachmentRepository attachmentRepository,
        IAttachmentMappingRepository attachmentMappingRepository,
        IBlobContainer blobContainer,
        IOptions<GlobalConfigOptions> globalOptions,
        IMapper mapper,
        UserContext userContext,
        IOptionsSnapshot<FileConversionOptions> fileConversionOptions)
    {
        _attachmentRepository = attachmentRepository;
        _attachmentMappingRepository = attachmentMappingRepository;
        _blobContainer = blobContainer;
        _globalOptions = globalOptions;
        _mapper = mapper;
        _userContext = userContext;
        _fileConversionOptions = fileConversionOptions;
    }

    public async Task<AttachmentResponse> UploadAsync(IFormFile formFile)
    {
        if (formFile == null)
        {
            throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, "File is empty!");
        }

        var fileExt = Path.GetExtension(formFile.FileName);
        fileExt = fileExt[1..];
        if (!_fileConversionOptions.Value.UploadFileExt.Contains(fileExt.ToLower()))
        {
            throw new AgentCentralException(ErrorCodeEnum.UnsupportedfileUploadTypes, "Unsupported file upload types");
        }

        var fileName = HashFileName();
        var filePath = GetDirPath();
        var fileSize = formFile.Length;
        var realName = string.Empty;
        var contentType = string.Empty;
        bool isConvert = false;

        //todo ： 不在临时处理转换需求
        //if (_fileConversionOptions.Value.ConvertFileExt.Contains(fileExt))
        //{
        //    ConvertApi convertApi = new(_fileConversionOptions.Value.Secret);
        //    var dstExt = _fileConversionOptions.Value.ConvertSettints.FirstOrDefault(s => s.SrcFileExt == fileExt)
        //        .DstFileExt;
        //    using var stream = new MemoryStream();
        //    await formFile.CopyToAsync(stream);
        //    stream.Seek(0, SeekOrigin.Begin);
        //    ConvertApiResponse result =
        //        await convertApi.ConvertAsync(fileExt, dstExt, new ConvertApiFileParam(stream, formFile.FileName));
        //    using var convertStream = await result.Files.First().FileStreamAsync();
        //    convertStream.Seek(0, SeekOrigin.Begin);
        //    await _blobContainer.SaveAsync(fileName, convertStream, overrideExisting: true);
        //    realName = $"{Path.GetFileNameWithoutExtension(formFile.FileName)}.{dstExt}";
        //    contentType = _fileConversionOptions.Value.ConvertSettints.FirstOrDefault(s => s.SrcFileExt == fileExt)
        //        .DstContentType;
        //    isConvert = true;
        //}
        //else
        {
            using var stream = new MemoryStream();
            await formFile.CopyToAsync(stream);
            stream.Seek(0, SeekOrigin.Begin);
            //todo ： 不在临时处理转换需求
            //// 处理XML文件
            //if (fileExt.ToLower() == "xml")
            //{
            //    // 读取流内容为字符串
            //    using var reader = new StreamReader(stream);
            //    string content = await reader.ReadToEndAsync();

            //    // 读取XML内容
            //    var xmlDoc = new XmlDocument();
            //    xmlDoc.LoadXml(content);

            //    // 创建txt文件内容
            //    var txtContent = xmlDoc.InnerText;
            //    var txtFileName = $"{fileName}";

            //    // 保存txt文件
            //    using var txtStream = new MemoryStream(Encoding.UTF8.GetBytes(txtContent));
            //    await _blobContainer.SaveAsync(txtFileName, txtStream, overrideExisting: true);
            //}
            //else
            {
                await _blobContainer.SaveAsync(fileName + $".{fileExt}", stream, overrideExisting: true);
            }

            //todo ： 不在临时处理转换需求
            //if (_fileConversionOptions.Value.ChangeFileExt.Contains(fileExt))
            //{
            //    var dstExt = _fileConversionOptions.Value.ChangeExtSettings.FirstOrDefault(s => s.SrcFileExt == fileExt)
            //        .DstFileExt;
            //    realName = $"{Path.GetFileNameWithoutExtension(formFile.FileName)}.{dstExt}";
            //    contentType = _fileConversionOptions.Value.ChangeExtSettings
            //        .FirstOrDefault(s => s.SrcFileExt == fileExt).DstContentType;
            //}
            //else
            {
                realName = formFile.FileName;
                contentType = formFile.ContentType;
            }
        }

        string md5String;
        using (var md5 = MD5.Create())
        {
            await using (var stream = formFile.OpenReadStream())
            {
                var hash = await md5.ComputeHashAsync(stream);
                md5String = BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            }
        }

        var attachment = new Doc_Attachment
        {
            RealName = realName,
            FileType = contentType,
            FileName = fileName + $".{fileExt}",
            FileAbsolutePath = string.Empty,
            FolderPath = filePath.Replace("\\", "/"),
            FileSize = fileSize,
            FileExt = $".{fileExt}",
            AccessUrl = string.Concat(filePath.Replace("\\", "/"), "/", fileName + $".{fileExt}"),
            StoreType = _globalOptions.Value.BlobStoreType,
            Md5 = md5String,
            IsConvert = isConvert
        };
        await _attachmentRepository.InsertAsync(attachment);

        return _mapper.Map<Doc_Attachment, AttachmentResponse>(attachment);
    }

    public async Task<(Stream, string)> GetAttachmentStreamAsync(long attachmentId)
    {
        var attachment = await _attachmentRepository.GetByIdAsync(attachmentId) ??
                         throw new AgentCentralException(ErrorCodeEnum.DataIsNullError,
                             $"Attachment not found,id:{attachmentId}.");


        if (attachment.StoreType != _globalOptions.Value.BlobStoreType)
        {
            throw new AgentCentralException(ErrorCodeEnum.ParamInvalid,
                "The current storage type does not support.");
        }

        return (await _blobContainer.GetAsync(attachment.FileName, _userContext.TenantId), attachment.RealName);
    }

    public async Task<string> GetAccessUrlAsync(long id, CancellationToken cancellationToken)
    {
        var attachment = await _attachmentRepository.GetByIdAsync(id, cancellationToken);
        return attachment != null ? await _blobContainer.GetAccessUrl(attachment.FileName) : string.Empty;
    }

    public async Task<string> GetAccessUrlByAsync(string attachmentIdStr, CancellationToken cancellationToken = default)
    {
        if (long.TryParse(attachmentIdStr, out var id) && id > 0)
        {
            return await GetAccessUrlAsync(id, cancellationToken);
        }
        return attachmentIdStr;
    }

    public async Task<string> GetAccessUrlAsync(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }
        return await _blobContainer.GetAccessUrl(fileName);
    }

    public async Task<AttachmentResponse> GetAttachmentByIdAsync(long id)
    {
        var docAttachment = await _attachmentRepository.GetByIdAsync(id);
        return _mapper.Map<Doc_Attachment, AttachmentResponse>(docAttachment);
    }

    public async Task<List<AttachmentResponse>> GetAttachmentsAsync(long businessId, AttachmentTypeEnum attachmentType)
    {
        if (businessId <= 0)
        {
            return [];
        }

        var mappings = await _attachmentMappingRepository.GetListAsync(x =>
            x.BusinessId == businessId &&
            x.BusinessType == attachmentType);

        if (!mappings.Any())
        {
            return [];
        }

        var attachmentIds = mappings.Select(x => x.AttachmentId).ToList();
        var docAttachments = await _attachmentRepository.GetListAsync(x => attachmentIds.Contains(x.Id));
        return docAttachments.Select(doc => _mapper.Map<Doc_Attachment, AttachmentResponse>(doc)).ToList();
    }

    public async Task<(Stream, string)> GetBatchAttachmentsStreamAsync(long businessId, AttachmentTypeEnum attachmentType)
    {
        if (businessId <= 0)
        {
            throw new AgentCentralException(ErrorCodeEnum.ParamInvalid, "BusinessId must be greater than 0");
        }

        var mappings = await _attachmentMappingRepository.GetListAsync(x =>
            x.BusinessId == businessId &&
            x.BusinessType == attachmentType);

        if (!mappings.Any())
        {
            throw new AgentCentralException(ErrorCodeEnum.DataIsNullError, $"No attachments found for business {businessId}");
        }

        var attachmentIds = mappings.Select(x => x.AttachmentId).ToList();
        var attachments = await _attachmentRepository.GetListAsync(x => attachmentIds.Contains(x.Id));

        var memoryStream = new MemoryStream();
        using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
        {
            foreach (var attachment in attachments)
            {
                using var fileStream = await _blobContainer.GetAsync(attachment.FileName, _userContext.TenantId);
                var zipEntry = archive.CreateEntry(attachment.RealName);
                using var entryStream = zipEntry.Open();
                await fileStream.CopyToAsync(entryStream);
            }
        }

        memoryStream.Seek(0, SeekOrigin.Begin);
        var baseFileName = HashFileName();
        return (memoryStream, $"{baseFileName}.zip");
    }

    private static string GetDirPath(string storePath = "Attachments")
    {
        var date = DateTime.Now;
        var timeDir = date.ToString("yyyy/MMdd");

        if (!string.IsNullOrEmpty(storePath))
        {
            timeDir = Path.Combine(storePath, timeDir);
        }

        return timeDir;
    }

    private static string HashFileName(string str = null)
    {
        if (string.IsNullOrEmpty(str))
        {
            str = Guid.NewGuid().ToString().ToLower();
        }

        return BitConverter.ToString(MD5.HashData(Encoding.Default.GetBytes(str)), 4, 8).Replace("-", "");
    }
}