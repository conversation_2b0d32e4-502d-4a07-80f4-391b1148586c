using AgentCentral.Application.Contracts.RequestModels.Tag;
using AgentCentral.Application.Contracts.ResponseModels.Tag;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Enums;
using AutoMapper;

namespace AgentCentral.Application.Maps
{
    public class TagMapProfile : Profile
    {
        public TagMapProfile()
        {
            CreateMap<Def_Tag, TagResponse>().BeforeMap((src, dest) =>
            {
                dest.TagId = src.Id;
            });

            CreateMap<CreateTagRequest, Def_Tag>()
                .ForMember(dest => dest.TagType, opt => opt.MapFrom(m => (int)Enum.Parse(typeof(TagTypeEnum), m.TagType, true)));
        }
    }
}
