using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Maps
{
    /// <summary>
    /// Mp_App映射配置
    /// </summary>
    public class MpAppMapProfile : Profile
    {
        public MpAppMapProfile()
        {
            // Mp_App <-> MpAppReviewListDto 映射
            CreateMap<Mp_App, MpAppReviewListDto>().ReverseMap();

            // Mp_App <-> MpAppDetailDto 映射
            CreateMap<Mp_App, MpAppDetailDto>()
                // 正向映射：忽略 Screenshots，因为它需要单独处理
                .ForMember(dest => dest.Screenshots, opt => opt.Ignore())
                .ReverseMap(); // 自动创建反向映射

            // UpdateMpAppRequest -> Mp_App
            CreateMap<UpdateMpAppFormRequest, Mp_App>();
        }
    }
}
