using AgentCentral.Application.Contracts.RequestModels.DocResource;
using AgentCentral.Application.Contracts.ResponseModels.DocResource;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Maps;

public class ResourceMapProfile : Profile
{
    public ResourceMapProfile()
    {
        CreateMap<CreateDocResourceRequest, Doc_Resource>().ReverseMap();
        CreateMap<Doc_Resource, DocResourceResponse>().ReverseMap();
        CreateMap<UpdateDocResourceRequest, Doc_Resource>().ReverseMap();
        CreateMap<CommonProgramDto, CommonProgram>().ReverseMap();
    }
}