using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Maps
{
    /// <summary>
    /// Application parameter mapping profile
    /// </summary>
    public class AppParamMapProfile : Profile
    {
        public AppParamMapProfile()
        {
            // Parameter definition mappings
            CreateMap<Def_App_Param, AppParamDto>();
            CreateMap<CreateAppParamDto, Def_App_Param>()
                .ForMember(dest => dest.IsRequired, opt => opt.MapFrom(src =>
                    !string.IsNullOrEmpty(src.ValidationRules) &&
                    src.ValidationRules.Contains("\"required\":true")));

            // Parameter value mappings
            CreateMap<Def_App_Param_Value, AppParamValueDto>().ReverseMap();
        }
    }
}
