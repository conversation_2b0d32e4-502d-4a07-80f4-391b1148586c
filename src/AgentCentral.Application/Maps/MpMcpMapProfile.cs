using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Domain.Entities;
using AutoMapper;
using Newtonsoft.Json;

namespace AgentCentral.Application.Maps
{
    /// <summary>
    /// Mp_Mcp映射配置
    /// </summary>
    public class MpMcpMapProfile : Profile
    {
        public MpMcpMapProfile()
        {
            // Mp_Mcp <-> MpMcpListDto 映射
            CreateMap<Mp_Mcp, MpMcpListDto>()
                // 忽略ReviewRemark，因为它需要从工作流信息中单独处理
                .ForMember(dest => dest.ReviewRemark, opt => opt.Ignore())
                // 配置ReviewResult的JSON转换，包含异常处理
                .ForMember(dest => dest.ReviewResult, opt => opt.MapFrom(src => ParseReviewResult(src.ReviewResult)))
                .ReverseMap();

            // Mp_Mcp <-> MpMcpDetailDto 映射
            CreateMap<Mp_Mcp, MpMcpDetailDto>()
                // 配置ReviewResult的JSON转换，包含异常处理
                .ForMember(dest => dest.ReviewResult, opt => opt.MapFrom(src => ParseReviewResult(src.ReviewResult)))
                // 忽略WorkflowLogs，因为它需要单独从工作流服务获取
                .ForMember(dest => dest.WorkflowLogs, opt => opt.Ignore())
                .ReverseMap();

            // Mp_Mcp -> MpMcpDetailDto 映射
            CreateMap<UpdateMpMcpDto, Mp_Mcp>();
        }

        /// <summary>
        /// 解析 ReviewResult JSON 字符串为 MpMcpWorkflowCommentsDto 对象
        /// </summary>
        /// <param name="reviewResultJson">ReviewResult JSON 字符串</param>
        /// <returns>反序列化后的对象，如果解析失败返回 null</returns>
        private static MpMcpWorkflowCommentsDto ParseReviewResult(string reviewResultJson)
        {
            if (string.IsNullOrEmpty(reviewResultJson))
            {
                return null;
            }

            try
            {
                return JsonConvert.DeserializeObject<MpMcpWorkflowCommentsDto>(reviewResultJson);
            }
            catch (JsonException)
            {
                // 静默处理 JSON 解析异常，返回 null
                return null;
            }
        }
    }
} 