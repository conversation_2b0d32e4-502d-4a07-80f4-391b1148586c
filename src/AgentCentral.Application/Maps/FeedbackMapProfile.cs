using AgentCentral.Application.Contracts.Dtos.Feedback;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;
using AutoMapper;

namespace AgentCentral.Application.Maps
{
    public class FeedbackMapProfile : Profile
    {
        public FeedbackMapProfile()
        {
            CreateMap<FeedbackDto, Def_Feedback>();
            CreateMap<Def_Feedback, FeedbackOutputDto>();
            CreateMap<FeedbackModel, FeedbackOutputDto>();
        }
    }
}
