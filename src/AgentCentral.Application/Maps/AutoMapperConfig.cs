 using AutoMapper;

namespace AgentCentral.Application.Maps
{
    public static class AutoMapperConfig
    {
        /// <summary>
        /// 单条实体类型映射,默认字段名字一一对应,只支持简单类型的映射，不能有跨命名空间的属性
        /// </summary>
        /// <typeparam name="TDestination">类型</typeparam>
        /// <param name="source">要被转化的数据</param>
        /// <returns></returns>
        public static TDestination MapTo<TDestination>(this object source)
            where TDestination : class, new()
        {
            if (source == null)
            {
                return null;
            }

            MapperConfiguration config = new(cfg =>
            {
                _ = cfg.CreateMap(source.GetType(), typeof(TDestination));
            });
            IMapper mapper = config.CreateMapper();
            return mapper.Map<TDestination>(source);
        }

        /// <summary>
        /// 实体列表类型映射,默认字段名字一一对应，不能有跨命名空间的属性
        /// </summary>
        /// <typeparam name="TDestination">Dto类型</typeparam>
        /// <param name="source">要被转化的数据</param>
        /// <returns>转化之后的实体列表</returns>
        public static List<TDestination> MapToList<TDestination>(this IEnumerable<object> source)
            where TDestination : class, new()
        {
            if (source == null || !source.Any())
            {
                return null;
            }

            MapperConfiguration config = new(cfg =>
            {
                _ = cfg.CreateMap(source.FirstOrDefault()?.GetType(), typeof(TDestination));
            });
            IMapper mapper = config.CreateMapper();
            return mapper.Map<List<TDestination>>(source);
        }
    }
}