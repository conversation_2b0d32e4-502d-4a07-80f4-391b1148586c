using AgentCentral.Application.Contracts.Dtos.ChangeLog;
using AgentCentral.Domain.Entities;
using AutoMapper;
using Item.Internal.ChangeLog.Models;

namespace AgentCentral.Application.Maps
{
    public class ChangeLogMapProfile : Profile
    {
        public ChangeLogMapProfile()
        {
            CreateMap<ChangeLogResultModel, ChangeLogData>()
                .ForMember(x => x.Message, m => m.MapFrom(p => p.Messages))
                .ForMember(x => x.UserId, m => m.MapFrom(p => p.UserId))
                .ForMember(x => x.UserName, m => m.MapFrom(p => p.UserName))
                .ForMember(x => x.OpTime, m => m.MapFrom(p => p.OpTime.ToString("yyyy-MM-dd HH:mm:ss zzz")))
                .ForMember(x => x.OpType, m => m.MapFrom(p => p.OpType))
                .ForMember(x => x.OpId, m => m.MapFrom(p => p.OpId));

            CreateMap<ChangeLogRequestDto, SearchModel>()
                .ConvertUsing<ChangeLogRequestDtoToSearchModelConverter>();

            CreateMap<DataChangeLog, ChangeLogDto>()
                .ReverseMap();
        }
    }

    public class ChangeLogRequestDtoToSearchModelConverter : ITypeConverter<ChangeLogRequestDto, SearchModel>
    {
        public SearchModel Convert(ChangeLogRequestDto source, SearchModel destination, ResolutionContext context)
        {
            return new SearchModel()
            {
                Category = source.Category,
                BusinessId = source.CustomerId,
                Pageing = new PageingModel()
                {
                    PageIndex = source.PageIndex,
                    PageSize = source.PageSize,
                }
            };
        }
    }
}
