using AgentCentral.Application.Contracts.ResponseModels.Attachment;
using AgentCentral.Domain.Entities;
using AutoMapper;

namespace AgentCentral.Application.Maps
{
    internal class CommonMapProfile : Profile
    {
        public CommonMapProfile()
        {
            CreateMap<Doc_Attachment, AttachmentResponse>().BeforeMap((src, dest) => { dest.AttachmentId = src.Id; })
                .ReverseMap();
        }
    }
}