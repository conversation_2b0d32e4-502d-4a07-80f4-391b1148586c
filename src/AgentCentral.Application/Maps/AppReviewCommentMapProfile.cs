using AgentCentral.Application.Contracts.RequestModels.AppReviewComment;
using AgentCentral.Application.Contracts.ResponseModels.AppReviewComment;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;
using AutoMapper;

namespace AgentCentral.Application.Maps;

public class AppReviewCommentMapProfile : Profile
{
    public AppReviewCommentMapProfile()
    {
        CreateMap<ReviewCommentModel, AppReviewCommentResponse>().ReverseMap();
        CreateMap<CreateAppReviewCommentRequest, Def_App_Review_Comment>().ReverseMap();
    }
}