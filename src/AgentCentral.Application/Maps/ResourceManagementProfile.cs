using AgentCentral.Application.Contracts.Dtos.Attachment;
using AgentCentral.Application.Contracts.Dtos.ResourceManagement;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;
using AutoMapper;

namespace AgentCentral.Application.MapperProfiles
{
    public class ResourceManagementProfile : Profile
    {
        public ResourceManagementProfile()
        {
            // Doc_Resource_People 映射
            CreateMap<ResourcePeopleDto, Doc_Resource_People>();
            CreateMap<Doc_Resource_People, ResourcePeopleDto>();

            // Doc_Resource_Process 映射
            CreateMap<ResourceProcessDto, Doc_Resource_Process>();
            CreateMap<Doc_Resource_Process, ResourceProcessDto>().BeforeMap((src, dest) => dest.UserName = src.CreateName);

            // Doc_Resource_Customer_Process 映射
            CreateMap<ResourceCustomerProcessDto, Doc_Resource_Customer_Process>();
            CreateMap<Doc_Resource_Customer_Process, ResourceCustomerProcessDto>();

            // Doc_Attachment 映射
            CreateMap<Doc_Attachment, AttachmentDto>();

            CreateMap<ResourceProcessListModel, ResourceProcessListDto>();
        }
    }
}