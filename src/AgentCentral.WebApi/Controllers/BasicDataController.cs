using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.HrmDtos;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace AgentCentral.WebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("v{version:apiVersion}/basic-data")]
    public class BasicDataController : ControllerBase
    {
        private readonly HrmClient _hrmClient;

        private readonly IOptionsSnapshot<KnowledgeBaseProcessOptions> _options;

        public BasicDataController(HrmClient hrmClient,
            IOptionsSnapshot<KnowledgeBaseProcessOptions> options)
        {
            _hrmClient = hrmClient;
            _options = options;
        }

        [HttpGet("hrm/companies")]
        [ProducesResponseType(typeof(SuccessResponse<List<CompanyDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetHrmCompaniesAsync(string keyWord, int pageIndex = 1, int pageSize = 500)
        {
            var companies = await _hrmClient.GetCompaniesAsync(keyWord, pageIndex, pageSize);
            return Success(companies);
        }

        [HttpGet("hrm/departments")]
        [ProducesResponseType(typeof(SuccessResponse<List<DepartmentDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetHrmDepartmentsAsync(string companyCode)
        {
            var departments = await _hrmClient.GetDepartmentsAsync(companyCode);
            return Success(departments);
        }

        [HttpGet("knowledge-base-process")]
        [ProducesResponseType(typeof(SuccessResponse<KnowledgeBaseProcessOptions>), StatusCodes.Status200OK)]
        public IActionResult GetKnowledgeBaseProcessOptions()
        {
            return Success(_options.Value);
        }
    }
}
