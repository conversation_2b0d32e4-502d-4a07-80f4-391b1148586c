using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.WebApi.Model.Response;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// 应用市场应用控制器
    /// </summary>
    [Route("v{version:apiVersion}/mp-app")]
    [ApiController]
    [AllowAnonymous]
    [ApiVersion(1.0)]
    public class MpAppController : ControllerBase
    {
        private readonly IMpAppService _mpAppService;
        private readonly UserContext _userContext;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mpAppService">应用市场应用服务</param>
        /// <param name="userContext">用户上下文</param>
        public MpAppController(IMpAppService mpAppService, UserContext userContext)
        {
            _mpAppService = mpAppService;
            _userContext = userContext;
        }


        /// <summary>
        /// 创建或更新应用（支持multipart/form-data）
        /// </summary>
        /// <param name="formRequest">表单请求</param>
        /// <returns>是否成功</returns>
        [HttpPost("update")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateMpAppFromForm([FromBody] UpdateMpAppFormRequest formRequest)
        {
            var result = await _mpAppService.UpdateMpAppFromFormAsync(formRequest);
            return Success(result);
        }
    }
} 