using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.ResponseModels.Attachment;
using AgentCentral.WebApi.Model.Response;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// Attachment controller
    /// </summary>
    [ApiController]
    [ApiVersion(1.0)]
    [Route("v{version:apiVersion}/attachment")]
    [Authorize]
    public class AttachmentController : ControllerBase
    {
        private readonly IAttachmentService _attachmentService;
        private readonly UserContext _userContext;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="attachmentService">Attachment service</param>
        /// <param name="userContext">User context</param>
        public AttachmentController(IAttachmentService attachmentService,
            UserContext userContext)
        {
            _attachmentService = attachmentService;
            _userContext = userContext;
        }

        /// <summary>
        /// Upload attachment
        /// </summary>
        /// <param name="formFile">File to upload</param>
        /// <returns>Uploaded attachment information</returns>
        [HttpPost]
        [ProducesResponseType(typeof(SuccessResponse<AttachmentResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UploadAsync(IFormFile formFile)
        {
            return Success(await _attachmentService.UploadAsync(formFile));
        }

        /// <summary>
        /// Get batch attachments stream
        /// </summary>
        /// <param name="businessId">Business ID</param>
        /// <returns>Zipped attachments file stream</returns>
        [HttpGet("batch/{business_id:long}/{attachment_type}/stream")]
        [Produces("application/zip")]
        [ProducesResponseType(typeof(FileStreamResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBatchAttachmentsStreamAsync(
            [FromRoute(Name = "business_id")] long businessId, [FromRoute(Name = "attachment_type")] AttachmentTypeEnum attachmentType)
        {
            var (stream, fileName) = await _attachmentService.GetBatchAttachmentsStreamAsync(businessId, attachmentType);
            return File(stream, "application/zip", fileName);
        }

        /// <summary>
        /// Get attachment stream
        /// </summary>
        /// <param name="attachmentId">Attachment ID</param>
        /// <returns>Attachment file stream</returns>
        [HttpGet("{attachment_id:long}/stream")]
        [Produces("application/octet-stream")]
        [ProducesResponseType(typeof(FileStreamResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAttachmentStreamAsync([FromRoute(Name = "attachment_id")] long attachmentId)
        {
            var (stream, fileName) = await _attachmentService.GetAttachmentStreamAsync(attachmentId);
            return File(stream, "application/octet-stream", fileName);
        }

        /// <summary>
        /// Get Access URL
        /// </summary>
        /// <param name="id">Attachment ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Attachment URL</returns>
        [HttpGet("{attachment_id}/url")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SuccessResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAccessUrlAsync([FromRoute(Name = "attachment_id")] long id, CancellationToken cancellationToken)
        {
            return Success(await _attachmentService.GetAccessUrlAsync(id, cancellationToken));
        }
    }
}
