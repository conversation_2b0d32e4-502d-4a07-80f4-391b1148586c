using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// Application parameter controller
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("v{version:apiVersion}/app-param")]
    public class AppParamController : ControllerBase
    {
        private readonly IAppParamService _appParamService;

        public AppParamController(IAppParamService appParamService)
        {
            _appParamService = appParamService;
        }

        /// <summary>
        /// Get parameter definitions by application ID
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>List of parameter definitions</returns>
        [HttpGet("{app_id}/definitions")]
        [ProducesResponseType(typeof(SuccessResponse<List<AppParamDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetParamDefinitionsAsync([FromRoute(Name = "app_id")] long appId)
        {
            var definitions = await _appParamService.GetParamDefinitionsAsync(appId);
            return Success(definitions);
        }

        /// <summary>
        /// Get parameter values by dify application ID
        /// </summary>
        /// <param name="appId">Dify application ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>List of parameter values</returns>
        [AllowAnonymous]
        [HttpGet("{dify_app_id}/values")]
        [ProducesResponseType(typeof(SuccessResponse<List<AppParamValueDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetParamValuesAsync([FromRoute(Name = "dify_app_id")] string appId, [FromQuery] long userId)
        {
            var values = await _appParamService.GetParamValuesAsync(appId, userId);
            return Success(values);
        }

        /// <summary>
        /// Create parameter definitions in batch
        /// </summary>
        /// <param name="appId">Dify application ID</param>
        /// <param name="dtos">List of parameter definitions to create</param>
        /// <returns>Created parameter definitions</returns>
        [AllowAnonymous]
        [HttpPost("{dify_app_id}/definitions")]
        [ProducesResponseType(typeof(SuccessResponse<List<AppParamDto>>), StatusCodes.Status201Created)]
        public async Task<IActionResult> CreateParamDefinitionsAsync([FromRoute(Name = "dify_app_id")] string appId, [FromBody] List<CreateAppParamDto> dtos)
        {
            var definitions = await _appParamService.CreateParamDefinitionsAsync(appId, dtos);
            return Success(definitions);
        }

        [AllowAnonymous]
        [HttpPut("{dify_app_id}/definitions")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status201Created)]
        public async Task<IActionResult> UpdateParamDefinitionsAsync([FromRoute(Name = "dify_app_id")] string appId, [FromBody] List<CreateAppParamDto> dtos)
        {
            var definitions = await _appParamService.UpdateParamDefinitionsAsync(appId, dtos);
            return Success(definitions);
        }

        /// <summary>
        /// Save parameter values in batch
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="values">List of parameter values to save</param>
        /// <returns>List of saved parameter values</returns>
        [HttpPost("{app_id}/values")]
        [ProducesResponseType(typeof(SuccessResponse<List<AppParamValueDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SaveParamValuesAsync([FromRoute(Name = "app_id")] long appId, [FromBody] List<SaveAppParamValueDto> values)
        {
            var paramValues = await _appParamService.SaveParamValuesAsync(appId, values);
            return Success(paramValues);
        }

        /// <summary>
        /// Check application parameter configuration status
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>Parameter configuration status</returns>
        [HttpGet("{app_id}/check-status")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckParamStatusAsync([FromRoute(Name = "app_id")] long appId)
        {
            var status = await _appParamService.CheckParamStatusAsync(appId);
            return Success(!status.RequiresParameters || status.IsConfigured);
        }

        /// <summary>
        /// Get parameter values by keys for current user
        /// </summary>
        /// <param name="keys">Parameter keys</param>
        /// <returns>Latest parameter values</returns>
        [HttpPost("values-by-keys")]
        [ProducesResponseType(typeof(SuccessResponse<Dictionary<string, string>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetParamValuesByKeysAsync([FromBody] List<string> keys)
        {
            var dictionary = await _appParamService.GetParamDictionaryAsync(keys);
            return Success(dictionary.Select(d => new { d.Key, d.Value }));
        }
    }
}
