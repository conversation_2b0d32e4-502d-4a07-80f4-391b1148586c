using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.ResponseModels.AppSubscribe;
using Asp.Versioning;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Application.Contracts.RequestModels.App;

namespace AgentCentral.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [ApiVersion(1.0)]
    [Route("v{version:apiVersion}/app-subscribe/{appId}")]
    public class AppSubscribeController : ControllerBase
    {
        private readonly IAppSubscribeService _appSubscribeService;

        public AppSubscribeController(IAppSubscribeService appSubscribeService)
        {
            _appSubscribeService = appSubscribeService;
        }

        /// <summary>
        /// 获取应用订阅用户列表
        /// </summary>
        [HttpGet("users")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(PageModelDto<AppSubscribeResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubscribeUsers([FromRoute] string appId, [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            var subscribers = await _appSubscribeService.GetSubscribeUsersAsync(appId, pageIndex, pageSize);
            return Success(subscribers);
        }

        /// <summary>
        /// 获取应用订阅总数
        /// </summary>
        [HttpGet("count")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubscribeCount([FromRoute] string appId)
        {
            var count = await _appSubscribeService.GetSubscribeCountAsync(appId);
            return Success(count);
        }

        /// <summary>
        /// 订阅应用
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> Subscribe(
            [FromRoute] string appId)
        {
            var subscription = await _appSubscribeService.SubscribeAsync(appId);
            return Success(subscription);
        }

        /// <summary>
        /// 检查订阅状态
        /// </summary>
        [HttpGet("check")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckSubscribedAsync([FromRoute] string appId)
        {
            var result = await _appSubscribeService.CheckSubscribedAsync(appId);
            return Success(result);
        }

        /// <summary>
        /// 获取当前用户订阅的所有应用
        /// </summary>
        [HttpGet("/v{version:apiVersion}/app-subscribe/subscribed")]
        [ProducesResponseType(typeof(PageModelDto<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserSubscribedApps(AppSearchPageDto searchDto)
        {
            var apps = await _appSubscribeService.GetUserSubscribedAppsAsync(searchDto);
            return Success(apps);
        }

        /// <summary>
        /// 取消订阅应用
        /// </summary>
        [HttpDelete]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> Unsubscribe([FromRoute] string appId)
        {
            var result = await _appSubscribeService.UnsubscribeAsync(appId);
            return Success(result);
        }
    }
}