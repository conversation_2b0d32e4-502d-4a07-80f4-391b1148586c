using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.WebApi.Model.Response;
using Asp.Versioning;
using Item.BlobProvider.FileService;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// 角色管理控制器
    /// </summary>
    [Route("v{version:apiVersion}/role")]
    [ApiController]
    [Authorize]
    public class RoleController : ControllerBase
    {
        private readonly IRoleService _roleService;
        private readonly UserContext _userContext;

        public RoleController(IRoleService roleService, UserContext userContext)
        {
            _roleService = roleService;
            _userContext = userContext;
        }

        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <param name="keyword">关键字查询</param>
        /// <param name="departmentId">部门ID</param>
        /// <returns>角色列表</returns>
        [HttpGet("list")]
        [ProducesResponseType(typeof(SuccessResponse<List<RoleDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRoles(string keyword, long? departmentId = null)
        {
            List<RoleDto> roles = await _roleService.GetRolesAsync(_userContext.TenantId, keyword, departmentId);
            return Success(roles);
        }

        /// <summary>
        /// 分页获取角色列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页角色列表</returns>
        [HttpPost("page")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<RoleDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRolePageList([FromBody] RoleSearchDto searchDto)
        {
            searchDto.TenantId = _userContext.TenantId;
            PageModelDto<RoleDto> rolePage = await _roleService.GetRolePageListAsync(searchDto);
            return Success(rolePage);
        }

        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="roleDto">角色信息</param>
        /// <returns>角色ID</returns>
        [HttpPost]
        [ProducesResponseType(typeof(SuccessResponse<long>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateRole([FromBody] RoleDto roleDto)
        {
            roleDto.TenantId = _userContext.TenantId;
            long roleId = await _roleService.CreateRoleAsync(roleDto);
            return Success(roleId);
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="roleDto">角色信息</param>
        /// <returns>是否成功</returns>
        [HttpPut]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateRole([FromBody] RoleDto roleDto)
        {
            roleDto.TenantId = _userContext.TenantId;
            bool result = await _roleService.UpdateRoleAsync(roleDto);
            return Success(result);
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>是否成功</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteRole(long id)
        {
            bool result = await _roleService.DeleteRoleAsync(id);
            return Success(result);
        }

        /// <summary>
        /// 获取角色详情
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>角色信息</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<RoleDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRole(long id)
        {
            RoleDto role = await _roleService.GetRoleAsync(id);
            return Success(role);
        }

        /// <summary>
        /// 根据部门ID获取角色列表
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <returns>角色列表</returns>
        [HttpGet("by-department/{departmentId}")]
        [ProducesResponseType(typeof(SuccessResponse<List<RoleDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRolesByDepartment(long departmentId)
        {
            List<RoleDto> roles = await _roleService.GetRolesByDepartmentAsync(departmentId, _userContext.TenantId);
            return Success(roles);
        }

        /// <summary>
        /// 检查角色编码是否已存在
        /// </summary>
        /// <param name="roleCode">角色编码</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        [HttpGet("check-code")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckRoleCode(string roleCode, long? departmentId = null, long? excludeId = null)
        {
            bool exists = await _roleService.IsRoleCodeExistsAsync(_userContext.TenantId, roleCode, departmentId, excludeId);
            return Success(exists);
        }

        /// <summary>
        /// 获取部门角色树形结构
        /// </summary>
        /// <param name="keyword">关键字查询</param>
        /// <returns>部门角色树形结构</returns>
        [HttpGet("department-role-tree")]
        [ProducesResponseType(typeof(SuccessResponse<List<DepartmentRoleTreeDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDepartmentRoleTree(string keyword = null)
        {
            List<DepartmentRoleTreeDto> departmentRoleTree = await _roleService.GetDepartmentRoleTreeAsync(_userContext.TenantId, keyword);
            return Success(departmentRoleTree);
        }

        /// <summary>
        /// 获取公司部门角色树形结构
        /// </summary>
        /// <param name="keyword">关键字查询</param>
        /// <returns>公司部门角色树形结构</returns>
        [HttpGet("company-department-role-tree")]
        [ProducesResponseType(typeof(SuccessResponse<List<CompanyDepartmentRoleTreeDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyDepartmentRoleTree(string keyword = null)
        {
            List<CompanyDepartmentRoleTreeDto> companyDepartmentRoleTree = await _roleService.GetCompanyDepartmentRoleTreeAsync(_userContext.Token, keyword);
            return Success(companyDepartmentRoleTree);
        }

        /// <summary>
        /// 判断角色是否与用户关联
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否存在关联关系</returns>
        [HttpGet("has-user-associated/{roleId}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> HasUserAssociated(long roleId)
        {
            bool hasAssociated = await _roleService.HasUserAssociatedAsync(roleId);
            return Success(hasAssociated);
        }
    }
}
