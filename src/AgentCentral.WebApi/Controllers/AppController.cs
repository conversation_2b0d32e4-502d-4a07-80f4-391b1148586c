using AgentCentral.WebApi.Model.Response;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.ResponseModels.App;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.Dtos.Workflow;

namespace AgentCentral.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("v{version:apiVersion}/[controller]")]
    public class AppController : ControllerBase
    {
        private readonly IAppService _appService;

        private readonly IAppWorkflowService _appWorkflowService;

        public AppController(IAppService appService,
            IAppWorkflowService appWorkflowService)
        {
            _appService = appService;
            _appWorkflowService = appWorkflowService;
        }

        /// <summary>
        /// 获取应用列表（管理员）
        /// </summary>
        /// <param name="searchDto">查询参数</param>
        /// <returns>分页后的应用列表</returns>
        [HttpPost("page")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<AppDetailPageResponse>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPageListAsync(AppSearchPageDto searchDto)
        {
            PageModelDto<AppDetailPageResponse> result = await _appService.GetPageListAsync(searchDto, AppQueryModeEnum.All);
            return Success(result);
        }

        /// <summary>
        /// 获取应用审核列表
        /// </summary>
        /// <param name="searchDto">查询参数</param>
        /// <returns>分页后的应用审核列表</returns>
        [HttpPost("review/page")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<AppReviewDetailPageResponse>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetReviewPageListAsync(AppReviewSearchPageDto searchDto)
        {
            PageModelDto<AppReviewDetailPageResponse> result = await _appService.GetReviewPageListAsync(searchDto);
            return Success(result);
        }

        [HttpPost("submit/{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SubmitForApprovalAsync([FromRoute(Name = "app_id")] long appId)
        {
            return Success(await _appWorkflowService.SubmitForApprovalAsync(new ApprovalActionDto
            {
                BusinessId = appId,
                BusinessType = WorkflowBusinessTypeEnum.AgentReview
            }));
        }

        [HttpPost("approval/{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ApproveAsync([FromRoute(Name = "app_id")] long appId)
        {
            return Success(await _appWorkflowService.ApproveAsync(new ApprovalActionDto
            {
                BusinessId = appId,
                BusinessType = WorkflowBusinessTypeEnum.AgentReview
            }));
        }

        [HttpPost("reject/{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RejectAsync([FromRoute(Name = "app_id")] long appId, ApprovalActionDto approvalAction)
        {
            approvalAction.BusinessId = appId;
            return Success(await _appWorkflowService.RejectAsync(approvalAction));
        }

        [HttpPost("delist/{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DelistAsync([FromRoute(Name = "app_id")] long appId, ApprovalActionDto approvalAction)
        {
            approvalAction.BusinessId = appId;
            return Success(await _appWorkflowService.DelistAsync(approvalAction));
        }

        /// <summary>
        /// Get application by ID
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>Application details</returns>
        [HttpGet("{appId}")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByIdAsync(string appId)
        {
            AppDetailResponse app = await _appService.GetByAppIdAsync(appId);
            return Success(app);
        }

        /// <summary>
        /// Create application from Dify platform
        /// </summary>
        /// <param name="request">Application creation request</param>
        /// <returns>Created application details</returns>
        [HttpPost]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAsync([FromBody] CreateAppRequest request)
        {
            AppDetailResponse result = await _appService.CreateAsync(request);
            return Success(result);
        }

        /// <summary>
        /// Create application from Agent platform
        /// </summary>
        /// <param name="createApp">Application creation data</param>
        /// <returns>Created application details</returns>
        [HttpPost("/v{version:apiVersion}/agent/app")]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAgentAppAsync([FromBody] CreateAppDto createApp)
        {
            AppDetailResponse result = await _appService.CreateAppAsync(createApp);
            return Success(result);
        }

        /// <summary>
        /// Update application from Dify platform
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="appDto">Application update data</param>
        /// <returns>Updated application details</returns>
        [HttpPut("{appId}")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAsync(string appId, [FromBody] UpdateAppRequest appDto)
        {
            AppDetailResponse result = await _appService.UpdateAsync(appId, appDto);
            return Success(result);
        }

        /// <summary>
        /// Update application from Agent platform
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="appDto">Application update data</param>
        /// <returns>Updated application details</returns>
        [HttpPut("/v{version:apiVersion}/agent/app/{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAgentAppAsync([FromRoute(Name = "app_id")] string appId, [FromBody] UpdateAppDto appDto)
        {
            AppDetailResponse result = await _appService.UpdateAppAsync(appId, appDto);
            return Success(result);
        }

        /// <summary>
        /// Delete application
        /// </summary>
        /// <param name="appId">Application ID to delete</param>
        /// <returns>True if deletion was successful</returns>
        [HttpDelete("{appId}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync(string appId)
        {
            bool result = await _appService.DeleteAsync(appId);
            return Success(result);
        }

        /// <summary>
        /// Get current user's application list
        /// </summary>
        /// <param name="searchDto">Search parameters</param>
        /// <returns>Paged list of user's applications</returns>
        [HttpGet]
        [Route("list")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<AppDetailResponse>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserAppsAsync([FromQuery] AppSearchDto searchDto)
        {
            PageModelDto<AppDetailResponse> result = await _appService.GetUserAppsAsync(searchDto);
            return Success(result);
        }

        /// <summary>
        /// Update application status
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="statusDto">Status update data</param>
        /// <returns>True if status update was successful</returns>
        [HttpPut("{app_id}/status")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateStatusAsync([FromRoute(Name = "app_id")] string appId, UpdateAppStatusDto statusDto)
        {
            return Success(await _appService.UpdateAppStatusAsync(appId, statusDto.Status));
        }

        /// <summary>
        /// Copy application
        /// </summary>
        /// <param name="appId">Source application ID</param>
        /// <param name="copyApp">Copy configuration data</param>
        /// <returns>Copied application details</returns>
        [HttpPost("/v{version:apiVersion}/agent/app/{app_id}/copy")]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CopyAppAsync([FromRoute(Name = "app_id")] string appId, CreateAppDto copyApp)
        {
            return Success(await _appService.CopyAppAsync(appId, copyApp));
        }

        /// <summary>
        /// Publish application from Dify platform
        /// </summary>
        /// <param name="appId">Application ID to publish</param>
        /// <returns>True if publish was successful</returns>
        [HttpPut("/v{version:apiVersion}/agent/app/{app_id}/publish")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PublishFromDifyAsync([FromRoute(Name = "app_id")] string appId)
        {
            return Success(await _appService.UpdateDifyPublishedAsync(appId, true));
        }

        /// <summary>
        /// 更新应用权限
        /// </summary>
        /// <param name="dto">更新权限DTO</param>
        /// <returns>是否更新成功</returns>
        [HttpPost("permission")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAppPermissionAsync(UpdateAppPermissionDto dto)
        {
            bool result = await _appService.UpdateAppPermissionAsync(dto);
            return Success(result);
        }

        [HttpPost("auto-approve/{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AutoApproveAsync([FromRoute(Name = "app_id")] long appId)
        {
            return Success(await _appWorkflowService.DifyAutoApprovalAsync(appId));
        }

        /// <summary>
        /// Dify platform-create application from source app
        /// </summary>
        /// <param name="request">Application creation request</param>
        /// <returns>Created application details</returns>
        [AllowAnonymous]
        [HttpPost("/api/v{version:apiVersion}/dify/app/create-from-source")]
        [ProducesResponseType(typeof(SuccessResponse<AppDetailResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAgentFromSourceAsync([FromBody] CreateAgentFromSourceRequest request)
        {
            AppDetailResponse result = await _appService.CreateAgentFromSourceAsync(request);
            return Success(result);
        }


        /// <summary>
        /// 更新Agent的Guest Mode状态
        /// </summary>
        /// <param name="dto">更新请求</param>
        /// <returns>是否更新成功</returns>
        [HttpPost("guest-mode")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateGuestModeAsync([FromBody] UpdateGuestModeDto dto)
        {
            bool result = await _appService.UpdateGuestModeAsync(dto);
            return Success(result);
        }

        /// <summary>
        /// 批量更新Agent的Guest Mode状态
        /// </summary>
        /// <param name="dto">批量更新请求</param>
        /// <returns>是否更新成功</returns>
        [HttpPost("batch-guest-mode")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> BatchUpdateGuestModeAsync([FromBody] BatchUpdateGuestModeDto dto)
        {
            bool result = await _appService.BatchUpdateGuestModeAsync(dto);
            return Success(result);
        }    }
}