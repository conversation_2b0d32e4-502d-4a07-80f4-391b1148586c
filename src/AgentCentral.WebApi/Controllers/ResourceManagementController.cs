using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos;
using AgentCentral.Application.Contracts.RequestModels.ResourceManagement;
using AgentCentral.Application.Contracts.ResponseModels.ResourceManagement;
using AgentCentral.Application.Contracts.Services;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// 资源管理控制器
    /// </summary>
    [Authorize]
    [ApiController]
    [ApiVersion(1.0)]
    [Route("v{version:apiVersion}/employee/{employeeId}/resource-management")]
    public class ResourceManagementController : ControllerBase
    {
        private readonly IResourceManagementService _resourceManagementService;
        private readonly ClockApiClient _hrmClient;
        private readonly CrmClient _crmClient;

        public ResourceManagementController(IResourceManagementService resourceManagementService,
            ClockApiClient hrmClient, CrmClient crmClient)
        {
            _resourceManagementService = resourceManagementService;
            _hrmClient = hrmClient;
            _crmClient = crmClient;
        }

        /// <summary>
        /// 创建资源信息
        /// </summary>
        /// <param name="request">创建资源请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("/v{version:apiVersion}/employee/resource-management")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateResource([FromBody] CreateResourceRequest request)
        {
            var result = await _resourceManagementService.CreateResourceAsync(request);
            return Success(result);
        }

        /// <summary>
        /// 更新资源信息
        /// </summary>
        /// <param name="request">更新资源请求</param>
        /// <returns>操作结果</returns>
        [HttpPut("/v{version:apiVersion}/employee/resource-management")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateResource([FromBody] UpdateResourceRequest request)
        {
            var result = await _resourceManagementService.UpdateResourceAsync(request);
            return Success(result);
        }

        /// <summary>
        /// 获取资源信息
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>资源信息</returns>
        [HttpGet("/v{version:apiVersion}/employee/resource-management")]
        [ProducesResponseType(typeof(ResourceResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResource(string employeeId)
        {
            var result = await _resourceManagementService.GetResourceAsync(employeeId);
            return Success(result);
        }

        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>员工信息</returns>
        [HttpGet("/v{version:apiVersion}/employee/{employeeId}")]
        [ProducesResponseType(typeof(HrmEmployeeDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetEmployee([FromRoute] string employeeId)
        {
            var result = await _hrmClient.GetEmployeeByIdAsync(employeeId);
            return Success(result);
        }

        [HttpGet("/v{version:apiVersion}/retailer-or-customer")]
        [ProducesResponseType(typeof(List<CrmCustomerOrRetailerDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCustomerPageListAsync(string accountName,
            int tags)
        {
            var utData = await _crmClient.GetCustomerPageListAsync(1, 10, accountName, tags, "SBFH");
            var ufData = await _crmClient.GetCustomerPageListAsync(1, 10, accountName, tags, "LT");
            utData.ForEach(f => f.TenantId = "SBFH");
            ufData.ForEach(f => f.TenantId = "LT");
            return Success(utData.Union(ufData).Distinct());
        }

        [HttpGet("/v{version:apiVersion}/resource-management/sync")]
        public async Task<IActionResult> SyncResourcesAsync()
        {
            await _resourceManagementService.SyncResourcesAsync(default);
            return Success();
        }
    }
}