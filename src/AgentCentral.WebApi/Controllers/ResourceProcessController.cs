using AgentCentral.WebApi.Model.Response;
using AgentCentral.Application.Contracts.Dtos.Attachment;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ResourceManagement;
using AgentCentral.Application.Contracts.RequestModels.ResourceManagement;
using AgentCentral.Application.Contracts.Services;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// 资源流程控制器
    /// </summary>
    [Authorize]
    [ApiController]
    [ApiVersion(1.0)]
    [Route("v{version:apiVersion}/resource-process")]
    public class ResourceProcessController : ControllerBase
    {
        private readonly IResourceManagementService _resourceManagementService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="resourceManagementService">资源管理服务</param>
        public ResourceProcessController(IResourceManagementService resourceManagementService)
        {
            _resourceManagementService = resourceManagementService;
        }

        /// <summary>
        /// 获取资源流程列表
        /// </summary>
        /// <param name="searchDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("list")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<ResourceProcessListDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResourceProcessListAsync(ResourceProcessSearchDto searchDto)
        {
            var result = await _resourceManagementService.GetResourceProcessListAsync(searchDto);
            return Success(result);
        }

        /// <summary>
        /// 删除资源流程（支持单个和批量删除）
        /// </summary>
        /// <param name="ids">流程ID列表</param>
        /// <returns>操作结果</returns>
        [HttpPost("delete")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteResourceProcessAsync([FromBody] List<long> ids)
        {
            var result = await _resourceManagementService.BatchDeleteResourceProcessAsync(ids);
            return Success(result);
        }

        /// <summary>
        /// 获取资源流程的文件信息
        /// </summary>
        /// <param name="id">流程ID</param>
        /// <returns>文件列表</returns>
        [HttpGet("{id}/files")]
        [ProducesResponseType(typeof(SuccessResponse<List<AttachmentDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResourceProcessFilesAsync(long id)
        {
            var result = await _resourceManagementService.GetResourceProcessFilesAsync(id);
            return Success(result);
        }

        /// <summary>
        /// 删除资源流程文件
        /// </summary>
        /// <param name="processId">流程ID</param>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{processId}/files/{attachmentId}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteResourceProcessFileAsync(long processId, long attachmentId)
        {
            var result = await _resourceManagementService.DeleteResourceProcessFileAsync(processId, attachmentId);
            return Success(result);
        }
    }
}