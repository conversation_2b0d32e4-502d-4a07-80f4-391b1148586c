using AgentCentral.Application.Contracts.Dtos.Tag;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.Tag;
using AgentCentral.Application.Contracts.ResponseModels.Tag;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    [Route("v{version:apiVersion}/tag")]
    [ApiController]
    [Authorize]
    public class TagController : ControllerBase
    {
        private readonly ITagService _tagService;

        public TagController(ITagService tagService)
        {
            _tagService = tagService;
        }

        [AllowAnonymous]
        [HttpPost]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateTag(CreateTagRequest request)
        {
            return Success(await _tagService.CreateTagRequest(request));
        }

        [AllowAnonymous]
        [HttpPost("list")]
        [ProducesResponseType(typeof(SuccessResponse<TagResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetTags(SearchTagRequest searchRequest)
        {
            return Success(await _tagService.GetTags(searchRequest.TagName));
        }

        [HttpDelete("{tag_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteTagAsync([FromRoute(Name = "tag_id")] long tagId)
        {
            return Success(await _tagService.DeleteTagAsync(tagId));
        }

        [AllowAnonymous]
        [HttpDelete("dify/{tag_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteTagByDify([FromRoute(Name = "tag_id")] string difyTagId)
        {
            return Success(await _tagService.DeleteTagByDifyId(difyTagId));
        }

        [AllowAnonymous]
        [HttpPut("dify/{tag_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateTagByDify([FromRoute(Name = "tag_id")] string difyTagId, UpdateTagRequest request)
        {
            return Success(await _tagService.UpdateTagByDify(difyTagId, request));
        }

        [HttpPost("create")]

        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateTagAsync(CreateTagDto createDto)
        {
            return Success(await _tagService.CreateTagAsync(createDto));
        }

        [HttpPut("update/{tag_id}")]

        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateTagAsync([FromRoute(Name = "tag_id")] long tagId, CreateTagDto createDto)
        {
            return Success(await _tagService.UpdateTagAsync(tagId, createDto));
        }
    }
}
