using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Item.Internal.Auth.Authentication.JwtBearer;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.Application.Contracts.Dtos;
using Microsoft.Extensions.Options;
using AgentCentral.Application.Client;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// Login related controller
    /// </summary>
    [ApiController]
    [ApiVersion(1.0)]
    [Route("oauth/v{version:apiVersion}")]
    [AllowAnonymous]
    public class LoginController : ControllerBase
    {
        private readonly IOptionsSnapshot<JwtOptions> _jwtOptions;
        private readonly IamClient _iamClient;

        /// <summary>
        /// Constructor
        /// </summary>
        public LoginController(
            IOptionsSnapshot<JwtOptions> jwtOptions,
            IamClient iamClient)
        {
            _jwtOptions = jwtOptions;
            _iamClient = iamClient;
        }

        /// <summary>
        /// Login authentication
        /// </summary>
        /// <param name="loginCreate">Login credentials</param>
        /// <returns>Authentication token</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> LoginAsync([FromBody] LoginDto loginCreate)
        {
            try
            {
                // 使用密码方式获取token
                var tokenResponse = await _iamClient.GetTokenByPasswordAsync(loginCreate.Username, loginCreate.Password);

                // 构建返回结果
                var result = new JwtTokenDto
                {
                    Access_token = tokenResponse.AccessToken,
                    Refresh_token = tokenResponse.RefreshToken,
                    Expires_in = tokenResponse.ExpiresIn,
                    Token_type = tokenResponse.TokenType,
                    Scope = ""
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                throw new AgentCentralException(ErrorCodeEnum.LoginFail, ex.Message);
            }
        }
    }
}
