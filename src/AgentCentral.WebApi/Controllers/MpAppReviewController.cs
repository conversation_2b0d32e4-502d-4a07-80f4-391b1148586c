using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Application.Contracts.RequestModels.Dify;
using AgentCentral.Application.Contracts.ResponseModels.Dify;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers;

/// <summary>
/// Mp_App审核API控制器
/// </summary>
[Authorize]
[ApiController]
[Route("v{version:apiVersion}/mp-app-review")]
public class MpAppReviewController : ControllerBase
{
    private readonly IMpAppReviewService _mpAppReviewService;
    private readonly IMpAppWorkflowService _mpAppWorkflowService;
    private readonly DifyClient _difyClient;
    private readonly ILogger<MpAppReviewController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MpAppReviewController(
        IMpAppReviewService mpAppReviewService,
        IMpAppWorkflowService mpAppWorkflowService,
        DifyClient difyClient,
        ILogger<MpAppReviewController> logger)
    {
        _mpAppReviewService = mpAppReviewService;
        _mpAppWorkflowService = mpAppWorkflowService;
        _difyClient = difyClient;
        _logger = logger;
    }

    /// <summary>
    /// 获取审核应用分页列表（统一接口，支持多状态查询）
    /// </summary>
    /// <param name="searchDto">搜索条件，通过AppStatus数组区分不同审核状态</param>
    /// <returns>分页结果</returns>
    [HttpPost("page")]
    [ProducesResponseType(typeof(SuccessResponse<PageModelDto<MpAppReviewListDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetPageListAsync([FromBody] MpAppReviewSearchPageDto searchDto)
    {
        if (searchDto.AppStatus is { Length: <= 0 })
        {
            searchDto.AppStatus = searchDto.ReviewStatus == 0
                ? [MpAppStatusEnum.AutoApproved]
                : [MpAppStatusEnum.AutoRejected, MpAppStatusEnum.Rejected, MpAppStatusEnum.Published, MpAppStatusEnum.Delisted, MpAppStatusEnum.Deleted];
        }
        PageModelDto<MpAppReviewListDto> result = await _mpAppReviewService.GetReviewPageListAsync(searchDto);
        return Success(result);
    }

    /// <summary>
    /// 获取应用详情
    /// </summary>
    /// <param name="appId">应用ID</param>
    /// <returns>应用详情</returns>
    [HttpGet("{appId}")]
    [ProducesResponseType(typeof(SuccessResponse<MpAppDetailDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAppDetailAsync([FromRoute] long appId)
    {
        MpAppDetailDto result = await _mpAppReviewService.GetAppDetailAsync(appId);
        return Success(result);
    }

    /// <summary>
    /// 隐私策略审核
    /// </summary>
    /// <param name="appId">应用ID</param>
    /// <returns>隐私策略审核结果</returns>
    [HttpPost("{appId}/privacy-policy-review")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(SuccessResponse<MpPrivacyPolicyReviewResponse>), StatusCodes.Status200OK)]
    public async Task<IActionResult> ReviewPrivacyPolicyAsync([FromRoute] long appId)
    {
        // 获取应用详情
        MpAppDetailDto appDetail = await _mpAppReviewService.GetAppDetailAsync(appId);
        
        // 验证隐私策略文档可用性
        if (appDetail.PrivacyPolicyDocumentType != PrivacyPolicyDocumentTypeEnum.Document && 
            string.IsNullOrEmpty(appDetail.PrivacyPolicyUrl))
        {
            await _mpAppReviewService.UpdatePrivacyPolicyReviewResultAsync(appId, null);
            return Success();
        }

        // 创建审核请求
        MpAppPrivacyPolicyReviewRequest request = new MpAppPrivacyPolicyReviewRequest
        {
            PrivacyPolicy = DifyFileInput.OfDocUrl(appDetail.PrivacyPolicyUrl)
        };

        // 调用Dify进行隐私策略审核
        MpPrivacyPolicyReviewResponse? result = await _difyClient.MpAppPrivacyPolicyReviewAsync(request);
        
        if (result == null)
        {
            // 清空历史审核结果
            try
            {
                bool clearSuccess = await _mpAppReviewService.ClearPrivacyPolicyReviewResultAsync(appId);
                if (!clearSuccess)
                {
                    _logger.LogWarning("Failed to clear privacy policy review result for App {AppId}", appId);
                }
                else
                {
                    _logger.LogInformation("Successfully cleared privacy policy review result for App {AppId}", appId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing privacy policy review result for App {AppId}", appId);
                // 清空操作失败不影响主要业务流程
            }
            
            throw new AgentCentralException(ErrorCodeEnum.SystemError, "Failed to review privacy policy");
        }

        // 持久化审核结果到数据库
        if (result != null)
        {
            try
            {
                bool updateSuccess = await _mpAppReviewService.UpdatePrivacyPolicyReviewResultAsync(appId, result);
                if (!updateSuccess)
                {
                    _logger.LogWarning("Failed to persist privacy policy review result for App {AppId}", appId);
                }
                else
                {
                    _logger.LogInformation("Successfully persisted privacy policy review result for App {AppId}", appId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error persisting privacy policy review result for App {AppId}", appId);
                // 不影响主要业务流程，继续返回结果
            }
        }

        return Success(result);
    }

    /// <summary>
    /// 批量审核通过应用
    /// </summary>
    /// <param name="batchDto">批量操作信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch/approve")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> BatchApproveAsync([FromBody] MpBatchOperationDto batchDto)
    {
        bool result = await _mpAppWorkflowService.BatchApproveAsync(batchDto.Ids, batchDto.Remark);
        return Success(result);
    }

    /// <summary>
    /// 批量审核拒绝应用
    /// </summary>
    /// <param name="batchDto">批量操作信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch/reject")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> BatchRejectAsync([FromBody] MpBatchOperationDto batchDto)
    {
        bool result = await _mpAppWorkflowService.BatchRejectAsync(batchDto.Ids, batchDto.Remark);
        return Success(result);
    }

    /// <summary>
    /// 批量下架应用
    /// </summary>
    /// <param name="batchDto">批量操作信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch/delist")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> BatchDelistAsync([FromBody] MpBatchOperationDto batchDto)
    {
        bool result = await _mpAppWorkflowService.BatchDelistAsync(batchDto.Ids, batchDto.Remark);
        return Success(result);
    }

    /// <summary>
    /// AI自动审批
    /// </summary>
    /// <param name="appId">应用ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{appId}/auto-approval")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<IActionResult> AutoApprovalAsync([FromRoute] long appId)
    {
        bool result = await _mpAppWorkflowService.AutoApprovalAsync(appId);
        return Success(result);
    }
}