using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.ReviewRecords;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.WebApi.Model.Response;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers;

[Authorize]
[ApiController]
[ApiVersion(1.0)]
[Route("v{version:apiVersion}/review-record")]
public class ReviewRecordsController : ControllerBase
{
    private readonly IReviewRecordService _reviewRecordService;

    public ReviewRecordsController(IReviewRecordService reviewRecordService)
    {
        _reviewRecordService = reviewRecordService;
    }

    /// <summary>
    /// 获取审核记录列表
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(SuccessResponse<PageModelDto<RecordResultModel>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetList([FromQuery] SearchReviewRecordDto request)
    {
        var result = await _reviewRecordService.GetAgentHistoryAsync(request);
        return Success(result);
    }
}
