using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.UserTag;
using AgentCentral.Application.Contracts.ResponseModels.UserTag;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// 用户标签控制器
    /// </summary>
    [Route("v{version:apiVersion}/user-tag")]
    [ApiController]
    [Authorize]
    public class UserTagController : ControllerBase
    {
        private readonly IUserTagService _userTagService;
        private readonly UserContext _userContext;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userTagService">用户标签服务</param>
        public UserTagController(IUserTagService userTagService, UserContext userContext)
        {
            _userTagService = userTagService;
            _userContext = userContext;
        }

        /// <summary>
        /// 创建用户标签
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateUserTag(CreateUserTagRequest request)
        {
            return Success(await _userTagService.CreateUserTagAsync(request));
        }

        /// <summary>
        /// 更新用户标签
        /// </summary>
        /// <param name="id">标签ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>操作结果</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateUserTag([FromRoute] long id, UpdateUserTagRequest request)
        {
            return Success(await _userTagService.UpdateUserTagAsync(id, request));
        }

        /// <summary>
        /// 删除用户标签
        /// </summary>
        /// <param name="id">标签ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteUserTag([FromRoute] long id)
        {
            return Success(await _userTagService.DeleteUserTagAsync(id));
        }

        /// <summary>
        /// 获取用户标签
        /// </summary>
        /// <param name="id">标签ID</param>
        /// <returns>标签信息</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<UserTagDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserTag([FromRoute] long id)
        {
            return Success(await _userTagService.GetUserTagAsync(id));
        }

        /// <summary>
        /// 分页查询用户标签
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>分页结果</returns>
        [HttpPost("search")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<UserTagDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SearchUserTags(SearchUserTagRequest request)
        {
            return Success(await _userTagService.SearchUserTagsAsync(request));
        }

        /// <summary>
        /// 保存用户标签关联
        /// </summary>
        /// <param name="request">关联请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("relation")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SaveUserTagRelation(UserTagRelationRequest request)
        {
            return Success(await _userTagService.SaveUserTagRelationAsync(request));
        }

        /// <summary>
        /// 获取用户标签列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>标签列表</returns>
        [HttpGet("list")]
        [ProducesResponseType(typeof(SuccessResponse<List<UserTagDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserTags()
        {
            return Success(await _userTagService.GetUserTagsAsync(_userContext.UserId));
        }

        /// <summary>
        /// 检查标签编码是否存在
        /// </summary>
        /// <param name="tagCode">标签编码</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        [HttpGet("check-code")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> IsTagCodeExists([FromQuery] string tagCode, [FromQuery] long? excludeId = null)
        {
            return Success(await _userTagService.IsTagCodeExistsAsync(tagCode, excludeId));
        }

        /// <summary>
        /// 获取所有标签列表（用于下拉选择）
        /// </summary>
        /// <param name="tagType">标签类型（可选）</param>
        /// <returns>标签列表</returns>
        [HttpGet("select")]
        [ProducesResponseType(typeof(SuccessResponse<List<UserTagDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllTagsForSelect([FromQuery] string tagType = null)
        {
            return Success(await _userTagService.GetAllTagsForSelectAsync(_userContext.TenantId, tagType));
        }
    }
}
