using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.Workflow;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Domain.Entities.Workflow;
using AgentCentral.WebApi.Model.Response;
using Asp.Versioning;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace AgentCentral.WebApi.Controllers;

[Authorize]
[ApiController]
[ApiVersion(1.0)]
[Route("v{version:apiVersion}/workflow-log")]
public class WorkflowController(IAppWorkflowService appWorkflowService, IMapper mapper, IWorkflowService workflowService) : ControllerBase
{
    private readonly IAppWorkflowService _appWorkflowService = appWorkflowService;
    private readonly IWorkflowService _workflowService = workflowService;
    private readonly IMapper _mapper = mapper;

    /// <summary>
    /// Get changes for template
    /// </summary>
    [ProducesResponseType<SuccessResponse<PageModelDto<WorkflowLogDto>>>((int)HttpStatusCode.OK)]
    [HttpGet("agent/{agentId}")]
    public async Task<IActionResult> GetWorkflowChangesPagesAsync(string agentId)
    {
        var result = await _appWorkflowService.GetWorkflowLogAsync(agentId);
        return Success(result);
    }
}
