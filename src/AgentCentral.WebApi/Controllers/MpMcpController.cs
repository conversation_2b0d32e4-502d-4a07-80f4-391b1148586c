using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.Dtos.MpApp;
using AgentCentral.Application.Contracts.Dtos.MpMcp;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.IServices.Workflow;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers;

/// <summary>
/// Mp_Mcp审核API控制器
/// </summary>
[Authorize]
[ApiController]
[Route("v{version:apiVersion}/mp-mcp")]
public class MpMcpController : ControllerBase
{
    private readonly IMpMcpService _mpMcpService;
    private readonly IMpMcpWorkflowService _mpMcpWorkflowService;
    private readonly DifyClient _difyClient;
    private readonly ILogger<MpMcpController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MpMcpController(
        IMpMcpService mpMcpService,
        IMpMcpWorkflowService mpMcpWorkflowService,
        DifyClient difyClient,
        ILogger<MpMcpController> logger)
    {
        _mpMcpService = mpMcpService;
        _mpMcpWorkflowService = mpMcpWorkflowService;
        _difyClient = difyClient;
        _logger = logger;
    }

    /// <summary>
    /// 获取审核MCP服务分页列表（统一接口，支持多状态查询）
    /// </summary>
    /// <param name="searchDto">搜索条件</param>
    /// <returns>分页结果</returns>
    [HttpPost("page")]
    [ProducesResponseType(typeof(SuccessResponse<PageModelDto<MpMcpListDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetPageListAsync([FromBody] MpMcpSearchPageDto searchDto)
    {
        if (searchDto.Status is { Length: <= 0 })
        {
            searchDto.Status = searchDto.ReviewStatus == 0
                ? [McpStatusEnum.AutoApproved, McpStatusEnum.AutoRejected]
                : [McpStatusEnum.Published, McpStatusEnum.Rejected, McpStatusEnum.Published, McpStatusEnum.Delisted, McpStatusEnum.Deleted];
        }
        PageModelDto<MpMcpListDto> result = await _mpMcpService.GetReviewPageListAsync(searchDto);
        return Success(result);
    }

    /// <summary>
    /// 获取MCP服务详情
    /// </summary>
    /// <param name="mcpId">MCP服务ID</param>
    /// <returns>MCP服务详情</returns>
    [HttpGet("{mcpId}")]
    [ProducesResponseType(typeof(SuccessResponse<MpMcpDetailDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetMcpDetailAsync([FromRoute] long mcpId)
    {
        MpMcpDetailDto result = await _mpMcpService.GetMcpDetailAsync(mcpId);
        return Success(result);
    }

    /// <summary>
    /// 更新MCP服务
    /// </summary>
    /// <param name="dto">请求数据</param>
    /// <param name="mcpId"></param>
    /// <returns>是否成功</returns>
    [HttpPut("{mcpId}")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateMpMcpAsync([FromRoute] long mcpId, [FromBody] UpdateMpMcpDto dto)
    {
        dto.Id = mcpId;
        var result = await _mpMcpService.UpdateMpMcpAsync(dto);
        return Success(result);
    }

    /// <summary>
    /// 批量审核通过应用
    /// </summary>
    /// <param name="batchDto">批量操作信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch/approve")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> BatchApproveAsync([FromBody] MpBatchOperationDto batchDto)
    {
        bool result = await _mpMcpWorkflowService.BatchApproveAsync(batchDto.Ids, batchDto.Remark);
        return Success(result);
    }

    /// <summary>
    /// 批量审核拒绝应用
    /// </summary>
    /// <param name="batchDto">批量操作信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch/reject")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> BatchRejectAsync([FromBody] MpBatchOperationDto batchDto)
    {
        bool result = await _mpMcpWorkflowService.BatchRejectAsync(batchDto.Ids, batchDto.Remark);
        return Success(result);
    }

    /// <summary>
    /// 批量下架应用
    /// </summary>
    /// <param name="batchDto">批量操作信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch/delist")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> BatchDelistAsync([FromBody] MpBatchOperationDto batchDto)
    {
        bool result = await _mpMcpWorkflowService.BatchDelistAsync(batchDto.Ids, batchDto.Remark);
        return Success(result);
    }

    /// <summary>
    /// 获取工作流日志
    /// </summary>
    /// <param name="mcpId">应用ID</param>
    /// <returns>工作流日志列表</returns>
    [HttpGet("{mcpId}/workflow-logs")]
    [ProducesResponseType(typeof(SuccessResponse<List<MpAppWorkflowLogDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetWorkflowLogsAsync([FromRoute] long mcpId)
    {
        var result = await _mpMcpWorkflowService.GetWorkflowLogsAsync(mcpId);
        return Success(result);
    }

    /// <summary>
    /// AI自动审批
    /// </summary>
    /// <param name="mcpId">应用ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{mcpId}/auto-approval")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<IActionResult> AutoApprovalAsync([FromRoute] long mcpId)
    {
        bool result = await _mpMcpWorkflowService.AutoApprovalAsync(mcpId);
        return Success(result);
    }

    /// <summary>
    /// 获取提供商列表
    /// </summary>
    /// <param name="keyword"></param>
    /// <returns>操作结果</returns>
    [HttpGet("providers")]
    [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<IActionResult> listProvieders([FromQuery] string keyword)
    {
        int limit = string.IsNullOrEmpty(keyword) ? 10 : 100;
        var result = await _mpMcpService.ListProvieders(keyword, 1, limit);
        return Success(result);
    }
}