using Microsoft.AspNetCore.Mvc;
using AgentCentral.Application.Contracts.IServices;
using Microsoft.AspNetCore.Authorization;
using AgentCentral.Application.Contracts.Dtos.Feedback;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.WebApi.Model.Response;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// 反馈管理
    /// </summary>
    [Route("v{version:apiVersion}/feedback")]
    [ApiController]
    [Authorize]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _feedbackService;

        public FeedbackController(IFeedbackService feedbackService)
        {
            _feedbackService = feedbackService;
        }

        /// <summary>
        /// 更新反馈状态
        /// </summary>
        [HttpPut("{id}/status")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateStatusAsync(long id, [FromBody] UpdateFeedbackStatusDto input)
        {
            await _feedbackService.UpdateStatusAsync(id, input.Status);
            return Success(true);
        }

        /// <summary>
        /// 删除反馈
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync(long id)
        {
            await _feedbackService.DeleteAsync(id);
            return Success(true);
        }

        /// <summary>
        /// 获取反馈详情
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SuccessResponse<FeedbackOutputDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAsync(long id)
        {
            var result = await _feedbackService.GetAsync(id);
            return Success(result);
        }

        /// <summary>
        /// 分页查询反馈
        /// </summary>
        [HttpGet("page")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<FeedbackOutputDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPageListAsync([FromQuery] FeedbackSearchDto input)
        {
            var result = await _feedbackService.GetPageListAsync(input);
            return Success(result);
        }
    }
}