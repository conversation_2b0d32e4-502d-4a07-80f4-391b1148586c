using AgentCentral.Application.Contracts.Dtos.CallLog;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// Call log controller
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("v{version:apiVersion}/[controller]")]
    public class CallLogController : ControllerBase
    {
        private readonly ICallLogService _callLogService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="callLogService">Call log service</param>
        public CallLogController(ICallLogService callLogService)
        {
            _callLogService = callLogService;
        }

        /// <summary>
        /// Get call support statistics
        /// </summary>
        /// <returns>Call support statistics</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(typeof(SuccessResponse<CallSupportStatisticsDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCallSupportStatistics()
        {
            var result = await _callLogService.GetCallSupportStatisticsAsync();
            return Success(result);
        }

        /// <summary>
        /// Search call logs with pagination
        /// </summary>
        /// <param name="searchParams">Search parameters</param>
        /// <returns>Page response with call log records</returns>
        [HttpPost("search")]
        [ProducesResponseType(typeof(SuccessResponse<CallLogPageDto<List<CallLogRecordDto>>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SearchCallLogs([FromBody] CallLogSearchParamsDto searchParams)
        {
            var result = await _callLogService.SearchCallLogsAsync(searchParams);
            return Success(result);
        }
    }
}