using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AgentCentral.Application.Contracts.IService.App;
using AgentCentral.WebApi.Model.Response;
using AgentCentral.Application.Contracts.ResponseModels.AppUsage;
using AgentCentral.Application.Contracts.RequestModels.AppUsage;

namespace AgentCentral.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("v{version:apiVersion}/[controller]")]
    public class AppUsageController : ControllerBase
    {
        private readonly IAppUsageService _appUsageService;

        public AppUsageController(IAppUsageService appUsageService)
        {
            _appUsageService = appUsageService;
        }

        /// <summary>
        /// 获取应用使用记录
        /// </summary>
        [HttpGet("{appId}")]
        [ProducesResponseType(typeof(SuccessResponse<AppUsageResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Get(string appId)
        {
            AppUsageResponse usage = await _appUsageService.GetByAppIdAsync(appId);
            return Success(usage);
        }

        /// <summary>
        /// 创建应用使用记录
        /// </summary>
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SuccessResponse<AppUsageResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateAppUsageRequest request)
        {
            AppUsageResponse result = await _appUsageService.CreateAsync(request);
            return Success(result);
        }

        /// <summary>
        /// 更新应用使用记录
        /// </summary>
        [HttpPut("{appId}")]
        [ProducesResponseType(typeof(SuccessResponse<AppUsageResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(string appId, [FromBody] UpdateAppUsageRequest request)
        {
            AppUsageResponse result = await _appUsageService.UpdateAsync(appId, request);
            return Success(result);
        }

        /// <summary>
        /// 删除应用使用记录
        /// </summary>
        [HttpDelete("{appId}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(string appId)
        {
            bool result = await _appUsageService.DeleteAsync(appId);
            return Success(result);
        }
    }
}