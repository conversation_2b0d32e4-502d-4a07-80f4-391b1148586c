using AgentCentral.Application.Contracts.IServices;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// Message Center Controller
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("v{version:apiVersion}/[controller]")]
    public class MessageCenterController : ControllerBase
    {
        private readonly IMessageCenterService _messageCenterService;

        public MessageCenterController(IMessageCenterService messageCenterService)
        {
            _messageCenterService = messageCenterService;
        }

        [AllowAnonymous]
        [HttpPost("send/{app_id}")]
        public async Task<IActionResult> SendAppNotificationAsync([FromRoute(Name = "app_id")] long appId)
        {
            var result = await _messageCenterService.SendAppNotificationAsync(appId);
            return Success(result);
        }
    }
}
