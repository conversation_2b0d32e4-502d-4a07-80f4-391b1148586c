using AgentCentral.Application.Contracts.Dtos.BaseDto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.UserInfo;
using AgentCentral.Application.Contracts.ResponseModels.UserInfo;
using AgentCentral.WebApi.Model.Response;
using Asp.Versioning;

namespace AgentCentral.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [ApiVersion(1.0)]
    [Route("v{version:apiVersion}/user")]
    public class UserInfoController : ControllerBase
    {
        private readonly IUserInfoService _userInfoService;

        public UserInfoController(
            IUserInfoService userInfoService)
        {
            _userInfoService = userInfoService;
        }

        /// <summary>
        /// 获取用户列表（分页）
        /// </summary>
        [HttpPost("search")]
        [ProducesResponseType(typeof(SuccessResponse<PageModelDto<UserInfoDto>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserList([FromBody] UserSearchDto searchDto)
        {
            // searchDto.TenantId = _userContext.TenantId;
            var users = await _userInfoService.GetUserListAsync(searchDto);
            return Success(users);
        }

        /// <summary>
        /// 更新用户角色
        /// </summary>
        [HttpPut("roles")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateUserRoles([FromBody] UpdateUserRolesDto updateDto)
        {
            var result = await _userInfoService.UpdateUserRolesAsync(updateDto);
            return Success(result);
        }

        /// <summary>
        /// 更新用户状态
        /// </summary>
        [HttpPut("status")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateUserStatus([FromBody] UpdateUserStatusDto request)
        {
            var result = await _userInfoService.UpdateUserStatusAsync(request.UserId, request.IsActive, string.Empty);
            return Success(result);
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        [HttpPut("update")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateUser([FromBody] UpdateUserCompleteInfoDto updateDto)
        {
            var result = await _userInfoService.UpdateUserCompleteInfoAsync(updateDto);
            return Success(result);
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        [ProducesResponseType(typeof(UserInfoResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserInfo(long? userId)
        {
            var userInfo = await _userInfoService.GetUserInfoAsync(userId);
            return Success(userInfo);
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        [HttpPut]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateUserInfo([FromBody] UpdateUserInfoRequest request)
        {
            var updatedUserInfo = await _userInfoService.UpdateUserInfoAsync(request);
            return Success(updatedUserInfo);
        }
    }
}