using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    public abstract class ControllerBase : Microsoft.AspNetCore.Mvc.ControllerBase
    {
        protected ControllerBase()
        {
        }

        protected ActionResult<SuccessResponse<T>> SuccessResult<T>(T data)
        {
            return Ok(SuccessResponse.Create(data));
        }

        protected ActionResult<SuccessResponse> SuccessResult()
        {
            return Ok(SuccessResponse.Create());
        }

        protected IActionResult Success<T>(T data)
        {
            return Ok(SuccessResponse.Create(data));
        }

        protected IActionResult Success()
        {
            return Ok(SuccessResponse.Create());
        }
    }
}