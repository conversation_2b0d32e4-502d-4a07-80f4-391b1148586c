using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Application.Contracts.RequestModels.AppReviewComment;
using AgentCentral.Application.Contracts.ResponseModels.AppReviewComment;
using Asp.Versioning;

namespace AgentCentral.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [ApiVersion(1.0)]
    [Route("v{version:apiVersion}/app-review/{appId}")]
    public class AppReviewCommentController : ControllerBase
    {
        private readonly IAppReviewCommentService _appReviewCommentService;

        public AppReviewCommentController(IAppReviewCommentService appReviewCommentService)
        {
            _appReviewCommentService = appReviewCommentService;
        }

        /// <summary>
        /// 获取应用评价列表
        /// </summary>
        [HttpGet("list")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(PageModelDto<AppReviewCommentResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetReviewComments([FromRoute] string appId,
            [FromQuery] BaseSearchPageDto searchPageDto)
        {
            var reviews = await _appReviewCommentService.GetReviewCommentsAsync(appId, searchPageDto);
            return Success(reviews);
        }

        /// <summary>
        /// 获取应用评价统计信息
        /// </summary>
        [HttpGet("stats")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(AppReviewStatsResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetReviewStats([FromRoute] string appId)
        {
            var stats = await _appReviewCommentService.GetReviewStatsAsync(appId);
            return Success(stats);
        }

        /// <summary>
        /// 创建应用评价
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateReview([FromRoute] string appId,
            [FromBody] CreateAppReviewCommentRequest request)
        {
            var review = await _appReviewCommentService.CreateReviewAsync(appId, request);
            return Success(review);
        }
    }
}