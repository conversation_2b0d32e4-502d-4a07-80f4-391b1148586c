using AgentCentral.Application.Contracts.Dtos.App;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.WebApi.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Controllers
{
    /// <summary>
    /// Application change record controller
    /// </summary>
    [Route("v{version:apiVersion}/app/change")]
    [ApiController]
    [Authorize]
    public class AppChangeController : ControllerBase
    {
        private readonly IAppChangeService _appChangeService;

        public AppChangeController(IAppChangeService appChangeService)
        {
            _appChangeService = appChangeService;
        }

        /// <summary>
        /// Get application change record by ID
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <returns>Change record details</returns>
        [HttpGet("{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<AppChangeDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAsync([FromRoute(Name = "app_id")] long appId)
        {
            return Success(await _appChangeService.GetAsync(appId));
        }

        /// <summary>
        /// Update application change record
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Update result</returns>
        [HttpPut("{app_id}")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAsync([FromRoute(Name = "app_id")] long appId, [FromBody] UpdateAppChangeDto updateDto)
        {
            return Success(await _appChangeService.UpdateAsync(appId, updateDto));
        }

        /// <summary>
        /// Update application change type
        /// </summary>
        /// <param name="appId">Dify application ID</param>
        /// <param name="updateDto">Change type data</param>
        /// <returns>Update result</returns>
        [HttpPut("{dify_app_id}/change-type")]
        [ProducesResponseType(typeof(SuccessResponse<bool>), StatusCodes.Status200OK)]
        [AllowAnonymous]
        public async Task<IActionResult> UpdateChangeTypeAsync([FromRoute(Name = "dify_app_id")] string appId, [FromBody] UpdateAppChangeTypeDto updateDto)
        {
            return Success(await _appChangeService.UpdateChangeTypeAsync(appId, updateDto));
        }
    }
}
