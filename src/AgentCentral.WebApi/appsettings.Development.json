{
  "service": {
    "name": "InfoService_1"
  },
  "ConnectionStrings": {
    "WorkId": "1",
    "AgentCentral": "Data Source=mysql01-share-rds-aliyun.item.pub;Port=3306;Initial Catalog=aihub;User ID=aihub;Pwd=****************;Pooling=true;SslMode=none;"
  },
  "Redis": {
    "ConnectionString": "redis-cluster-dev.item.pub:6379,user=aihub,password=VTgkpAOrodev,defaultDatabase=0,ssl=false,connectTimeout=4000,allowAdmin=true,abortConnect=false"
  },
  "AppName": "AgentCentral",
  "IamApis": {
    "ApiKey": "oWlmqvwjlQDwJ5_2v07mO0U_jLwmsx2uAVVRdt3IlT_METHmekZAa3S77TAMlaMyUgQqj00jbAbXzyi7nw8UQGJEe191kzt9DIbBzDprnG9YfSWS52f7XDBKje4juedBrtoR2LTsdKLL_a6-c_hJNWX1PiWJofp-b7wK8ZNnKwve80gwcqsM4Ilhex7ALCv9tBG9Bl50lzJBydmWa_FwKa5x4yXfRkmRY4aXm92JzbI0KRQxImmfoeBKkNOQ0Dbioodmww0qTyvnGOlz04MHxZ7NDynRK8YmCDn3YrgIRlDmeJV84wx182PdD5WF1P40FkPDGXpgJmPICZTh2S_FDw",
    "Issuer": "https://id-dev.item.pub",
    "ClientId": "325fa245-b086-4e70-b479-474944903f1c",
    "ClientIdV": "ba39e693-6a90-4046-84ca-a3532fedad3d",
    "ClientSecret": "a0cfca85-b2ee-4d40-b09f-c54e32d54fca",
    "ClientSecretV": "d768b8a4-30ad-495d-b217-8aae4353b77c",
    "Scope": [ "profile", "openid", "email", "user" ],
    "RedirectUri": "https://agent-dev.item.pub",
    "BaseUrl": "https://id-dev.item.pub",
    "ApiUrls": {
      "Token": "/oauth2/token",
      "Jwts": "/oauth2/jwks",
      "User": "/user-info",
      "Oauth2Token": "/oauth2/token",
      "ActivateUser": "/users/{id}/activate",
      "DeactivateUser": "/users/{id}/inactivate",
      "UpdateUser": "/users/{id}",
      "TenantInfo": "/company/list-by-codes"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "Properties": {
      "Application": "InfoService"
    },
    "WriteTo": [
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "File",
                "Args": {
                  "path": "/Logs/Error/logs.log",
                  "rollingInterval": "Day",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ],
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "@l = 'Error'",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ]
          }
        }
      },
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "File",
                "Args": {
                  "path": "/Logs/Info/logs.log",
                  "rollingInterval": "Day",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ],
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "@l = 'Information'",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ]
          }
        }
      },
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "Console",
                "Args": { "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}" }
              }
            ]
          }
        }
      }
    ]
  },
  //允许跨域的s
  "CorsUrls": [
    "localhost", //本地可跨域
    "192.168.*", //公司所有内网可跨域
    "*unisco.com",
    "*item.com",
    "*logisticsteam.com"
  ],
  "Global": {
    "BlobStoreType": "OSS"
  },
  "BlobStore": {
    "AccessKeyId": "LTAI5t9tjALH5r4axHe1v58F",
    "SecretAccessKey": "******************************",
    "Region": "us-west-2",
    "EndPoint": "oss-cn-shanghai.aliyuncs.com",
    "ProfileName": "aihub",
    "Bucket": "share-item"
  },
  "ResourceSync": {
    "Enabled": false,
    "BatchSize": 50,
    "CronExpression": "0 5,19 * * *"
  },
  "ResourceApis": {
    "BaseUrl": "https://prod-139.westus.logic.azure.com:443",
    "ApiUrls": {
      "Sync": "/workflows/40ee555f11ec4b4face1d8e9b00ac8b3/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=1MlzxfCGJ05aZZ6rMItGQUUDGPT2DjI4OByBmnkPw78"
    }
  },
  "ClockApis": {
    "BaseUrl": "https://clockapi.logisticsteam.com:9088",
    "Token": "test001",
    "ApiUrls": {
      "GetEmployee": "api/Employee/Employee_GetbyEmployeeID"
    }
  },
  "HrmApis": {
    "BaseUrl": "https://hrm-dev.item.pub",
    "ApiUrls": {
      "EmployeeDetail": "/hrm/EmpEmployee/v1/detail",
      "Companies": "/hrm/EmpEmployee/v1/companies",
      "Departments": "/hrm/EmpEmployee/v1/departments"
    }
  },
  "DifyApis": {
    "BaseUrl": "https://aiop-dev.item.pub",
    "ApiUrls": {
      "CreateApp": "/console/api/apps",
      "CopyApp": "/console/api/apps/{app_id}/copy",
      "DeleteApp": "/console/api/apps/{app_id}",
      "UpdateApp": "/console/api/apps/{app_id}",
      "CreateTag": "/console/api/tags",
      "UpdateTag": "/console/api/tags/",
      "DeleteTag": "/console/api/tags/",
      "RemoveTagBinding": "/console/api/tag-bindings/remove",
      "CreateTagBinding": "/console/api/tag-bindings/create",
      "AutoReview": "/inner/api/auto-review"
    },
    "AgentUrls": [
      {
        "Name": "GenerateAppComments",
        "Url": "https://aiop-test.item.com/v1/chat-messages",
        "ApiKey": "app-ZmH5rnXN5ZghY6qRuX3SPuHI"
      }
    ],
    "workflows": {
      "Url" : "https://aiagent.item.com/v1",
      "RunUrl": "/workflows/run",
      "MpAppAutoReviewApiKey": "app-hsgp8qQnEYDQVZWYPkRNQPyK",
      "MpMcpAutoReviewApiKey": "app-hsgp8qQnEYDQVZWYPkRNQPyK",
      "MpAppPolicyApiKey": "app-YyGd3u6ml4969LhikDhcgtZS"
    },
  },
  "CrmApis": {
    "BaseUrl": "https://crm-dev.item.pub",
    "ApiUrls": {
      "CustomerPage": "/crm/foundation-data/v1"
    }
  },
  "McpApis": {
    "Authorization": "",
    "BaseUrl": "https://agent-dev.item.pub",
    "ApiUrls": {
      "Marketplace": "/linker-mcp/api/linker-mcp-marketplace/app-api/marketplace",
      "UpdateMcpServer": "/linker-mcp/api/linker-mcp-marketplace/app-api/my-mcp-server/${service_name}"
    }
  },
  "FileConversionConfig": {
    "Secret": "secret_PJk4Iu2KrhRaRfFy",
    "ConvertFileExt": [ "ppt", "pptx", "xls", "xlsx", "doc", "docx" ],
    "ChangeFileExt": [ "csv", "json", "xml" ],
    "UploadFileExt": [ "ppt", "pptx", "xls", "xlsx", "doc", "docx", "json", "xml", "csv", "txt", "mp3", "mp4", "pdf", "md", ".markdown", "png", "jpg", "jpeg", "gif", "avi", "mov", "apk", "exe", "html", "tiff", "bmp", "heic" ],
    "ConvertSettints": [
      {
        "SrcFileExt": "ppt",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "pptx",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "xls",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "xlsx",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "doc",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "docx",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      }
    ],
    "ChangeExtSettings": [
      {
        "SrcFileExt": "csv",
        "DstFileExt": "md",
        "DstContentType": "text/markdown"
      },
      {
        "SrcFileExt": "json",
        "DstFileExt": "txt",
        "DstContentType": "text/plain"
      },
      {
        "SrcFileExt": "xml",
        "DstFileExt": "txt",
        "DstContentType": "text/plain"
      }
    ]
  },
  "RoutePrefix": "api",
  "CentralConfig": {
    "Issuer": "https://id-dev.item.pub",
    "Authority": "https://id-dev.item.pub",
    "RequireHttpsMetadata": false,
    "ValidateIssuerSigningKey": true,
    "Audience": "363cf191-c13e-4556-9449-e244167f3cc3",
    "ClientId": "363cf191-c13e-4556-9449-e244167f3cc3",
    "ClientSecret": "6e5ebe4d-8af9-49d2-a5ef-e44a996ea6d7"
  },
  "JWT": {
    "ValidateIssuer": true,
    "ValidIssuer": "https://agentcentral-dev.item.pub/",
    "ValidateIssuerSigningKey": true,
    "IssuerSigningKey": "iCEq6b3crhF9P793aTG0L7Vr1R5kRuKK",
    "ValidateAudience": true,
    "ValidAudience": "agentcentral",
    "ValidateLifetime": true,
    "RequireExpirationTime": true,
    "ClockSkew": 60,
    "Expire": 1440,
    "RefreshTokenExpire": 2880
  },
  "WorkflowApis": {
    "BaseUrl": "https://wfe-dev.item.pub",
    "AppSecret": "********************************",
    "GrantType": "client_credential",
    "ProjectCode": "AGENT",
    "ApiUrls": {
      "GetProjectToken": "/api/wf/auth/getProjectToken",
      "Start": "/api/wf/process/task/operation/start",
      "Complete": "/api/wf/process/task/operation/complete"
    }
  },
  "AgentCentralApis": {
    "BaseUrl": "https://agentcentral-dev.item.pub",
    "ApiUrls": {
      "AutoReview": "/api/v1/App/auto-approve/",
      "PrivacyPolicyReview": "/api/v1/mp-app-review/{0}/privacy-policy-review/"
    }
  },
  "CallLogApiUrls": {
    "BaseUrl": "https://aiop-dev.item.pub",
    "ApiUrls": {
      "CallLogStats": "/smart-call-admin/api/call-logs/stats",
      "CallLogPage": "/smart-call-admin/api/call-logs/page"
    }
  },
  "MessageCenterApiUrls": {
    "BaseUrl": "https://mc-dev.item.pub",
    "ReviewEmail": "<EMAIL>",
    "ReviewEmailJobCode": "875714cbd7bac3be3917da160cf27586",
    "AgentCentralUrl": "https://agentcentral-dev.item.pub",
    "ApiUrls": {
      "Send": "/message/api/v1/common/send"
    }
  }
}