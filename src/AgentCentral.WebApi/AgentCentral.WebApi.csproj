<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <UserSecretsId>cb21d122-a568-4ed1-b976-2c74b6e2c51c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.5" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="..\.dockerignore" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="..\.dockerignore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AgentCentral.Application\AgentCentral.Application.csproj" />
    <ProjectReference Include="..\AgentCentral.Infrastructure\AgentCentral.Infrastructure.csproj" />
    <ProjectReference Include="..\AgentCentral.SqlSugarDB\AgentCentral.SqlSugarDB.csproj" />
  </ItemGroup>

</Project>
