using AgentCentral.Domain.Shared.Models;
using SqlSugar;

namespace AgentCentral.WebApi.Extensions
{
    public static class DataBaseExtensions
    {
        public static void DataExecuting(this IServiceProvider serviceProvider, object oldValue, DataFilterModel entityInfo)
        {
            if (entityInfo.OperationType is not DataFilterType.InsertByObject and not DataFilterType.UpdateByObject)
            {
                return;
            }
            if (entityInfo.EntityValue is not Domain.Entities.Base.BaseEntity baseEntity)
            {
                return;
            }
            IHttpContextAccessor httpContextAccessor = serviceProvider.GetService<IHttpContextAccessor>();
            UserContext? userContext = httpContextAccessor?.HttpContext?.RequestServices
                .GetService<UserContext>();

            if (userContext == null)
            {
                return;
            }

            // 处理不同的操作类型
            switch (entityInfo.OperationType)
            {
                case DataFilterType.InsertByObject:
                    if (entityInfo.PropertyName == nameof(baseEntity.TenantId) && string.IsNullOrEmpty(baseEntity.TenantId))
                    {
                        entityInfo.SetValue(userContext.TenantId ?? "");
                    }
                    else if (entityInfo.PropertyName == nameof(baseEntity.CreateBy) && baseEntity.CreateBy <= 0 && userContext.UserId > 0)
                    {
                        entityInfo.SetValue(userContext.UserId);
                    }
                    else if (entityInfo.PropertyName == nameof(baseEntity.CreateName) && string.IsNullOrEmpty(baseEntity.CreateName) && !string.IsNullOrEmpty(userContext.UserName))
                    {
                        entityInfo.SetValue(userContext.UserName);
                    }
                    break;
                case DataFilterType.UpdateByObject:
                    if (entityInfo.PropertyName == nameof(baseEntity.UpdateBy) && baseEntity.UpdateBy <= 0 && userContext.UserId > 0)
                    {
                        entityInfo.SetValue(userContext.UserId);
                    }
                    else if (entityInfo.PropertyName == nameof(baseEntity.UpdateName) && string.IsNullOrEmpty(baseEntity.UpdateName) && !string.IsNullOrEmpty(userContext.UserName))
                    {
                        entityInfo.SetValue(userContext.UserName);
                    }
                    break;
            }
        }
    }
}
