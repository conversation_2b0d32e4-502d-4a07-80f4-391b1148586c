using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc;

namespace AgentCentral.WebApi.Extensions
{
    public static class RoutePrefixExtensions
    {
        private static string _prefix = string.Empty;

        public static void UseRoutePrefix(this MvcOptions opts, string prefix)
        {
            opts.Conventions.Add(new RoutePrefixConvention(new RouteAttribute(prefix)));
            _prefix = prefix;
        }

        public class RoutePrefixConvention : IApplicationModelConvention
        {
            private readonly AttributeRouteModel _routePrefix;

            public RoutePrefixConvention(IRouteTemplateProvider route)
            {
                _routePrefix = new AttributeRouteModel(route);
            }

            public void Apply(ApplicationModel application)
            {
                foreach (var selector in application.Controllers.SelectMany(c => c.Selectors))
                {
                    if (selector.AttributeRouteModel != null)
                    {
                        selector.AttributeRouteModel = AttributeRouteModel.CombineAttributeRouteModel(_routePrefix, selector.AttributeRouteModel);
                    }
                    else
                    {
                        selector.AttributeRouteModel = _routePrefix;
                    }
                }
                var actionSelector = application.Controllers.SelectMany(c => c.Actions).SelectMany(a => a.Selectors);
                foreach (var selector in actionSelector)
                {
                    if (selector.AttributeRouteModel != null && selector.AttributeRouteModel.IsAbsoluteTemplate)
                    {
                        var template = selector.AttributeRouteModel.Template;
                        selector.AttributeRouteModel.Template = $"/{_prefix}{template}";
                    }
                }
            }
        }
    }
}
