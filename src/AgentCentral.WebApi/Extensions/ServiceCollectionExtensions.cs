using AgentCentral.Domain.IRepository;
using AgentCentral.Infrastructure;
using System.Reflection;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Domain.Shared.Enums;
using Item.BlobProvider.Extensions;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Domain.Manager;

namespace AgentCentral.WebApi.Extensions
{
    public static partial class ServiceCollectionExtensions
    {
        public static IServiceCollection AddDIService(this IServiceCollection services,
            IEnumerable<Assembly> assemblies, IConfiguration configuration)
        {
            _ = services.Scan(scan => scan.FromAssemblies(assemblies)
                .AddClasses(classes => classes.AssignableTo<ITransientService>()).AsImplementedInterfaces()
                .WithTransientLifetime()
                .AddClasses(classes => classes.AssignableTo<IScopedService>()).AsImplementedInterfaces()
                .WithScopedLifetime()
                .AddClasses(classes => classes.AssignableTo<ISingletonService>()).AsImplementedInterfaces()
                .WithSingletonLifetime()
                .AddClasses(classes => classes.AssignableTo<ManagerBase>()).AsSelf().WithTransientLifetime());
            _ = services.AddScoped<UserContext>();
            _ = services.AddTransient(typeof(Lazy<>), typeof(Lazier<>));
            _ = services.Scan(scan => scan.FromAssemblies(assemblies)
                .AddClasses(classes => classes.AssignableTo(typeof(IBaseRepository<>))).AsImplementedInterfaces()
                .WithScopedLifetime());


            #region BlobStore

            var globalConfigOptions = configuration.GetSection("Global").Get<GlobalConfigOptions>();

            var accessKeyId = configuration["BlobStore:AccessKeyId"]!;
            var secretAccessKey = configuration["BlobStore:SecretAccessKey"]!;
            var region = configuration["BlobStore:Region"];
            var profileName = configuration["BlobStore:ProfileName"]!;
            var bucketName = configuration["BlobStore:Bucket"]!;
            var endPoint = configuration["BlobStore:EndPoint"];

            services.AddBlobProvider(() => new BlobProviderConfiguration
            {
                ProviderType = globalConfigOptions.BlobStoreType switch
                {
                    AttachmentStoreTypeEnum.Oss => BlobProviderType.OSS,
                    AttachmentStoreTypeEnum.Aws => BlobProviderType.AWS,
                    _ => BlobProviderType.AWS
                },
                AccessKeyId = accessKeyId,
                AccessKeySecret = secretAccessKey,
                Region = region ?? string.Empty,
                ContainerName = bucketName,
                ProfileName = profileName,
                EndPoint = endPoint ?? string.Empty
            }, null, null);

            #endregion BlobStore

            return services;
        }
    }

    public class Lazier<T> : Lazy<T>
    {
        public Lazier(IServiceProvider serviceProvider)
            : base(serviceProvider.GetRequiredService<T>)
        {
        }
    }
}