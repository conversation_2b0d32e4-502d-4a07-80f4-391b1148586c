using AgentCentral.Application.Contracts;
using AgentCentral.Domain.Shared.Models;
using Item.Internal.Auth.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Text.Encodings.Web;

namespace AgentCentral.WebApi.Authentication
{
    public class AgentJwtBearerAuthenticationHandler : AbstractJwtBearerAuthenticationHandler
    {
        private readonly ITokenValidatedInfoService _tokenValidatedInfoService;
        private readonly IConfiguration _configuration;

        public AgentJwtBearerAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            IOptionsSnapshot<JwtOptions> jwtOptions,
            ITokenValidatedInfoService tokenValidatedInfoService, IConfiguration configuration) : base(options, logger,
            encoder, jwtOptions)
        {
            _tokenValidatedInfoService = tokenValidatedInfoService;
            _configuration = configuration;
        }

        /// <summary>
        /// Token失效验证(退出、修改密码等,Token未过期已失效)
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        protected override async Task<bool> ValidatedTokenInfoAsync(string userId, string version)
        {
            return await Task.FromResult(true);
        }

        /// <summary>
        /// Token验证成功时执行
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected override async Task OnTokenValidated(JwtBearerTokenValidatedContext context)
        {
            var userContext = context.HttpContext.RequestServices.GetService<UserContext>() ??
                              throw new NullReferenceException(nameof(UserContext));
            var principal = context.Principal ?? throw new NullReferenceException(nameof(context.Principal));
            var claims = principal.Claims;
            userContext.UserId = long.Parse(claims.First(x => x.Type == "user_id").Value);
            userContext.UserName = claims.First(x => x.Type == "user_name").Value;

            await Task.CompletedTask;
        }
    }
}
