using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.Infrastructure.Redis;
using AgentCentral.Infrastructure.Redis.Enum;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Newtonsoft.Json.Linq;

namespace AgentCentral.WebApi.Authentication;

/// <summary>
/// 
/// </summary>
public static class TokenValidatedHandler
{
    public const string ADD_USER_LOCK_KEY_PREFIX = "user:add:";

    private const int MAX_RETRY_COUNT = 3;

    private const int RETRY_DELAY = 200;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="context"></param>
    /// <exception cref="NullReferenceException"></exception>
    public static async Task OnTokenValidated(TokenValidatedContext context)
    {
        await Task.CompletedTask;
        var principal = context.Principal ?? throw new NullReferenceException(nameof(context.Principal));
        var claims = principal.Claims.ToList();
        var grantType = claims.First(x => x.Type == "grant_type")?.Value;
        var userContext = context.HttpContext.RequestServices.GetService<UserContext>() ??
                          throw new NullReferenceException(nameof(UserContext));
        var dataClaim = JObject.Parse(claims.First(x => x.Type == "data").Value);
        var userId = dataClaim["user_id"]?.Value<long>() ?? throw new AgentCentralException(ErrorCodeEnum.LoginFail, "Authentication fail.");
        var userName = dataClaim["user_name"]?.Value<string>() ?? throw new AgentCentralException(ErrorCodeEnum.LoginFail, "Authentication fail.");
        var userRepository = context.HttpContext.RequestServices.GetService<IUserInfoRepository>();
        var redisService = context.HttpContext.RequestServices.GetService<IRedisService>();

        var addUserLockKey = $"{ADD_USER_LOCK_KEY_PREFIX}{userId}";
        int retryCount = 0;
        while (retryCount < MAX_RETRY_COUNT)
        {
            if (await redisService.LockAsync(RedisDataType.Token, addUserLockKey, 10))
            {
                try
                {
                    var user = await userRepository.GetFirstAsync(u => u.UserId == userId && u.UserType == UserTypeEnum.Central);
                    if (user == null)
                    {
                        await userRepository.InsertAsync(new Def_User_Info
                        {
                            UserType = UserTypeEnum.Central,
                            UserId = userId,
                            UserName = userName,
                            UserProfile = string.Empty,
                            TenantId = string.Empty,
                            RegisterTime = DateTime.UtcNow
                        });
                    }
                    break;
                }
                finally
                {
                    await redisService.UnLockAsync(RedisDataType.Token, addUserLockKey);
                }
            }
            retryCount++;
            await Task.Delay(RETRY_DELAY);
        }

        try
        {
            switch (grantType)
            {
                case "authorization_code":
                case "refresh_token":
                case "password":
                    userContext.UserId = userId;
                    userContext.UserName = userName;
                    userContext.UserType = UserTypeEnum.Central;
                    break;
                case "client_credentials":
                    userContext.UserId = 0L;
                    userContext.UserName = "System";
                    break;
            }
        }
        catch (Exception ex)
        {
            context.Fail(ex.Message);
        }
    }
}