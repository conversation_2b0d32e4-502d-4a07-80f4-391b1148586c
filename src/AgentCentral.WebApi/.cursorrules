# 角色
你是一名WebApi专家

# 职责
你的职责是定义和实现WebApi

# 规范
## 基础规范
- 可以调用AgentCentral.Application定义的业务模型
- 可以调用AgentCentral.Application.Contracts定义的业务接口
- AgentCentral.Infrastructuree是基础设施，有需要基础设施的调用都调用AgentCentral.Infrastructure中的方法
- AgentCentral.Domain.Shared是领域模型共享部分，有需要领域模型共享部分的可以使用AgentCentral.Domain.Shared

## 控制器规范
- 所有控制器必须继承BaseController
- 所有控制器必须使用[ApiController]特性
- 所有控制器必须使用[ApiVersion("1.0")]特性
- 所有控制器必须使用英文注释

## 路由规范
- 所有控制器必须使用[Route("{module}/v{version:apiVersion}/[controller]")]
- 路由使用kebab-case命名风格
- 参数使用camelCase命名风格

## 方法规范
- 所有方法必须使用[ProducesResponseType(typeof(SuccessResponse<T>), StatusCodes.Status200OK)]
- 所有方法必须返回Success(result)
- 所有方法必须使用英文注释
- 所有方法必须使用适当的HTTP方法特性([HttpGet], [HttpPost], [HttpPut], [HttpDelete])

## 命名规范
- 控制器名称必须使用PascalCase并以Controller结尾
- 方法名称必须使用PascalCase
- 参数名称必须使用camelCase

## 注释规范
- 所有注释必须使用英文
- 控制器必须包含类注释
- 方法必须包含方法注释和参数注释
- 返回值必须包含注释说明

## 依赖注入规范
- 使用构造函数注入
- 所有依赖必须声明为private readonly
- 依赖变量名必须以下划线开头

## 错误处理规范
- 使用统一的错误处理机制
- 返回标准的错误响应格式