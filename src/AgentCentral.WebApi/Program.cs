using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Options;
using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Infrastructure;
using AgentCentral.Infrastructure.JsonConverts;
using AgentCentral.Infrastructure.OpenApi.Extensions;
using AgentCentral.Infrastructure.RabbitMq.Extensions;
using AgentCentral.Infrastructure.Redis.Extensions;
using AgentCentral.SqlSugarDB;
using AgentCentral.WebApi.Authentication;
using AgentCentral.WebApi.Extensions;
using AgentCentral.WebApi.Middleware;
using Asp.Versioning;
using Item.Common.Lib.Common;
using Item.Internal.ChangeLog.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using Serilog.Filters;
using SqlSugar;
using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Reflection;
using System.Text.RegularExpressions;

namespace AgentCentral.WebApi
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // #AI Copilot: 创建Web应用程序构建器
            WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

            // #AI Copilot: 创建配置构建器并加载配置文件
            IConfigurationRoot configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", true, true)
                .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", true, true)
                .AddJsonFile("knowledge_base_process.json", true, true)
                .AddEnvironmentVariables()
                .Build();

            builder.Services.AddControllers(options =>
            {
                options.UseRoutePrefix(configuration.GetSection("RoutePrefix").Value ?? "");
            });

            // #AI Copilot: 创建Serilog日志记录器
            Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
                .Filter.ByExcluding(Matching.WithProperty<string>("RequestPath", path => path.StartsWith("/api/health-check")))
                .CreateLogger();

            // #AI Copilot: 加载所有Parking.*.dll程序集
            List<Assembly> assemblies = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory, "AgentCentral.*.dll").Select(Assembly.LoadFrom).ToList();

            // #AI Copilot: 配置主机使用Serilog
            builder.Host.UseSerilog();

            //启用本地化，支持多语言
            builder.Services.AddLocalization();

            // #AI Copilot: 添加HttpContextAccessor服务
            builder.Services.AddHttpContextAccessor();

            // #AI Copilot: 添加选项服务并加载配置
            builder.Services.AddOptions(configuration);

            // 添加API版本控制服务
            builder.Services.AddApiVersioning(options =>
            {
                options.DefaultApiVersion = new ApiVersion(1, 0);
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.ReportApiVersions = true;
            }).AddApiExplorer(options =>
            {
                options.GroupNameFormat = "'v'VVV";
                options.SubstituteApiVersionInUrl = true;
            });

            #region 设置允许跨域

            //获取配置文件中的允许跨域的地址
            IEnumerable<string> cors = configuration.GetSection("CorsUrls").Get<string[]>().Select(p => p.Replace("*", "[^/]*"));
            // #AI Copilot: 添加跨域策略
            builder.Services.AddCors(options => options.AddPolicy("CorsPolicy",
                builder =>
                {
                    //允许任何 HTTP 方法的跨域请求，例如 POST, PUT, DELETE
                    builder.AllowAnyMethod()
                           //允许跨域请求包含任何 HTTP 头,例如 Accept，Accept-Language，Content-Language 和 Content-Type
                           .AllowAnyHeader()
                           //只允许指定的来源跨域
                           .SetIsOriginAllowed(origin => Uri.TryCreate(origin, UriKind.Absolute, out Uri uriResult) && cors.Any(host => Regex.IsMatch(uriResult.Host, host)))
                           //允许响应被前端的 XMLHttpRequest 或 Fetch API 在进行跨域请求时，携带认证信息（例如 cookies 和 HTTP 认证方案的证书）
                           .AllowCredentials();
                }));

            #endregion 设置允许跨域

            // Add services to the container.

            // #AI Copilot: 添加控制器服务并配置NewtonsoftJson和Api行为选项
            builder.Services.AddControllers().AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new CustomContractResolver(new HttpContextAccessor());
            }).ConfigureApiBehaviorOptions(options =>
            {
                options.InvalidModelStateResponseFactory = context =>
                {
                    var errors = context.ModelState
                        .Where(e => e.Value?.Errors.Count > 0)
                        .Select(e => new
                        {
                            Name = e.Key,
                            Message = e.Value?.Errors.First().ErrorMessage
                        }).ToList();

                    return new BadRequestObjectResult(new { Code = (int)HttpStatusCode.BadRequest, Message = errors.Select(e => e.Message), Status = "error" });
                };
            });

            // #AI Copilot: 添加Swagger/OpenAPI支持
            builder.Services.AddEndpointsApiExplorer();

            builder.Services.AddSwaggerGenExt();

            builder.Services.AddHttpLogging(logging =>
            {
                logging.LoggingFields = HttpLoggingFields.All;
            });

            #region JWT认证

            //鉴权
            // #AI Copilot: 添加JWT认证服务
            var itemIamConfig = configuration.GetSection("CentralConfig").Get<CentralOptions>();
            builder.Services.AddAuthentication(options =>
            {
                JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.Authority = itemIamConfig?.Authority;
                options.RequireHttpsMetadata = itemIamConfig.RequireHttpsMetadata;
                options.Audience = itemIamConfig.Audience;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidIssuer = itemIamConfig.Issuer,
                    ValidateAudience = true,
                    ValidateIssuerSigningKey = itemIamConfig.ValidateIssuerSigningKey
                };
                options.Events = new JwtBearerEvents
                {
                    OnTokenValidated = TokenValidatedHandler.OnTokenValidated
                };
            });
            //builder.Services.AddAuthentication(options =>
            //{
            //    JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
            //    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            //})
            //.AddScheme<AuthenticationSchemeOptions, AgentJwtBearerAuthenticationHandler>(
            //    JwtBearerDefaults.AuthenticationScheme, null);

            #endregion JWT认证

            #region 注册SqlSugar

            //注册SqlSugar
            // #AI Copilot: 添加SqlSugar服务
            builder.Services.Configure<WorkIdOptions>(configuration.GetSection("WorkIdSetting"));
            builder.Services.AddSingleton<SqlSugarWorkIdProvider>();
            builder.Services.AddSingleton<ISqlSugarClient>(s =>
            {
                var workIdProvider = s.GetRequiredService<SqlSugarWorkIdProvider>();
                SnowFlakeSingle.WorkId = workIdProvider.GetWorkIdAsync().GetAwaiter().GetResult();
                Log.Information($"SnowFlakeSingle.WorkId:{SnowFlakeSingle.WorkId}");
                SqlSugarScope Db = new(
                [
                    new ConnectionConfig()
                    {
                        ConfigId = "AgentCentral",
                        DbType = DbType.MySql,
                        ConnectionString = configuration.GetConnectionString("AgentCentral"),
                        IsAutoCloseConnection = true,
                        InitKeyType = InitKeyType.Attribute,
                        ConfigureExternalServices = new ConfigureExternalServices()
                        {
                            //EntityService = (x,p) =>
                            //{
                            //    p.DbColumnName = UtilMethods.ToUnderLine(p.DbColumnName);
                            //}
                        },
                        MoreSettings = new ConnMoreSettings()
                        {
                            IsWithNoLockQuery = false,
                            IsAutoRemoveDataCache = true
                        }
                    }
                 ],
                 db =>
                 {
                     db.Aop.DataExecuting = (oldValue, entityInfo) =>
                     {
                         s.DataExecuting(oldValue, entityInfo);
                     };
#if DEBUG
                     db.Aop.OnLogExecuting = (sql, pars) =>
                     {
                         string log = $"[db aihub SQL]{UtilMethods.GetSqlString(DbType.MySql, sql, pars)}\n";
                         Console.WriteLine(log);
                         Debug.WriteLine("[db aihub SQL] ====== Start\n");
                         Debug.WriteLine(UtilMethods.GetSqlString(DbType.MySql, sql, pars));
                         Debug.WriteLine("[db aihub SQL] ====== End");
                     };
#endif
                 });
                Db.QueryFilter.AddTableFilter<BaseEntity>(m => m.IsActive == true);
                return Db;
            });

            #endregion 注册SqlSugar

            // #AI Copilot: 添加AutoMapper服务
            builder.Services.AddAutoMapper(assemblies);

            // #AI Copilot: 添加Redis服务
            builder.Services.AddRedis(configuration.GetSection("Redis"));

            // #AI Copilot: 添加RabbitMq服务
            builder.Services.AddRabbitMq(configuration.GetSection("RabbitMq"));

            // #AI Copilot: 添加服务
            builder.Services.AddDIService(assemblies, configuration);

            // #AI Copilot: 添加客户端服务
            builder.Services.AddClient(configuration);

            // #AI Copilot: 添加内存缓存服务
            builder.Services.AddMemoryCache();

            builder.Services
                .AddChangeLog(assemblies.Where(a => a.GetName().Name == "AgentCentral.Domain"))
                .UseSqlSugar(config =>
                {
                    config.UseChangeLogDB<AgentCentralDatabase>();
                });

            // #AI Copilot: 构建应用程序
            WebApplication app = builder.Build();

            // app.AddDBExecution(configuration);

            // Configure the HTTP request pipeline.
            //if (app.Environment.IsDevelopment())
            //{
            // #AI Copilot: 配置Swagger扩展
            app.UseSwaggerExt(configuration);
            //}
            // #AI Copilot: 使用HTTP日志记录
            app.UseHttpLogging();

            //跨域
            // #AI Copilot: 使用跨域策略
            app.UseCors("CorsPolicy");

            // #AI Copilot: 使用静态文件和文件服务器
            app.UseStaticFiles();
            app.UseFileServer();
            // #AI Copilot: 使用全局异常处理中间件
            app.UseExceptionHandler(GlobalExceptionMiddleware.Use);

            // #AI Copilot: 使用认证和授权
            app.UseAuthentication();//鉴权
            app.UseAuthorization();//授权

            // 注册自定义的本地化语言中间件
            app.UseMiddleware<RequestLocalizationByHeaderMiddleware>();

            // #AI Copilot: 映射控制器路由
            app.MapControllers();

            app.UseSqlSugarDifferLogEvent().UseChangeLog((config) =>
            {
                config.GetUserFunc = (provider) =>
                {
                    var userContext = provider.GetRequiredService<UserContext>();
                    return new LogUserModel()
                    {
                        TenantId = userContext.TenantId ?? string.Empty,
                        UserId = userContext.UserId,
                        UserName = userContext.UserName ?? string.Empty
                    };
                };
                config.IgnoreFieldList = config.IgnoreFieldList.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Select(v => v.ToCamelCase()).ToList() ?? new List<string>()
                );
                config.CustomerIdMapping = config.CustomerIdMapping.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.ToCamelCase()
                    );
            });

            // #AI Copilot: 记录信息日志
            Log.Information("AgentCentral.WebApi.Running");
            // #AI Copilot: 运行应用程序
            app.Run();
        }
    }
}