#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER root
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
ARG NUGET_SOURCE="https://api.nuget.org/v3/index.json"
RUN dotnet nuget remove source 'nuget.org' \
 && dotnet nuget add source --protocol-version 3 "$NUGET_SOURCE"  
ARG NUGET_SOURCE2
RUN [ ! -n "$NUGET_SOURCE2" ] || dotnet nuget add source --protocol-version 3 "$NUGET_SOURCE2" 
WORKDIR /src
COPY ["AgentCentral.WebApi/AgentCentral.WebApi.csproj", "AgentCentral.WebApi/"]
RUN dotnet restore "./AgentCentral.WebApi/./AgentCentral.WebApi.csproj"
COPY . .
WORKDIR "/src/AgentCentral.WebApi"
RUN dotnet build "./AgentCentral.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./AgentCentral.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
USER root
RUN apt-get update && apt-get -y install libfontconfig1
RUN apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
USER root
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AgentCentral.WebApi.dll"]