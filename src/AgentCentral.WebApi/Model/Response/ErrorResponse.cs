namespace AgentCentral.WebApi.Model.Response
{
    public class ErrorResponse : ResponseBase
    {
        public ErrorResponse(int code) : this(code, "")
        {
        }

        public ErrorResponse(int code, string message)
        {
            Status = "error";
            Code = code;
            Message = message;
        }

        public static ErrorResponse Create(int code, string message)
        {
            return new ErrorResponse(code, message);
        }

        public static ErrorResponse Create(int code)
        {
            return new ErrorResponse(code);
        }
    }
}