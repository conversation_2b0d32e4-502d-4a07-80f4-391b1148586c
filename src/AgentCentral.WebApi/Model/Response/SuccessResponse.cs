namespace AgentCentral.WebApi.Model.Response
{
    public class SuccessResponse<T> : ResponseBase
    {
        public T Data { get; set; }

        public SuccessResponse()
        {
            Status = "success";
            Code = 0;
            Message = "";
        }

        public SuccessResponse(T data) : this()
        {
            Data = data;
        }
    }

    public class SuccessResponse : ResponseBase
    {
        public SuccessResponse()
        {
            Status = "success";
            Code = 0;
            Message = "";
        }

        public static SuccessResponse<T> Create<T>(T data)
        {
            return new SuccessResponse<T>(data);
        }

        public static SuccessResponse Create()
        {
            return new SuccessResponse();
        }
    }
}