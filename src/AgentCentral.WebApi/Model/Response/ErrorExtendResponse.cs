namespace AgentCentral.WebApi.Model.Response
{
    public class ErrorExtendResponse : ResponseBase
    {
        public object Data { get; set; }

        public ErrorExtendResponse(int code, string message, object data)
        {
            Status = "error";
            Code = code;
            Message = message;
            Data = data;
        }

        public static ErrorExtendResponse Create(int code, string message, object data)
        {
            return new ErrorExtendResponse(code, message, data);
        }
    }
}