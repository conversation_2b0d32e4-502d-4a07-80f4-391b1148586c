{
  "service": {
    "name": "InfoService_1"
  },
  "ConnectionStrings": {
    "WorkId": "1",
    "AgentCentral": "Data Source=172.21.3.11;Port=6033;Initial Catalog=aihub;User ID=user_aihub;Pwd=****************;Pooling=true;SslMode=none;"
  },
  "Redis": {
    "ConnectionString": "redis-cluster.item-public.svc:6379,user=aihub,password=VTgkpAOrodEe8vo9,defaultDatabase=0,ssl=false,connectTimeout=4000,allowAdmin=true,abortConnect=false"
  },
  "AppName": "AgentCentral",
  "IamApis": {
    "ApiKey": "oWlmqvwjlQDwJ5_2v07mO0U_jLwmsx2uAVVRdt3IlT_METHmekZAa3S77TAMlaMyUgQqj00jbAbXzyi7nw8UQGJEe191kzt9DIbBzDprnG9YfSWS52f7XDBKje4juedBrtoR2LTsdKLL_a6-c_hJNWX1PiWJofp-b7wK8ZNnKwve80gwcqsM4Ilhex7ALCv9tBG9Bl50lzJBydmWa_FwKa5x4yXfRkmRY4aXm92JzbI0KRQxImmfoeBKkNOQ0Dbioodmww0qTyvnGOlz04MHxZ7NDynRK8YmCDn3YrgIRlDmeJV84wx182PdD5WF1P40FkPDGXpgJmPICZTh2S_FDw",
    "Issuer": "https://id.item.com",
    "ClientId": "5810d2ff-8c0b-4eb6-a196-8ede43cd8f72",
    "ClientIdV": "0192be15-4ae7-733f-9f3c-260c95e4948d",
    "ClientSecret": "1b264037-b4c7-4ea2-ba75-2ca5593eb00b",
    "ClientSecretV": "0192be15-9e28-7dd1-8a8f-bf2dd4e7bd80",
    "Scope": [ "profile", "openid", "email" ],
    "RedirectUri": "https://agent.item.com",
    "BaseUrl": "https://id.item.com",
    "ApiUrls": {
      "Token": "/oauth2/token",
      "Jwts": "/oauth2/jwks",
      "User": "/user-info",
      "Oauth2Token": "/oauth2/token",
      "ActivateUser": "/users/{id}/activate",
      "DeactivateUser": "/users/{id}/inactivate",
      "UpdateUser": "/users/{id}",
      "TenantInfo": "/company/list-by-codes"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "Properties": {
      "Application": "InfoService"
    },
    "WriteTo": [
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "File",
                "Args": {
                  "path": "/Logs/Error/logs.log",
                  "rollingInterval": "Day",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ],
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "@l = 'Error'",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ]
          }
        }
      },
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "File",
                "Args": {
                  "path": "/Logs/Info/logs.log",
                  "rollingInterval": "Day",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ],
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "@l = 'Information'",
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ]
          }
        }
      },
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "Console",
                "Args": {
                  "outputTemplate": "|-[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Application}] [{Level:u3}] [{TraceId}] - {Message:l}{Exception}{NewLine}"
                }
              }
            ]
          }
        }
      }
    ]
  },
  //允许跨域的s
  "CorsUrls": [
    "localhost",
    //本地可跨域
    "192.168.*",
    //公司所有内网可跨域
    "*unisco.com",
    "*item.com",
    "*logisticsteam.com"
  ],
  "Global": {
    "BlobStoreType": "AWS"
  },
  "BlobStore": {
    "AccessKeyId": "********************",
    "SecretAccessKey": "Jjmjp39T4DYQVgprTjApX5i+Kg5hRKzDo1LkE9ME",
    "Region": "us-west-2",
    "ProfileName": "aihub",
    "Bucket": "item-prod-share-bucket"
  },
  "ResourceSync": {
    "Enabled": true,
    "BatchSize": 300,
    "CronExpression": "0 3,13 * * *"
  },
  "ResourceApis": {
    "BaseUrl": "https://prod-153.westus.logic.azure.com:443",
    "ApiUrls": {
      "Sync": "/workflows/41844f985d3c4f9588cc0f6b659b67eb/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=52kNGP-Amir33CXedC40VPveC3GBlX-7-epmU0-2jFw"
    }
  },
  "ClockApis": {
    "BaseUrl": "https://clockapi.logisticsteam.com:9090",
    "Token": "AGENT0B891B2FACA88B8139D238320C",
    "ApiUrls": {
      "GetEmployee": "api/Employee/Employee_GetbyEmployeeID"
    }
  },
  "HrmApis": {
    "BaseUrl": "https://hrm.item.com",
    "ApiUrls": {
      "EmployeeDetail": "/hrm/EmpEmployee/v1/detail",
      "Companies": "/hrm/EmpEmployee/v1/companies",
      "Departments": "/hrm/EmpEmployee/v1/departments"
    }
  },
  "DifyApis": {
    "BaseUrl": "https://aiop-apidbg-prod.item.com",
    "ApiUrls": {
      "CreateApp": "/console/api/apps",
      "CopyApp": "/console/api/apps/{app_id}/copy",
      "DeleteApp": "/console/api/apps/{app_id}",
      "UpdateApp": "/console/api/apps/{app_id}",
      "CreateTag": "/console/api/tags",
      "UpdateTag": "/console/api/tags/",
      "DeleteTag": "/console/api/tags/",
      "RemoveTagBinding": "/console/api/tag-bindings/remove",
      "CreateTagBinding": "/console/api/tag-bindings/create",
      "AutoReview": "/inner/api/auto-review"
    },
    "AgentUrls": [
      {
        "Name": "GenerateAppComments",
        "Url": "https://aiop-test.item.com/v1/chat-messages",
        "ApiKey": "app-ZmH5rnXN5ZghY6qRuX3SPuHI"
      }
    ],
    "workflows": {
      "Url" : "https://aiagent.item.com/v1",
      "RunUrl": "/workflows/run",
      "MpAppAutoReviewApiKey": "app-hsgp8qQnEYDQVZWYPkRNQPyK",
      "MpMcpAutoReviewApiKey": "app-hsgp8qQnEYDQVZWYPkRNQPyK",
      "MpAppPolicyApiKey": "app-YyGd3u6ml4969LhikDhcgtZS"
    }
  },
  "CrmApis": {
    "BaseUrl": "https://crm.item.com",
    "ApiUrls": {
      "CustomerPage": "/crm/foundation-data/v1"
    }
  },
  "McpApis": {
    "Authorization": "",
    "BaseUrl": "https://marketplace.item.com",
    "ApiUrls": {
      "Marketplace": "/linker-mcp/api/linker-mcp-marketplace/app-api/marketplace",
      "UpdateMcpServer": "/linker-mcp/api/linker-mcp-marketplace/app-api/my-mcp-server/{service_name}"
    }
  },
  "FileConversionConfig": {
    "Secret": "secret_PJk4Iu2KrhRaRfFy",
    "ConvertFileExt": [ "ppt", "pptx", "xls", "xlsx", "doc", "docx" ],
    "ChangeFileExt": [ "csv", "json", "xml" ],
    "UploadFileExt": [ "ppt", "pptx", "xls", "xlsx", "doc", "docx", "json", "xml", "csv", "txt", "mp3", "mp4", "pdf", "md", ".markdown", "png", "jpg", "jpeg", "gif", "avi", "mov", "apk", "exe", "html", "tiff", "bmp", "heic" ],
    "ConvertSettints": [
      {
        "SrcFileExt": "ppt",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "pptx",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "xls",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "xlsx",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "doc",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      },
      {
        "SrcFileExt": "docx",
        "DstFileExt": "pdf",
        "DstContentType": "application/pdf"
      }
    ],
    "ChangeExtSettings": [
      {
        "SrcFileExt": "csv",
        "DstFileExt": "md",
        "DstContentType": "text/markdown"
      },
      {
        "SrcFileExt": "json",
        "DstFileExt": "txt",
        "DstContentType": "text/plain"
      },
      {
        "SrcFileExt": "xml",
        "DstFileExt": "txt",
        "DstContentType": "text/plain"
      }
    ]
  },
  "RoutePrefix": "api",
  "CentralConfig": {
    "Issuer": "https://id.item.com",
    "Authority": "https://id.item.com",
    "RequireHttpsMetadata": false,
    "ValidateIssuerSigningKey": true,
    "Audience": "db82a3f1-71b6-4d6a-9365-0f824f560bdb",
    "ClientId": "db82a3f1-71b6-4d6a-9365-0f824f560bdb",
    "ClientSecret": "cb796d0c-39ee-4a9e-8cd4-e35fc8c0bdea"
  },
  "JWT": {
    "ValidateIssuer": true,
    "ValidIssuer": "https://agentcentral.item.com/",
    "ValidateIssuerSigningKey": true,
    "IssuerSigningKey": "iCEq6b3crhF9P793aTG0L7Vr1R5kRuKK",
    "ValidateAudience": true,
    "ValidAudience": "agentcentral",
    "ValidateLifetime": true,
    "RequireExpirationTime": true,
    "ClockSkew": 60,
    "Expire": 1440,
    "RefreshTokenExpire": 2880
  },
  "WorkflowApis": {
    "BaseUrl": "https://wfe.item.com",
    "AppSecret": "********************************",
    "GrantType": "client_credential",
    "ProjectCode": "AGENT",
    "ApiUrls": {
      "GetProjectToken": "/api/wf/auth/getProjectToken",
      "Start": "/api/wf/process/task/operation/start",
      "Complete": "/api/wf/process/task/operation/complete"
    }
  },
  "AgentCentralApis": {
    "BaseUrl": "https://agentcentral.item.com",
    "ApiUrls": {
      "AutoReview": "/api/v1/App/auto-approve/",
      "PrivacyPolicyReview": "/api/v1/mp-app-review/{0}/privacy-policy-review/"
    }
  },
  "CallLogApiUrls": {
    "BaseUrl": "https://aiop-prod.item.com",
    "ApiUrls": {
      "CallLogStats": "/smart-call-admin/api/call-logs/stats",
      "CallLogPage": "/smart-call-admin/api/call-logs/page"
    }
  },
  "MessageCenterApiUrls": {
    "BaseUrl": "https://mc.item.com",
    "ReviewEmail": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
    "ReviewEmailJobCode": "26ed96335ef8337dc01a3aee6c562a60",
    "AgentCentralUrl": "https://agentcentral.item.com",
    "ApiUrls": {
      "Send": "/message/api/v1/common/send"
    }
  }
}