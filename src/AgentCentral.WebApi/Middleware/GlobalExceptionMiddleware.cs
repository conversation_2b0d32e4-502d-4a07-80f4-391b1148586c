using Microsoft.AspNetCore.Diagnostics;
using Serilog;
using System.Net;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.WebApi.Model.Response;

namespace AgentCentral.WebApi.Middleware
{
    public static class GlobalExceptionMiddleware
    {
        public static void Use(this IApplicationBuilder builder)
        {
            builder.Run(async context =>
            {
                context.Response.StatusCode = 200;
                context.Response.ContentType = "application/json";

                IExceptionHandlerFeature exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
                if (exceptionHandlerFeature != null)
                {
                    Exception ex = exceptionHandlerFeature.Error;
                    if (exceptionHandlerFeature.Error is AgentCentralException)
                    {
                        //if (((FMSException)ex).Code == ErrorCodeEnum.AuthenticationFail)
                        //{
                        //    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                        //}

                        await context.Response.WriteAsJsonAsync(ErrorResponse.Create((int)((AgentCentralException)ex).Code, ((AgentCentralException)ex).Message));
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(context.Items["CustomNoAccess"] as string))
                        {
                            //在自定义权限认证下权限不通过时将会走此方法
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            /*  状态码解释：
                             •401 Unauthorized：这个状态码表示客户端在尝试访问需要身份验证的资源时，请求没有提供有效的认证信息，或者提供的认证信息不被服务器接受。在返回401状态码的响应中，服务器通常会包含一个WWW-Authenticate头，指示客户端如何进行认证。
                             •403 Forbidden：这个状态码表示客户端虽然已经通过了身份验证，但是并没有足够的权限来访问请求的资源。换句话说，服务器已经理解了请求，但是拒绝执行它。与401状态码不同，对于403状态码，服务器通常不会提供进一步的认证方式。
                            */
                            await context.Response.WriteAsJsonAsync(ErrorResponse.Create((int)ErrorCodeEnum.AuthenticationFail, "You do not have permission to access this resource"));
                        }
                        else
                        {
                            await context.Response.WriteAsJsonAsync(ErrorResponse.Create((int)ErrorCodeEnum.SystemError, "system error"));
                        }
                    }

                    Log.Error("", ex);
                }
            });
        }
    }
}