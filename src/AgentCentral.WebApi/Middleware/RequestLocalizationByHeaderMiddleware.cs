using System.Globalization;
using System.Net.Http.Headers;

namespace AgentCentral.WebApi.Middleware
{
    public class RequestLocalizationByHeaderMiddleware
    {
        private readonly string DefultLanguage = "en-US";
        private readonly RequestDelegate _next;

        public RequestLocalizationByHeaderMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                string lang = GetLanguageFromHeaders(context.Request.Headers);
                SetCulture(lang);
            }
            catch (Exception)
            {
                SetCulture(DefultLanguage);
            }

            await _next(context);
        }

        private string GetLanguageFromHeaders(IHeaderDictionary headers)
        {
            string lang = headers["locale"].FirstOrDefault();
            if (string.IsNullOrWhiteSpace(lang))
            {
                lang = headers["Accept-Language"].FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(lang))
                {
                    lang = ParseAcceptLanguageHeader(lang);
                }
            }
            return lang ?? DefultLanguage;
        }

        private void SetCulture(string lang)
        {
            CultureInfo defaultCulture = new(lang);
            CultureInfo.CurrentCulture = defaultCulture;
            CultureInfo.CurrentUICulture = defaultCulture;
        }

        private string ParseAcceptLanguageHeader(string acceptLanguageHeader)
        {
            List<string> languages = acceptLanguageHeader.Split(',')
                .Select(StringWithQualityHeaderValue.Parse)
                .OrderByDescending(s => s.Quality.GetValueOrDefault(1))
                .Select(s => s.Value)
                .ToList();

            return languages.FirstOrDefault();
        }
    }
}