using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 用户角色关联数据访问接口
    /// </summary>
    public interface IUserRoleRepository : IBaseRepository<Def_User_Role>
    {
        /// <summary>
        /// 获取用户的所有角色ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色ID列表</returns>
        Task<List<long>> GetUserRoleIdsAsync(long userId);

        /// <summary>
        /// 获取用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户角色关联列表</returns>
        Task<List<Def_User_Role>> GetUserRolesAsync(long userId);

        /// <summary>
        /// 批量保存用户角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleIds">角色ID列表</param>
        /// <param name="departmentId">部门ID</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveUserRolesAsync(long userId, List<long> roleIds, long departmentId, long createBy, string createName);

        /// <summary>
        /// 批量保存用户角色，每个角色使用其对应的部门ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleWithDepartments">角色ID及其对应的部门ID字典</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveUserRolesWithDifferentDepartmentsAsync(long userId, Dictionary<long, long> roleWithDepartments, long createBy, string createName);

        /// <summary>
        /// 批量保存用户角色，每个角色使用其对应的部门ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleWithDepartments">角色ID及其对应的部门ID字典</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveUserRolesWithDifferentDepartmentsAsync(long userId, Dictionary<long, long> roleWithDepartments, long createBy, string createName, string tenantId, List<long> departmentIds = null);

        /// <summary>
        /// 删除用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteUserRolesAsync(long userId, string tenantId);

        /// <summary>
        /// 检查用户是否拥有指定角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否拥有角色</returns>
        Task<bool> HasRoleAsync(long userId, long roleId);

        /// <summary>
        /// 获取具有指定角色的所有用户ID
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户ID列表</returns>
        Task<List<long>> GetUserIdsByRoleAsync(long roleId);
    }
}
