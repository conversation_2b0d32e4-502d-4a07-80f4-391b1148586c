using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// Mp_App Repository接口
    /// </summary>
    public interface IMpAppRepository : IBaseRepository<Mp_App>, IScopedService
    {
        /// <summary>
        /// 获取Mp_App审核分页列表
        /// </summary>
        /// <param name="expression">查询条件表达式</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="orderBy">排序字段</param>
        /// <param name="isAsc">是否升序</param>
        /// <returns>分页结果</returns>
        Task<(List<Mp_App> items, int total)> GetReviewPageListAsync(
            Expression<Func<Mp_App, bool>> expression, 
            int pageIndex, 
            int pageSize, 
            string orderBy, 
            bool isAsc);

        /// <summary>
        /// 批量更新Mp_App状态
        /// </summary>
        /// <param name="appIds">应用ID列表</param>
        /// <param name="status">目标状态</param>
        /// <param name="updateBy">更新人ID</param>
        /// <param name="updateName">更新人姓名</param>
        /// <returns>更新的记录数</returns>
        Task<int> BatchUpdateStatusAsync(
            List<long> appIds, 
            MpAppStatusEnum status, 
            long updateBy, 
            string updateName);

        /// <summary>
        /// 根据应用ID获取Mp_App详情
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>Mp_App实体</returns>
        Task<Mp_App> GetByAppIdAsync(long appId);
    }
} 