using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 应用参数值仓储接口
    /// </summary>
    public interface IAppParamValueRepository : IBaseRepository<Def_App_Param_Value>
    {
        /// <summary>
        /// 获取应用的所有参数值
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>参数值列表</returns>
        Task<List<Def_App_Param_Value>> GetAppParamValuesAsync(long appId);

        /// <summary>
        /// 根据参数键获取参数值
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="paramKey">参数键</param>
        /// <returns>参数值</returns>
        Task<Def_App_Param_Value> GetParamValueByKeyAsync(long appId, string paramKey);
    }
}
