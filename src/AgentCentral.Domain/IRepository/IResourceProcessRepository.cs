using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;
using System.Linq.Expressions;

namespace AgentCentral.Domain.IRepository;

public interface IResourceProcessRepository : IBaseRepository<Doc_Resource_Process>
{
    Task<List<ProcessAttachmentModel>> GetProcessAttachments(CancellationToken cancellationToken);

    Task<(List<ResourceProcessListModel>, int)> GetResourcePageListAsync(Expression<Func<Doc_Resource_Process, Doc_Resource_People, bool>> where, int pageSize, int pageIndex, string orderBy, bool isAsc);
}