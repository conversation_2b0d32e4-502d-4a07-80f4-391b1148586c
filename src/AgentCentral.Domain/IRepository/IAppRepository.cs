using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.IRepository;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Domain.Shared.Models;

public interface IAppRepository : IBaseRepository<Def_App>
{
    /// <summary>
    /// 获取应用分页列表（包含使用统计）
    /// </summary>
    Task<(List<Def_App> list, int total)> GetPageListWithStatsAsync(
        Expression<Func<Def_App, bool>> expression,
        int pageIndex,
        int pageSize,
        string orderByField,
        bool isAsc,
        IList<long> tagIds,
        AppQueryModeEnum queryMode = AppQueryModeEnum.Public,
        Dictionary<string, List<string>>? departments = null);

    Task<Def_App> GetAppDetailByAppId(string appId);

    /// <summary>
    /// 获取应用审核分页列表
    /// </summary>
    Task<(List<AppReviewListModel> list, int total)> GetReviewPageListAsync(
        int pageIndex,
        int pageSize,
        string orderByField,
        bool isAsc,
        string agent<PERSON><PERSON>,
        string creator,
        string company,
        string department,
        List<long> tagIds,
        AppStatusEnum? status,
        AppPermissionEnum? permission,
        string reviewer,
        string appMode,
        bool? guestMode,
        int reviewStatus);

    Task<(List<AppListModel> list, int total)> GetPlatformPageListAsync(
        Expression<Func<Def_App, bool>> expression,
        int pageIndex,
        int pageSize,
        string orderByField,
        bool isAsc,
        IList<long> tagIds,
        bool queryUser = false
        );
}