using AgentCentral.Domain.Entities.Workflow;

namespace AgentCentral.Domain.IRepository.Workflow
{
    /// <summary>
    /// 工作流信息仓储接口
    /// </summary>
    public interface IWorkflowInfoRepository : IBaseRepository<WorkflowInfo>
    {
        /// <summary>
        /// 根据业务ID列表批量获取工作流信息列表
        /// </summary>
        /// <param name="businessIds">业务ID列表</param>
        /// <returns>工作流信息列表</returns>
        Task<List<WorkflowInfo>> GetWorkflowInfosByBusinessIdsAsync(List<long> businessIds);
    }
}