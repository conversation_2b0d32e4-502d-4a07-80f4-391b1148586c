using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 用户标签仓储接口
    /// </summary>
    public interface IUserTagRepository : IBaseRepository<Def_User_Tag>
    {
        /// <summary>
        /// 获取用户的所有标签ID
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>标签ID列表</returns>
        Task<List<long>> GetUserTagIdsAsync(long userId);

        /// <summary>
        /// 获取用户的所有标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>标签列表</returns>
        Task<List<Def_User_Tag>> GetUserTagsAsync(long userId);

        Task<Dictionary<long, List<Def_User_Tag>>> GetUserTagsAsync(List<long> userIds);

        /// <summary>
        /// 批量保存用户标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tagIds">标签ID列表</param>
        /// <param name="createBy">创建人ID</param>
        /// <param name="createName">创建人姓名</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveUserTagsAsync(long userId, List<long> tagIds, long createBy, string tenantId, string createName);

        /// <summary>
        /// 删除用户的所有标签
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteUserTagsAsync(long userId);

        /// <summary>
        /// 检查标签编码是否已存在
        /// </summary>
        /// <param name="tagCode">标签编码</param>
        /// <param name="excludeId">排除的标签ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsTagCodeExistsAsync(string tagCode, long? excludeId = null);
    }
}
