using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;

namespace AgentCentral.Domain.IRepository
{
    public interface IAppReviewCommentRepository : IBaseRepository<Def_App_Review_Comment>
    {
        /// <summary>
        /// 获取应用评价列表
        /// </summary>
        Task<(List<ReviewCommentModel>, int)> GetReviewCommentsAsync(string appId, int? ratingStar, int pageIndex, int pageSize,
            string orderBy, bool isAsc, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取应用评价统计信息
        /// </summary>
        /// <returns>(平均评分, 评价总数, 各星级评价数量)</returns>
        Task<(double avgRating, int totalCount, Dictionary<int, int> ratingDistribution)> GetReviewStatsAsync(
            string appId, CancellationToken cancellationToken = default);
    }
}