using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;

namespace AgentCentral.Domain.IRepository
{
    public interface IAppSubscribeRepository : IBaseRepository<Def_App_Subscribe>
    {
        /// <summary>
        /// 获取应用订阅用户列表
        /// </summary>
        Task<(List<SubscribeUsersModel>, int)> GetSubscribeUsersAsync(string appId, int pageIndex, int pageSize, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取应用订阅总数
        /// </summary>
        Task<int> GetSubscribeCountAsync(string appId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取用户订阅的应用列表
        /// </summary>
        Task<(List<AppListModel> list, int total)> GetUserSubscribedAppsAsync(
            Expression<Func<Def_App_Subscribe, Def_App, bool>> filterExpression,
            int pageIndex,
            int pageSize,
            string orderByField,
            bool isAsc,
            IList<long> tagIds,
            CancellationToken cancellationToken = default);
    }
}