using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 应用参数定义仓储接口
    /// </summary>
    public interface IAppParamRepository : IBaseRepository<Def_App_Param>
    {
        /// <summary>
        /// 获取应用的所有参数定义
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>参数定义列表</returns>
        Task<List<Def_App_Param>> GetAppParamsAsync(long appId);

        /// <summary>
        /// 根据参数键获取参数定义
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="paramKey">参数键</param>
        /// <returns>参数定义</returns>
        Task<Def_App_Param> GetParamByKeyAsync(long appId, string paramKey);
    }
}
