using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Domain.IRepository;

public interface IUserTypeRelationRepository : IBaseRepository<Def_User_Type_Relation>
{
    /// <summary>
    /// 获取用户的类型
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户类型</returns>
    Task<UserTypeEnum?> GetUserTypeAsync(long userId);

    /// <summary>
    /// 保存用户类型
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="userType">用户类型</param>
    /// <param name="createBy">创建人ID</param>
    /// <param name="createName">创建人姓名</param>
    /// <returns>是否成功</returns>
    Task<bool> SaveUserTypeAsync(long userId, UserTypeEnum userType, long createBy, string createName);

    /// <summary>
    /// 根据类型获取用户列表
    /// </summary>
    /// <param name="userType">用户类型</param>
    /// <returns>用户ID列表</returns>
    Task<List<long>> GetUsersByTypeAsync(UserTypeEnum userType);

    /// <summary>
    /// 删除用户的类型关联
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>是否成功</returns>
    Task<bool> DeleteUserTypeAsync(long userId);
}