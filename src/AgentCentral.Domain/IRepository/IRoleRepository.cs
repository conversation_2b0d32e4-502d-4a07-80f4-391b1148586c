using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 角色数据访问接口
    /// </summary>
    public interface IRoleRepository : IBaseRepository<Def_Role>
    {
        /// <summary>
        /// 获取部门下的所有角色
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>角色列表</returns>
        Task<List<Def_Role>> GetRolesByDepartmentAsync(long departmentId, string tenantId);

        /// <summary>
        /// 检查角色编码是否已存在
        /// </summary>
        /// <param name="roleName">角色编码</param>
        /// <param name="departmentId">部门ID</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsRoleCodeExistsAsync(string tenantId, string roleName, long? departmentId = null, long? excludeId = null);

        /// <summary>
        /// 根据角色编码获取角色
        /// </summary>
        /// <param name="roleCodes">角色编码列表</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>角色列表</returns>
        Task<List<Def_Role>> GetRolesByCodeAsync(List<string> roleCodes, string tenantId);
    }
}
