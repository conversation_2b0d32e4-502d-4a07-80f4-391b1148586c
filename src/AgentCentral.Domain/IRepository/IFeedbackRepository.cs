using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;
using System.Linq.Expressions;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 反馈仓储接口
    /// </summary>
    public interface IFeedbackRepository : IBaseRepository<Def_Feedback>
    {
        Task<(List<FeedbackModel>, int)> GetFeedbackPageList(Expression<Func<Def_Feedback, Def_User_Info, bool>> where, int pageIndex, int pageSize);
    }
}