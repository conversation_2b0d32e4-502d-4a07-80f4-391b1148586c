using System.Linq.Expressions;
using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Infrastructure;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// Mp_Mcp Repository接口
    /// </summary>
    public interface IMpMcpRepository : IBaseRepository<Mp_Mcp>, IScopedService
    {
        /// <summary>
        /// 获取Mp_Mcp审核分页列表
        /// </summary>
        /// <param name="expression">查询条件表达式</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="orderBy">排序字段</param>
        /// <param name="isAsc">是否升序</param>
        /// <returns>分页结果</returns>
        Task<(List<Mp_Mcp> items, int total)> GetReviewPageListAsync(
            Expression<Func<Mp_Mcp, bool>> expression, 
            int pageIndex, 
            int pageSize, 
            string orderBy, 
            bool isAsc);

        /// <summary>
        /// 批量更新Mp_Mcp状态
        /// </summary>
        /// <param name="mcpIds">MCP服务ID列表</param>
        /// <param name="status">目标状态</param>
        /// <param name="updateBy">更新人ID</param>
        /// <param name="updateName">更新人姓名</param>
        /// <returns>更新的记录数</returns>
        Task<int> BatchUpdateStatusAsync(
            List<long> mcpIds, 
            McpStatusEnum status,
            long updateBy, 
            string updateName);

        /// <summary>
        /// 根据MCP服务ID获取Mp_Mcp详情
        /// </summary>
        /// <param name="mcpId">MCP服务ID</param>
        /// <returns>Mp_Mcp实体</returns>
        Task<Mp_Mcp> GetByMcpIdAsync(long mcpId);

        /// <summary>
        /// 根据DI平台的MCP ID获取Mp_Mcp详情
        /// </summary>
        /// <param name="diMcpId">DI平台的MCP ID</param>
        /// <returns>Mp_Mcp实体</returns>
        Task<Mp_Mcp> GetByDiMcpIdAsync(long diMcpId);

        Task<List<string>> ListProvieders(string keyword, int pageNumber = 1, int pageSize = 10);
    }
} 