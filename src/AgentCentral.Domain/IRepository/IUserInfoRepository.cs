using System.Linq.Expressions;
using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    public interface IUserInfoRepository : IBaseRepository<Def_User_Info>
    {
        /// <summary>
        /// 获取用户详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<Def_User_Info> GetUserDetailByUserIdAsync(long userId);
        
        /// <summary>
        /// 获取用户信息列表
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="roleIds">角色ID列表，支持多选</param>
        /// <param name="tagIds">标签ID列表，支持多选</param>
        /// <returns>用户信息列表和总数</returns>
        Task<(List<Def_User_Info> users, int total)> GetUserInfoListAsync(
            Expression<Func<Def_User_Info, bool>> filter,
            int pageIndex,
            int pageSize,
            List<string> roleIds = null,
            List<long> tagIds = null);
        
        /// <summary>
        /// 获取用户详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<Def_User_Info> GetUserDetailAsync(long userId);
        
        /// <summary>
        /// 更新用户状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserStatusAsync(long userId, bool isActive);

        /// <summary>
        /// 检查邮箱是否已存在
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="excludeUserId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsEmailExistsAsync(string email, long? excludeUserId = null);
    }
}