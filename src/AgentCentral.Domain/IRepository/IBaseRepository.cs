using System.Linq.Expressions;
using SqlSugar;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 数据仓储公共接口
    /// </summary>
    public interface IBaseRepository<T> where T : class, new()
    {
        #region 新增

        /// <summary>
        /// 插入数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool Insert(T insertObj);

        /// <summary>
        /// 插入或更新数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool InsertOrUpdate(T data);

        /// <summary>
        /// 插入或更新数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        bool InsertOrUpdate(List<T> datas);

        /// <summary>
        /// 插入数据并返回自增id
        /// </summary>
        /// <returns>返回值</returns>
        int InsertReturnIdentity(T insertObj);

        /// <summary>
        /// 插入数据并返回bigInt自增id
        /// </summary>
        /// <returns>返回值</returns>
        long InsertReturnBigIdentity(T insertObj);

        /// <summary>
        /// 插入数据并返回雪花id
        /// </summary>
        /// <returns>返回值</returns>
        long InsertReturnSnowflakeId(T insertObj);

        /// <summary>
        /// 插入数据列表并返回雪花id列表
        /// </summary>
        /// <returns>返回值</returns>
        List<long> InsertReturnSnowflakeId(List<T> insertObjs);

        /// <summary>
        /// 插入数据并返回实体
        /// </summary>
        /// <returns>返回值</returns>
        T InsertReturnEntity(T insertObj);

        /// <summary>
        /// 插入数据数组
        /// </summary>
        /// <returns>是否成功</returns>
        bool InsertRange(T[] insertObjs);

        /// <summary>
        /// 插入数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        bool InsertRange(List<T> insertObjs);

        /// <summary>
        /// 插入数据并返回雪花id（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<long> InsertReturnSnowflakeIdAsync(T insertObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 插入数据并返回SnowflakeId（异步）
        /// </summary>
        /// <returns>SnowflakeId列表</returns>
        Task<List<long>> InsertReturnSnowflakeIdAsync(List<T> insertObjs, CancellationToken cancellationToken = default);

        /// <summary>
        /// 插入或更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> InsertOrUpdateAsync(T data, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量插入或更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> InsertOrUpdateAsync(List<T> datas, CancellationToken cancellationToken = default);

        /// <summary>
        /// 插入数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> InsertAsync(T insertObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 插入数据并返回Identity（异步）
        /// </summary>
        /// <returns>Identity</returns>
        Task<int> InsertReturnIdentityAsync(T insertObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 插入数据并返回BigIdentity（异步）
        /// </summary>
        /// <returns>BigIdentity</returns>
        Task<long> InsertReturnBigIdentityAsync(T insertObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 插入数据并返回实体（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<T> InsertReturnEntityAsync(T insertObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量插入数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> InsertRangeAsync(T[] insertObjs, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量插入数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> InsertRangeAsync(List<T> insertObjs, CancellationToken cancellationToken = default);

        #endregion 新增

        #region 修改

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool Update(T updateObj);

        /// <summary>
        /// 更新数据数组
        /// </summary>
        /// <returns>是否成功</returns>
        bool UpdateRange(T[] updateObjs);

        /// <summary>
        /// 更新数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        bool UpdateRange(List<T> updateObjs);

        /// <summary>
        /// 根据条件更新数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool Update(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据条件更新数据并设置列为true
        /// </summary>
        /// <returns>是否成功</returns>
        bool UpdateSetColumnsTrue(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> UpdateAsync(T updateObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> UpdateRangeAsync(T[] updateObjs, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> UpdateRangeAsync(List<T> updateObjs, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件更新数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> UpdateAsync(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件更新数据并设置列为True（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> UpdateSetColumnsTrueAsync(Expression<Func<T, T>> columns, Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        Task<bool> UpdateColumnsRangeAsync(List<T> updateObjs, Expression<Func<T, object>> columns, CancellationToken cancellationToken = default);

        #endregion 修改

        #region 查询

        /// <summary>
        /// 根据id获取数据
        /// </summary>
        /// <returns>返回值</returns>
        T GetById(object id);

        /// <summary>
        /// 得到所有数据
        /// </summary>
        /// <returns>返回值</returns>
        List<T> GetList();

        /// <summary>
        /// 根据条件获取数据列表
        /// </summary>
        /// <returns>返回值</returns>
        List<T> GetList(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据条件获取单个数据
        /// </summary>
        /// <returns>返回值</returns>
        T GetSingle(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据条件获取第一个数据
        /// </summary>
        /// <returns>返回值</returns>
        T GetFirst(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据id获取数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<T> GetByIdAsync(object id, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取所有数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<List<T>> GetListAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件获取数据列表（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件获取排序的数据列表（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression, string orderPropertyName, bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null);

        /// <summary>
        /// 根据条件获取单个数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<T> GetSingleAsync(Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件获取第一个数据（异步）
        /// </summary>
        /// <returns>返回值</returns>
        Task<T> GetFirstAsync(Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据排序条件和查询条件获取第一个数据（异步）
        /// </summary>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="order">排序：desc为倒序，其他为正序</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<T> GetFirstAsync(Expression<Func<T, bool>> whereExpression, Expression<Func<T, object>> orderByExpression, string order, CancellationToken cancellationToken = default);

        Task<TResult> GetSumAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> sumByExpression, CancellationToken cancellationToken = default);

        Task<List<TResult>> GetSumByGroupAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, object>> groupExpression, Expression<Func<T, TResult>> selectExpression, CancellationToken cancellationToken = default);

        Task<TResult> GetMinAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression, CancellationToken cancellationToken = default);

        Task<TResult> GetMaxAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression, CancellationToken cancellationToken = default);

        #endregion 查询

        #region 分页查询

        /// <summary>
        /// 分页, 可排序
        /// </summary>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="TotalCount">总条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <returns>结果列表</returns>
        List<T> GetPageList(Expression<Func<T, bool>> whereExpression, int pageIndex, int pageSize, out int TotalCount, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null);

        /// <summary>
        /// 分页(异步), 可排序,可取消操作
        /// </summary>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <param name="cancellationToken">可取消操作</param>
        /// <returns>(结果列表, 总条数)</returns>
        Task<(List<T>, int)> GetPageListAsync(Expression<Func<T, bool>> whereExpression, int pageIndex, int pageSize, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default);

        Task<(List<T>, int)> GetPageListAsync(Expression<Func<T, bool>> whereExpression, int pageIndex, int pageSize, string orderPropertyName, bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 分页, 可排序
        /// </summary>
        /// <param name="whereExpressionList">多条件动态联合查询（适合有多个可选查询项的查询场景）</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="TotalCount">总条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <returns>结果列表</returns>
        List<T> GetPageList(List<Expression<Func<T, bool>>> whereExpressionList, int pageIndex, int pageSize, out int TotalCount, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null);

        /// <summary>
        /// 分页(异步), 可排序,可取消操作
        /// </summary>
        /// <param name="whereExpressionList">多条件动态联合查询（适合有多个可选查询项的查询场景）</param>
        /// <param name="pageIndex">页数</param>
        /// <param name="pageSize">每页条数</param>
        /// <param name="orderByExpression">排序条件</param>
        /// <param name="isAsc">排序类型（true:正序，false倒序）</param>
        /// <param name="selectedColumnExpression">指定只查询某几列的数据</param>
        /// <param name="cancellationToken">可取消操作</param>
        /// <returns>(结果列表, 总条数)</returns>
        Task<(List<T>, int)> GetPageListAsync(List<Expression<Func<T, bool>>> whereExpressionList, int pageIndex, int pageSize, Expression<Func<T, object>> orderByExpression = null, bool isAsc = true, Expression<Func<T, T>> selectedColumnExpression = null, CancellationToken cancellationToken = default);

        #endregion 分页查询

        #region 删除

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool Delete(T deleteObj);

        /// <summary>
        /// 删除数据列表
        /// </summary>
        /// <returns>是否成功</returns>
        bool Delete(List<T> deleteObjs);

        /// <summary>
        /// 根据条件删除数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool Delete(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据id删除数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool DeleteById(object id);

        /// <summary>
        /// 删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(T deleteObj, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(List<T> deleteObjs, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据id删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> DeleteByIdAsync(object id, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据id集合删除数据（异步）
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> DeleteByIdsAsync(dynamic[] ids, CancellationToken cancellationToken = default);

        #endregion 删除

        #region 是否存在

        /// <summary>
        /// 根据条件判断是否存在数据
        /// </summary>
        /// <returns>返回值</returns>
        bool IsAny(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据条件判断是否存在数据（异步）
        /// </summary>
        /// <returns>是否存在</returns>
        Task<bool> IsAnyAsync(Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        #endregion 是否存在

        #region 计算数据量

        /// <summary>
        /// 根据条件计算数据数量
        /// </summary>
        /// <returns>返回值</returns>
        int Count(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据条件计算数据数量（异步）
        /// </summary>
        /// <returns>数量</returns>
        Task<int> CountAsync(Expression<Func<T, bool>> whereExpression, CancellationToken cancellationToken = default);

        #endregion 计算数据量

        #region 事务

        /// <summary>
        /// 开始事务
        /// </summary>
        void BeginTran();

        /// <summary>
        /// 提交事务
        /// </summary>
        void CommitTran();

        /// <summary>
        /// 回滚事务
        /// </summary>
        void RollbackTran();

        /// <summary>
        /// 执行事务
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>是否执行成功</returns>
        bool UseTran(Action action);

        /// <summary>
        /// 执行事务，异步
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>是否执行成功</returns>
        Task<bool> UseTranAsync(Func<Task> action);

        /// <summary>
        /// 执行事务并获取指定返回值
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>返回值，是否执行成功</returns>
        (ReturnValueT result, bool success) UseTranReturnValue<ReturnValueT>(Func<ReturnValueT> action);

        /// <summary>
        /// 执行事务并获取指定返回值，异步
        /// </summary>
        /// <param name="action">要执行的所有操作</param>
        /// <returns>返回值，是否执行成功</returns>
        Task<(ReturnValueT result, bool success)> UseTranReturnValueAsync<ReturnValueT>(Func<Task<ReturnValueT>> action);

        #endregion 事务

        #region 不能这样使用异步事务，会无效

        ///// <summary>
        ///// 开始事务,异步
        ///// </summary>
        //Task BeginTranAsync();
        ///// <summary>
        ///// 提交事务,异步
        ///// </summary>
        //Task CommitTranAsync();
        ///// <summary>
        ///// 回滚事务,异步
        ///// </summary>
        //Task RollbackTranAsync();

        #endregion 不能这样使用异步事务，会无效


        void QueryFilterClearAndBackup();

        void QueryFilterClear();

        void QueryFilterRestore();

        /// <summary>
        /// 有外部事务，内部事务用外部事务
        /// 通过工作单元实现嵌套事务
        /// </summary>
        /// <returns></returns>
        SugarUnitOfWork CreateContext();

        #region 查询部分列扩展
        /// <summary>
        /// 根据条件获取第一个数据,支持查询部分列
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <returns>返回值</returns>
        TResult GetFirst<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression);

        /// <summary>
        /// 根据条件和排序获取第一个数据,支持查询部分列
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="orderByExpression">排序字段</param>
        /// <param name="orderByType">排序方式</param>
        /// <returns>返回值</returns>
        TResult GetFirst<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression, Expression<Func<T, object>> orderByExpression, OrderByType orderByType = OrderByType.Asc);

        /// <summary>
        /// 根据条件获取第一个数据,支持查询部分列（异步）
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回值</returns>
        Task<TResult> GetFirstAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件和排序获取第一个数据,支持查询部分列（异步）
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="orderByExpression">排序字段</param>
        /// <param name="orderByType">排序方式</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回值</returns>
        Task<TResult> GetFirstAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression, Expression<Func<T, object>> orderByExpression, OrderByType orderByType = OrderByType.Asc, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据条件和排序获取第一个数据,支持查询部分列（异步）
        /// </summary>
        /// <typeparam name="TResult">返回的数据类型</typeparam>
        /// <param name="whereExpression">查询条件</param>
        /// <param name="selectExpression">查询的列</param>
        /// <param name="orderByExpression">排序字段</param>
        /// <param name="order">排序方式（desc为倒序，其他为正序）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回值</returns>
        Task<TResult> GetFirstAsync<TResult>(Expression<Func<T, bool>> whereExpression, Expression<Func<T, TResult>> selectExpression, Expression<Func<T, object>> orderByExpression, string order, CancellationToken cancellationToken = default);
        #endregion
    }
}