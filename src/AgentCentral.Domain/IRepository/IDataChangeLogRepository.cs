using AgentCentral.Domain.Entities;
using AgentCentral.Domain.Shared.Models;

namespace AgentCentral.Domain.IRepository;

/// <summary>
/// 数据变更日志仓储接口
/// </summary>
public interface IDataChangeLogRepository : IBaseRepository<DataChangeLog>
{
    /// <summary>
    /// 获取应用变更历史记录
    /// </summary>
    /// <param name="searchModel"></param>
    /// <returns></returns>
    Task<(List<AppChangeLogModel>, int)> GetAppChangeHistoryAsync(SearchReviewRecordModel searchModel);
}