using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities;

namespace AgentCentral.Domain.IRepository
{
    /// <summary>
    /// 部门数据访问接口
    /// </summary>
    public interface IDepartmentRepository : IBaseRepository<Def_Department>
    {
        /// <summary>
        /// 获取部门树形结构
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <param name="condition">查询条件</param>
        /// <returns>部门树结构</returns>
        Task<List<Def_Department>> GetDepartmentTreeAsync(string tenantId, Expression<Func<Def_Department, bool>> condition = null);

        /// <summary>
        /// 检查部门编码是否已存在
        /// </summary>
        /// <param name="departmentCode">部门编码</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="excludeId">排除的部门ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsDepartmentCodeExistsAsync(string departmentCode, string tenantId, long? excludeId = null);

        /// <summary>
        /// 获取部门及其所有子部门
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="tenantId">租户ID</param>
        /// <returns>部门及其子部门列表</returns>
        Task<List<Def_Department>> GetDepartmentAndChildrenAsync(long departmentId, string tenantId);
    }
}
