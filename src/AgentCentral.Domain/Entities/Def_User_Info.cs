using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 用户信息
    /// </summary>
    [SugarTable(TableName = "def_user_info")]
    public class Def_User_Info : BaseEntity
    {
        public long UserId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        [SugarColumn(Length = 200)]
        public string UserName { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [SugarColumn(Length = 100)]
        public string FirstName { get; set; }

        /// <summary>
        /// 用户姓
        /// </summary>
        [SugarColumn(Length = 100)]
        public string LastName { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [SugarColumn(Length = 100)]
        public string Email { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [SugarColumn(Length = 50)]
        public string ContactNumber { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }


        /// <summary>
        /// 用户配置信息（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string UserProfile { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisterTime { get; set; }

        public UserTypeEnum UserType { get; set; }

        public string Company { get; set; }

        public string Department { get; set; }

        public string CompanyName { get; set; }

        public string DepartmentName { get; set; }

        /// <summary>
        /// 用户头像（Base64字符串）
        /// </summary>
        [SugarColumn(ColumnName = "avatar", ColumnDescription = "用户头像（Base64字符串）", IsNullable = true)]
        public string Avatar { get; set; }

        /// <summary>
        /// 用户来源：0-历史数据 1-Self-Verified 2-Admin-Created 3-Admin-Approved
        /// </summary>
        [SugarColumn(ColumnName = "UserSource", ColumnDescription = "用户来源：0-历史数据 1-Self-Verified 2-Admin-Created 3-Admin-Approved")]
        public UserSourceEnum UserSource { get; set; } = UserSourceEnum.Historical;
    }
}