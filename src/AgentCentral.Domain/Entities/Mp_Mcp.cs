using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// MCP服务管理表
    /// </summary>
    [SugarTable("mp_mcp")]
    [ChangeNoTracker]
    public class Mp_Mcp : BaseEntity
    {
        /// <summary>
        /// DI平台的MCP ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long McpId { get; set; }

        /// <summary>
        /// 服务名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        public string ServiceName { get; set; }

        /// <summary>
        /// 服务提供商
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string Provider { get; set; }

        /// <summary>
        /// 服务描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        public string Description { get; set; }

        /// <summary>
        /// 服务Logo URL
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string LogoUrl { get; set; }

        /// <summary>
        /// 服务分类
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string Category { get; set; }

        /// <summary>
        /// 使用场景
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string UseCases { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string ServiceType { get; set; }

        /// <summary>
        /// 服务价值
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string ServiceValue { get; set; }

        /// <summary>
        /// 文档URL
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string DocumentUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string VideoUrl { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string Source { get; set; }

        /// <summary>
        /// 状态(1:未提交, 2:待审核, 3:已发布, 4:已拒绝, 5:已下架, 6:已删除)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public McpStatusEnum Status { get; set; } = McpStatusEnum.NotSubmitted;

        /// <summary>
        /// 自动审核结果
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ReviewResult { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string Version { get; set; }
    }
} 