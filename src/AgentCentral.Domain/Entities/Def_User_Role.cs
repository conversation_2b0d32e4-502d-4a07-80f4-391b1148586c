using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 用户角色关联实体
    /// </summary>
    [SugarTable(TableName = "def_user_role")]
    [ChangeNoTracker]
    public class Def_User_Role : BaseEntity
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public long RoleId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public long DepartmentId { get; set; }

    }
}
