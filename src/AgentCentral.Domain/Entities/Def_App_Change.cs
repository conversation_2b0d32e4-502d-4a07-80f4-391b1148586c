using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// Application change record
    /// </summary>
    [SugarTable(TableName = "def_app_change")]
    [ChangeNoTracker]
    public class Def_App_Change : BaseEntity
    {
        /// <summary>
        /// Application ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// Change type (bitwise)
        /// </summary>
        public int ChangeType { get; set; }

        /// <summary>
        /// Whether to upgrade version
        /// True: Create new version
        /// False: Replace current version
        /// </summary>
        public bool NeedUpgradeVersion { get; set; }

        /// <summary>
        /// Whether to force update
        /// True: Force all users to update to new version
        /// False: Users can choose when to update
        /// </summary>
        public bool ForceUpdate { get; set; }

        /// <summary>
        /// Change description
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string ChangeDescription { get; set; }
    }
}