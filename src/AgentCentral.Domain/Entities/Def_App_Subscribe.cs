using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 应用订阅
    /// </summary>
    [SugarTable(TableName = "def_app_subscribe")]
    [ChangeNoTracker]
    public class Def_App_Subscribe : BaseEntity
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        [SugarColumn(Length = 200)]
        public string AppId { get; set; }
    }
}