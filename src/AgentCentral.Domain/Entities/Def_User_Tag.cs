using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 用户标签
    /// </summary>
    [SugarTable(TableName = "def_user_tag")]
    [ChangeNoTracker]
    public class Def_User_Tag : BaseEntity
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        [SugarColumn(Length = 100)]
        public string TagName { get; set; }

        /// <summary>
        /// 标签编码
        /// </summary>
        [SugarColumn(Length = 50)]
        public string TagCode { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        [SugarColumn(Length = 50)]
        public string TagType { get; set; }
    }
}
