using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    [SugarTable("doc_resource")]
    [ChangeNoTracker]
    public class Doc_Resource : BaseEntity
    {
        public string Name { get; set; }

        public string Company { get; set; }

        public string Department { get; set; }

        public string Position { get; set; }

        public string JobDescription { get; set; }

        [SugarColumn(IsJson = true)] public List<string> RelatedCustomers { get; set; }

        [SugarColumn(IsJson = true)] public List<string> RelatedSuppliers { get; set; }

        [SugarColumn(IsJson = true)] public List<CommonProgram> CommonPrograms { get; set; }
    }

    public class CommonProgram
    {
        public string ProgramName { get; set; }
        public string Link { get; set; }
        public string LoginAccount { get; set; }
    }
}