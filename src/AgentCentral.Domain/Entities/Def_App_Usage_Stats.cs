using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    [SugarTable(TableName = "def_app_usage_stats")]
    [ChangeNoTracker]
    public class Def_App_Usage_Stats : BaseEntity
    {
        [SugarColumn(Length = 200)]
        public string AppId { get; set; }

        public long MainAppId { get; set; }

        public int Runs { get; set; }
        public int Joins { get; set; }
    }
}