using Item.Internal.ChangeLog;
using AgentCentral.Infrastructure.JsonConverts;
using Newtonsoft.Json;
using SqlSugar;

namespace AgentCentral.Domain.Entities.Workflow
{
    [SugarTable("work_flow_log")]
    [ChangeNoTracker]
    public class WorkflowLog
    {
        /// <summary>
        /// id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public virtual long Id { get; set; }

        /// <summary>
        /// 操作数据标识ID
        /// </summary>
        public long BusinessId { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 当前审批人标识
        /// </summary>
        public long ApprovalUserId { get; set; }

        /// <summary>
        /// 当前审批人名称
        /// </summary>
        public string ApprovalUserName { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        [JsonConverter(typeof(UsDateFormatConverter))]
        public DateTime ApprovalDate { get; set; }

        /// <summary>
        /// 审批状态
        /// </summary>
        public int ApprovalStatus { get; set; }
    }
}
