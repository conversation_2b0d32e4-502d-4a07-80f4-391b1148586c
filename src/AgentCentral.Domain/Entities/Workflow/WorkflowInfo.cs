using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities.Workflow
{
    [SugarTable("work_flow_info")]
    [ChangeNoTracker]
    public class WorkflowInfo : BaseEntity
    {
        /// <summary>
        /// 关联业务id
        /// </summary>
        public long BusinessId { get; set; }

        /// <summary>
        /// 业务类型(源于WorkflowBussinessTypeEnum)
        /// </summary>
        public int Category { get; set; }

        /// <summary>
        /// 任务id
        /// </summary>
        public string TaskId { get; set; }

        /// <summary>
        ///  当前节点名称
        /// </summary>
        public string CurrentNodeName { get; set; }

        /// <summary>
        /// 操作者角色id集合，逗号分隔：,1,2,3,
        /// </summary>
        public string RoleIds { get; set; }

        /// <summary>
        /// 操作者id集合，逗号分隔：,1,2,3,
        /// </summary>
        public string UserIds { get; set; }

        /// <summary>
        /// 是否结束
        /// </summary>
        public bool IsEnd { get; set; }

        /// <summary>
        /// 当前节点描述
        /// </summary>
        public string CurrentNodeDescription { get; set; }
    }
}
