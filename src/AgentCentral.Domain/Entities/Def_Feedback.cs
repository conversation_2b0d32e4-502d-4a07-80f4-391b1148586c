using SqlSugar;
using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;

namespace AgentCentral.Domain.Entities
{
    [SugarTable("def_feedback")]
    [ChangeNoTracker]
    public class Def_Feedback : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        public string FeedbackId { get; set; }

        /// <summary>
        /// 问题类型
        /// </summary>
        public FeedbackIssueTypeEnum IssueType { get; set; }
        
        /// <summary>
        /// 模块类型
        /// </summary>
        public string Module {get;set;}

        /// <summary>
        /// 名
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 反馈内容
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public FeedbackStatusEnum Status { get; set; }

        /// <summary>
        /// AI生成的标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 管理员回复
        /// </summary>
        public string Reply { get; set; }

        /// <summary>
        /// 回复管理员用户ID
        /// </summary>
        public string ReplyUserId { get; set; }
    }
}