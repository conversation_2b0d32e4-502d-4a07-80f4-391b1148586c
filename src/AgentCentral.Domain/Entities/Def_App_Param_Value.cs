using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// AI Agent参数值表
    /// </summary>
    [SugarTable("def_app_param_value")]
    [ChangeNoTracker]
    public class Def_App_Param_Value : BaseEntity
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// 参数定义ID
        /// </summary>
        public long ParamId { get; set; }

        /// <summary>
        /// 参数键名
        /// </summary>
        public string ParamKey { get; set; }

        /// <summary>
        /// 参数值
        /// </summary>
        public string ParamValue { get; set; }
    }
}
