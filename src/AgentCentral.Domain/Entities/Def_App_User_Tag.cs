using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 应用与用户标签关联
    /// </summary>
    [SugarTable(TableName = "def_app_user_tag")]
    [ChangeNoTracker]
    public class Def_App_User_Tag : BaseEntity
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// 用户标签ID
        /// </summary>
        public long UserTagId { get; set; }
    }
}
