using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities;

/// <summary>
/// 用户类型关联
/// </summary>
[SugarTable(TableName = "def_user_type_relation")]
[ChangeNoTracker]
public class Def_User_Type_Relation : BaseEntity
{

    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public UserTypeEnum UserType { get; set; }
}