using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    [SugarTable(TableName = "def_app_usage")]
    [ChangeNoTracker]
    public class Def_App_Usage : BaseEntity
    {
        [SugarColumn(Length = 200)]
        public string AppId { get; set; }

        public long MainAppId { get; set; }

        public int TotalTokens { get; set; }
        public int CostSeconds { get; set; }
    }
}