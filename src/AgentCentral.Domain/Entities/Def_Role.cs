using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 角色实体
    /// </summary>
    [SugarTable(TableName = "def_role")]
    [ChangeNoTracker]
    public class Def_Role : BaseEntity
    {

        /// <summary>
        /// 公司/租户
        /// </summary>
        [SugarColumn(Length = 32)]
        public string Company { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        [SugarColumn(Length = 100)]
        public string RoleName { get; set; }

        /// <summary>
        /// 角色编码
        /// </summary>
        [SugarColumn(Length = 50)]
        public string RoleCode { get; set; }

        /// <summary>
        /// 所属部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        [SugarColumn(Length = 500)]
        public string Description { get; set; }

        /// <summary>
        /// 是否系统角色
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; }
    }
}
