using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 资源流程信息表
    /// </summary>
    [SugarTable("doc_resource_process")]
    [ChangeNoTracker]
    public class Doc_Resource_Process : BaseEntity
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        [SugarColumn(Length = 50)]
        public string EmployeeId { get; set; }

        /// <summary>
        /// 分组
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Group { get; set; }

        /// <summary>
        /// 流程环境
        /// </summary>
        [SugarColumn(Length = 50)]
        public string ProcessEnvironment { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        [SugarColumn(Length = 50)]
        public string Process { get; set; }

        /// <summary>
        /// 流程名称
        /// </summary>
        [SugarColumn(Length = 200)]
        public string ProcessName { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        [SugarColumn(Length = 200)]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Customer { get; set; }

        /// <summary>
        /// 客户租户ID
        /// </summary>
        [SugarColumn(Length = 32)]
        public string CustomerTenantId { get; set; }

        /// <summary>
        /// 零售商客户编码
        /// </summary>
        [SugarColumn(Length = 200)]
        public string RetailerCode { get; set; }

        /// <summary>
        /// 零售商
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Retailer { get; set; }

        /// <summary>
        /// 零售商租户ID
        /// </summary>
        [SugarColumn(Length = 32)]
        public string RetailerTenantId { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string Description { get; set; }

        /// <summary>
        /// URL链接
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string UrlLinks { get; set; }
    }
}