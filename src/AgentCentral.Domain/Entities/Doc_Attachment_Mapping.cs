using SqlSugar;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 附件映射
    /// </summary>
    [SugarTable("doc_attachment_mapping")]
    [ChangeNoTracker]
    public class Doc_Attachment_Mapping
    {
        /// <summary>
        /// 业务Id
        /// </summary>
        public long BusinessId { get; set; }
        /// <summary>
        /// 附件Id
        /// </summary>
        public long AttachmentId { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public AttachmentTypeEnum BusinessType { get; set; }
    }
}
