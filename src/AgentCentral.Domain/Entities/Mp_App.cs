using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 应用市场应用信息表
    /// </summary>
    [SugarTable("mp_app")]
    [ChangeNoTracker]
    public class Mp_App : BaseEntity
    {

        /// <summary>
        /// 应用编码(D/P+6位数字)
        /// </summary>
        [SugarColumn(Length = 7, IsNullable = false)]
        public string AppId { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        [SugarColumn(Length = 255)]
        public string AppName { get; set; }

        /// <summary>
        /// 默认语言
        /// </summary>
        [SugarColumn(Length = 100)]
        public string DefaultLanguage { get; set; }

        /// <summary>
        /// 变现方式(Free/Paid)
        /// </summary>
        [SugarColumn(Length = 20)]
        public MonetizationTypeEnum MonetizationType { get; set; }

        /// <summary>
        /// 隐私政策类型(Document/Url)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = true)]
        public PrivacyPolicyDocumentTypeEnum? PrivacyPolicyDocumentType { get; set; }

        /// <summary>
        /// 隐私政策URL地址
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string PrivacyPolicyUrl { get; set; }

        /// <summary>
        /// 隐私政策文档名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string PrivacyPolicyFileName { get; set; }

        /// <summary>
        /// 隐私政策文档大小(bytes)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? PrivacyPolicyFileSize { get; set; }

        /// <summary>
        /// 隐私政策文档类型(如pdf/docx)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string PrivacyPolicyFileType { get; set; }

        /// <summary>
        /// 应用市场展示名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string AppDisplayName { get; set; }

        /// <summary>
        /// 简要描述(最多80字)
        /// </summary>
        [SugarColumn(Length = 160, IsNullable = true)]
        public string ShortDescription { get; set; }

        /// <summary>
        /// 完整描述(支持markdown)
        /// </summary>
        [SugarColumn(ColumnDataType = "text", IsNullable = true)]
        public string FullDescription { get; set; }

        /// <summary>
        /// 应用图标路径(512x512)
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string AppIcon { get; set; }

        /// <summary>
        /// 应用版本号
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string VersionNumber { get; set; }

        /// <summary>
        /// 开发者/团队名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string DeveloperName { get; set; }

        /// <summary>
        /// 开发者联系方式
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string DeveloperContact { get; set; }

        /// <summary>
        /// App类型分类ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? CategoryId { get; set; }

        /// <summary>
        /// APK文件路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string ApkFilePath { get; set; }

        /// <summary>
        /// APK文件名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ApkName { get; set; }

        /// <summary>
        /// APK文件大小(bytes)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ApkSize { get; set; }

        /// <summary>
        /// 上传类型(DeveloperBuild/ExistingApp)
        /// </summary>
        [SugarColumn(Length = 20)]
        public UploadTypeEnum UploadType { get; set; }

        /// <summary>
        /// 平台类型(ITEM/GooglePlay/AppStore)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public PlatformTypeEnum? PlatformType { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool? AllowDownload { get; set; } = false;

        /// <summary>
        /// 下载次数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long DownloadCount { get; set; } = 0;

        /// <summary>
        /// 应用状态(Draft/Reviewing/Published/Rejected)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = true)]
        public MpAppStatusEnum AppStatus { get; set; } = MpAppStatusEnum.NotSubmitted;

        /// <summary>
        /// 提交日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SubmitDate { get; set; }

        /// <summary>
        /// 上线日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 应用评分(1-5分)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal Rating { get; set; } = 0;

        /// <summary>
        /// 内容质量评分
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? ContentQualityScore { get; set; }

        /// <summary>
        /// 安全检查评分
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? SafetyCheckScore { get; set; }

        /// <summary>
        /// 合规性评分
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? ComplianceScore { get; set; }

        /// <summary>
        /// 总体评分
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? OverallScore { get; set; }

        /// <summary>
        /// 上传包体分析/外链分析结果
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsFileAnalysis { get; set; }
        /// <summary>
        /// 隐私协议分析结果
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsPrivacyAnalysis { get; set; }
        
        /// <summary>
        /// 隐私策略审核结果(JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "text", IsNullable = true)]
        public string PrivacyPolicyReviewResult { get; set; }
    }
} 