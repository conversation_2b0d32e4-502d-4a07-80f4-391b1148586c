using Newtonsoft.Json;
using SqlSugar;

namespace AgentCentral.Domain.Entities.Base
{
    public abstract class IdEntityBase
    {
        /// <summary>
        /// id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [JsonConverter(typeof(ValueToStringConverter))]
        public virtual long Id { get; set; } = SnowFlakeSingle.Instance.NextId();

        public long InitNewId()
        {
            Id = SnowFlakeSingle.Instance.NextId();
            return Id;
        }
    }
}