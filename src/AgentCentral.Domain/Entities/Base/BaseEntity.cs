using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities.Base
{
    public abstract class BaseEntity : IdEntityBase
    {
        [SugarColumn(IsOnlyIgnoreUpdate = true)]
        [ChangeLogColumn(IsIgnore = true)]
        public virtual string TenantId { get; set; }

        public virtual bool IsActive { get; set; } = true;

        [SugarColumn(IsOnlyIgnoreUpdate = true)]
        [ChangeLogColumn(IsIgnore = true)]
        public virtual long CreateBy { get; set; } = 0;

        [SugarColumn(IsOnlyIgnoreUpdate = true)]
        [ChangeLogColumn(IsIgnore = true)]
        public virtual string CreateName { get; set; } = string.Empty;

        [SugarColumn(IsOnlyIgnoreUpdate = true)]
        [ChangeLogColumn(IsIgnore = true)]
        public virtual DateTime CreateTime { get; set; } = DateTime.UtcNow;

        [ChangeLogColumn(IsIgnore = true)]
        public virtual long UpdateBy { get; set; } = 0;

        [ChangeLogColumn(IsIgnore = true)]
        public virtual string UpdateName { get; set; } = string.Empty;

        [ChangeLogColumn(IsIgnore = true)]
        public virtual DateTime UpdateTime { get; set; } = DateTime.UtcNow;
    }
}