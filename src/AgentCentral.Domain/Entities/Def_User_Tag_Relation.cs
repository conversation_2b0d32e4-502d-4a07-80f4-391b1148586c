using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 用户标签关联
    /// </summary>
    [SugarTable(TableName = "def_user_tag_relation")]
    [ChangeNoTracker]
    public class Def_User_Tag_Relation : BaseEntity
    {

        /// <summary>
        /// 用户ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 标签ID
        /// </summary>
        public long TagId { get; set; }
    }
}
