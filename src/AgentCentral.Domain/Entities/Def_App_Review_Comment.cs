using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 应用评价
    /// </summary>
    [SugarTable(TableName = "def_app_review_comment")]
    [ChangeNoTracker]
    public class Def_App_Review_Comment : BaseEntity
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        [SugarColumn(Length = 200)]
        public string AppId { get; set; }

        public long MainAppId { get; set; }

        /// <summary>
        /// 评分（1-5星）
        /// </summary>
        public int RatingStar { get; set; }

        /// <summary>
        /// 评价内容
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string ReviewContent { get; set; }
    }
}