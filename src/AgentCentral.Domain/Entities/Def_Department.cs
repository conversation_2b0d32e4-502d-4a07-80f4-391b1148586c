using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 部门实体
    /// </summary>
    [SugarTable(TableName = "def_department")]
    [ChangeNoTracker]
    public class Def_Department : BaseEntity
    {

        /// <summary>
        /// 公司/租户
        /// </summary>
        [SugarColumn(Length = 32)]
        public string Company { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [SugarColumn(Length = 100)]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        [SugarColumn(Length = 50)]
        public string DepartmentCode { get; set; }

        /// <summary>
        /// 父部门ID
        /// </summary>
        public long? ParentId { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 部门描述
        /// </summary>
        [SugarColumn(Length = 500)]
        public string Description { get; set; }
    }
}
