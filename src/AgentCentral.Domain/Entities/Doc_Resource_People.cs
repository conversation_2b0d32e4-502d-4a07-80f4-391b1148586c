using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 资源人员信息表
    /// </summary>
    [SugarTable("doc_resource_people")]
    [ChangeNoTracker]
    public class Doc_Resource_People : BaseEntity
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        [SugarColumn(Length = 50)]
        public string EmployeeId { get; set; }

        /// <summary>
        /// 员工名字
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Name { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Company { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Department { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Position { get; set; }

        /// <summary>
        /// 设施
        /// </summary>
        [SugarColumn(Length = 200)]
        public string Facility { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string Description { get; set; }

        /// <summary>
        /// URL链接
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string UrlLinks { get; set; }
    }
}