using SqlSugar;
using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;

namespace AgentCentral.Domain.Entities
{
    [SugarTable("doc_attachment")]
    [ChangeNoTracker]
    public class Doc_Attachment : BaseEntity
    {
        /// <summary>
        /// 文件原名
        /// </summary>
        public string RealName { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public string FileType { get; set; }

        /// <summary>
        /// 存储文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件存储地址 eg：/uploads/20220202
        /// </summary>
        public string FileAbsolutePath { get; set; }

        /// <summary>
        /// 仓库位置 eg：/uploads
        /// </summary>
        public string FolderPath { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string FileExt { get; set; }

        /// <summary>
        /// 存储类型
        /// </summary>
        public AttachmentStoreTypeEnum? StoreType { get; set; }

        /// <summary>
        /// 访问路径
        /// </summary>
        public string AccessUrl { get; set; }

        /// <summary>
        /// 文件MD5
        /// </summary>
        [SugarColumn(Length = 32)]
        public string Md5 { get; set; }

        public bool IsConvert { get; set; }
    }
}
