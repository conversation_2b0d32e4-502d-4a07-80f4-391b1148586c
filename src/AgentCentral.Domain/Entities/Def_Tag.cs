using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 标签表
    /// </summary>
    [SugarTable("def_tag")]
    [ChangeNoTracker]
    public class Def_Tag : BaseEntity
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        [SugarColumn(Length = 255)]
        public string TagName { get; set; }

        /// <summary>
        /// Dify平台TagId
        /// </summary>
        [SugarColumn(Length = 100)]
        public string DifyTagId { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        public int TagType { get; set; }
    }
}