using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// 标签映射表
    /// </summary>
    [SugarTable("def_tag_mapping")]
    [ChangeNoTracker]
    public class Def_Tag_Mapping : BaseEntity
    {
        /// <summary>
        /// 标签ID
        /// </summary>
        public long TagId { get; set; }

        /// <summary>
        /// 资源ID
        /// </summary>
        public long ResourceId { get; set; }
    }
}