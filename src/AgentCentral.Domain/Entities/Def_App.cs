using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;
using Newtonsoft.Json;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    [SugarTable(TableName = "def_app")]
    public class Def_App : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        [JsonConverter(typeof(ValueToStringConverter))]
        [ChangeLogColumn(IsCustomerId = true)]
        public override long Id { get; set; } = SnowFlakeSingle.Instance.NextId();

        public string GeneratorId { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        [SugarColumn(Length = 200)]
        public string AppId { get; set; }

        /// <summary>
        /// 应用编码
        /// </summary>
        [SugarColumn(Length = 200)]
        public string AppCode { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        [SugarColumn(Length = 200)]
        public string AppName { get; set; }

        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用模式
        /// </summary>
        [SugarColumn(Length = 50)]
        public string AppMode { get; set; }

        /// <summary>
        /// 应用图标(后期优化为OSS存储)
        /// </summary>
        public string AppIcon { get; set; }

        /// <summary>
        /// 站点启用状态
        /// </summary>
        public bool EnableSite { get; set; } = true;

        /// <summary>
        /// API启用状态
        /// </summary>
        public bool EnableApi { get; set; } = true;

        public AppPermissionEnum AppPermission { get; set; } = AppPermissionEnum.Public;

        /// <summary>
        /// 应用Tag
        /// </summary>
        [SugarColumn(Length = 50)]
        public string AppTag { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [SugarColumn(Length = 50)]
        public string Department { get; set; }

        public AppStatusEnum AppStatus { get; set; } = AppStatusEnum.Unpublished;

        public AppSourceEnum AppSource { get; set; } = AppSourceEnum.Dify;

        /// <summary>
        /// 内容质量评分
        /// </summary>
        public decimal ContentQualityScore { get; set; }

        /// <summary>
        /// 安全检查评分
        /// </summary>
        public decimal SafetyCheckScore { get; set; }

        /// <summary>
        /// 合规性评分
        /// </summary>
        public decimal ComplianceScore { get; set; }

        /// <summary>
        /// 总体评分
        /// </summary>
        public decimal OverallScore { get; set; }

        /// <summary>
        /// Dify发布状态
        /// </summary>
        public bool DifyPublished { get; set; } = true;

        /// <summary>
        /// 主应用ID
        /// </summary>
        public long MainAppId { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int VersionNumber { get; set; }

        /// <summary>
        /// 是否为当前使用的版本
        /// </summary>
        public bool IsCurrentVersion { get; set; } = false;

        /// <summary>
        /// 游客模式，0-不允许，1-允许
        /// </summary>
        public bool GuestMode { get; set; } = false;

        /// <summary>
        /// 提交日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SubmitDate { get; set; }
    }
}