using Item.Internal.ChangeLog;
using Item.Internal.ChangeLog.Models;
using Newtonsoft.Json;
using SqlSugar;

namespace AgentCentral.Domain.Entities;

[SugarTable("data_change_log")]
[ChangeNoTracker]
public class DataChangeLog
{
    [SugarColumn(IsPrimaryKey = true)]
    [JsonConverter(typeof(ValueToStringConverter))]
    public virtual long Id { get; set; }

    public string TableName { get; set; }

    public string ColumnName { get; set; }

    public string NewValue { get; set; }

    public string OldValue { get; set; }

    public string PrimaryValue { get; set; }

    public string UserId { get; set; }

    public string UserName { get; set; }

    public DateTime OpTime { get; set; }

    [SugarColumn(IsOnlyIgnoreUpdate = true)]
    [ChangeLogColumn(IsIgnore = true)]
    public virtual string TenantId { get; set; }

    [SugarColumn(ColumnName = "AppId")]
    public long AppId { get; set; } = 0;

    public OpTypeEnum OpType { get; set; }

    public string OpId { get; set; }

    public string ExtendData { get; set; }
}
