using AgentCentral.Domain.Entities.Base;
using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    /// <summary>
    /// AI Agent parameter definition table
    /// </summary>
    [SugarTable("def_app_param")]
    [ChangeNoTracker]
    public class Def_App_Param : BaseEntity
    {
        /// <summary>
        /// Application ID
        /// </summary>
        public long AppId { get; set; }

        /// <summary>
        /// Parameter key
        /// </summary>
        public string ParamKey { get; set; }

        /// <summary>
        /// Parameter name
        /// </summary>
        public string ParamName { get; set; }

        /// <summary>
        /// Parameter description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Parameter type
        /// </summary>
        public AppParamTypeEnum ParamType { get; set; }

        /// <summary>
        /// Is required
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Default value
        /// </summary>
        public string DefaultValue { get; set; }

        /// <summary>
        /// Dropdown options (JSON format, valid only when ParamType is Dropdown)
        /// </summary>
        public string Options { get; set; }

        /// <summary>
        /// Validation rules (JSON format, can include max length, min length, regex pattern, etc.)
        /// </summary>
        public string ValidationRules { get; set; }

        /// <summary>
        /// Sort order
        /// </summary>
        public int SortOrder { get; set; }
    }
}
