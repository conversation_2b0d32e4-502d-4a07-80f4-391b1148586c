using AgentCentral.Domain.Entities.Base;
using Item.Internal.ChangeLog;
using SqlSugar;

namespace AgentCentral.Domain.Entities
{
    [SugarTable(TableName = "def_app_permission")]
    [ChangeNoTracker]
    public class Def_App_Permission : BaseEntity
    {
        public string AppId { get; set; }

        public string Company { get; set; }

        public string DepartmentCode { get; set; }
    }
}
