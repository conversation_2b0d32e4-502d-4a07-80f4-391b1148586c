namespace AgentCentral.Domain.Shared.Models;

/// <summary>
/// 应用变更记录Model
/// </summary>
public class AppChangeLogModel
{
    public long MainAppId { get; set; }

    /// <summary>
    /// 应用ID
    /// </summary>
    public long AppId { get; set; }

    /// <summary>
    /// 应用名称
    /// </summary>
    public string AppName { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public int OpType { get; set; }

    /// <summary>
    /// 应用状态
    /// </summary>
    public int AppStatus { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OpTime { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreateName { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    public long CreatorId { get; set; }

    public string OpId { get; set; }

    public int VersionNumber { get; set; }

    public string ApprovalUserName { get; set; }

    public string Description { get; set; }

    public string ChangeDescription { get; set; }

    public string GeneratorId { get; set; }
}