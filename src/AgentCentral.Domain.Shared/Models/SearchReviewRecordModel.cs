using AgentCentral.Domain.Shared.Enums;
using Item.Internal.ChangeLog.Models;

namespace AgentCentral.Domain.Shared.Models
{
    public class SearchReviewRecordModel
    {
        public DateTime? StartTime { get; set; }

        public DateTime? EndTime { get; set; }

        public string AgentName { get; set; }

        public string AgentId { get; set; }

        public int PageIndex { get; set; }

        public int PageSize { get; set; }

        public string Creator { get; set; }

        public OpTypeEnum? Operation { get; set; }

        public string Reviewer { get; set; }

        public AppStatusEnum? Status { get; set; }
    }
}
