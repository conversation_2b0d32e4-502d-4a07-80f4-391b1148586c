using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Domain.Shared.Models
{
    public class AppListModel
    {
        public long Id { get; set; }
        /// <summary>
        /// 应用ID
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用编码
        /// </summary>
        public string AppCode { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用模式
        /// </summary>
        public string AppMode { get; set; }

        /// <summary>
        /// 应用图标
        /// </summary>
        public string AppIcon { get; set; }

        /// <summary>
        /// 站点启用状态
        /// </summary>
        public bool EnableSite { get; set; }

        /// <summary>
        /// API启用状态
        /// </summary>
        public bool EnableApi { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 创建人名字
        /// </summary>
        public string CreateName { get; set; }

        /// <summary>
        /// 应用Tag
        /// </summary>
        public string AppTag { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public AppPermissionEnum AppPermission { get; set; }

        public AppStatusEnum AppStatus { get; set; }

        public AppSourceEnum AppSource { get; set; }

        public List<AttachmentOutputModel> Banners { get; set; } = [];

        public AttachmentOutputModel Video { get; set; }

        public List<AttachmentOutputModel> Departments { get; set; } = [];

        public string RejectReason { get; set; } = "";

        public string TenantId { get; set; }

        public bool IsActive { get; set; }

        public long UpdateBy { get; set; }

        public string UpdateName { get; set; }

        public DateTime UpdateTime { get; set; }

        public string Department { get; set; }

        public int RatingStar { get; set; }

        public int TotalStar { get; set; }

        public string Tags { get; set; }

        public long MainAppId { get; set; }

        public bool CheckUpdate { get; set; }

        public string CurrentAppId { get; set; }

        public bool DifyPublished { get; set; }

        public string GeneratorId { get; set; }
    }

    public class AttachmentOutputModel
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Url { get; set; }
    }
}
