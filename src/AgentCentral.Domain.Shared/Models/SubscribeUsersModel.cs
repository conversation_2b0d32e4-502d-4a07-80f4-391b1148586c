namespace AgentCentral.Domain.Shared.Models;

public class SubscribeUsersModel
{
    /// <summary>
    /// 用户名字
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 用户头像Id
    /// </summary>
    public long? AttachmentId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 存储文件名
    /// </summary>
    public string AccessUrl { get; set; }

    /// <summary>
    /// 存储文件名
    /// </summary>
    public string FileName { get; set; }
}