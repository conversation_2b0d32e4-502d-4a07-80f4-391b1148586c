using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Domain.Shared.Models
{
    public class FeedbackModel
    {
        public string FeedbackId { get; set; }

        public long Id { get; set; }

        public string Title { get; set; }

        public FeedbackIssueTypeEnum IssueType { get; set; }

        public string Module { get; set; }

        public FeedbackStatusEnum Status { get; set; }

        public string CreateName { get; set; }

        public string Company { get; set; }

        public string Department { get; set; }

        public DateTime CreateTime { get; set; }

        public string Email { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }
    }
}
