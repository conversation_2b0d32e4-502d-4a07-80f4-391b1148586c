namespace AgentCentral.Domain.Shared.Models
{
    public class ResourceProcessListModel
    {
        public long Id { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 贡献者名称
        /// </summary>
        public string ContributorName { get; set; }

        /// <summary>
        /// 公司/部门
        /// </summary>
        public string ProcessEnvironment { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string Process { get; set; }

        /// <summary>
        /// 流程名称
        /// </summary>
        public string ProcessName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string Customer { get; set; }

        /// <summary>
        /// 零售商
        /// </summary>
        public string Retailer { get; set; }

        /// <summary>
        /// URL链接
        /// </summary>
        public string UrlLinks { get; set; }
    }
}
