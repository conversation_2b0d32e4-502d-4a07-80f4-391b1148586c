using AgentCentral.Domain.Shared.Enums;

namespace AgentCentral.Domain.Shared.Models
{
    public class UserContext
    {
        public long UserId { get; set; }
        public string UserName { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }

        public string ClientId { get; set; }

        public string ValidationVersion { get; set; }


        public UserTypeEnum UserType { get; set; }

        public string TenantId { get; set; }

        public DateTime CreateTime { get; set; }

        public string Token { get; set; }

        public TimeSpan UtcOffset { get; set; } = TimeZoneInfo.Local.BaseUtcOffset;

        public string Company { get; set; }

        public string DepartmentCode { get; set; }

        public string DepartmentName { get; set; }
    }
}