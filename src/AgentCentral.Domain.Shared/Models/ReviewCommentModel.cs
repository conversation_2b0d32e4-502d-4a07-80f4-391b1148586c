namespace AgentCentral.Domain.Shared.Models;

public class ReviewCommentModel
{
    /// <summary>
    /// 评分（1-5星）
    /// </summary>
    public int RatingStar { get; set; }

    /// <summary>
    /// 评价内容
    /// </summary>
    public string? ReviewContent { get; set; }

    /// <summary>
    /// 评价时间
    /// </summary>
    public DateTime CreateTime { get; set; }


    /// <summary>
    /// 用户名称
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// AttachmentId
    /// </summary>
    public long? AttachmentId { get; set; }

    /// <summary>
    /// 访问路径
    /// </summary>
    public string AccessUrl { get; set; }

    /// <summary>
    /// 存储文件名
    /// </summary>
    public string FileName { get; set; }
}