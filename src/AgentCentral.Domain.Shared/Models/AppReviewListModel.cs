using AgentCentral.Domain.Shared.Enums;
using Item.Common.Lib.JsonConverts;
using Newtonsoft.Json;

namespace AgentCentral.Domain.Shared.Models
{
    public class AppReviewListModel
    {
        public long Id { get; set; }

        public string AppId { get; set; }

        public string AppCode { get; set; }

        public string AppName { get; set; }

        public string AppDescription { get; set; }

        public string AppMode { get; set; }

        public string AppIcon { get; set; }

        public bool EnableSite { get; set; }

        public bool EnableApi { get; set; }

        public AppPermissionEnum AppPermission { get; set; }

        public string AppTag { get; set; }

        public AppStatusEnum AppStatus { get; set; }

        public AppSourceEnum AppSource { get; set; }

        public decimal ContentQualityScore { get; set; }

        public decimal SafetyCheckScore { get; set; }

        public decimal ComplianceScore { get; set; }

        public decimal OverallScore { get; set; }

        public bool DifyPublished { get; set; }

        public string CompanyName { get; set; }

        public string DepartmentName { get; set; }

        public string CreateName { get; set; }

        public DateTime CreateTime { get; set; }

        public DateTime ApprovalDate { get; set; }

        public string Reviewer { get; set; }

        /// <summary>
        /// 游客模式，true-允许，false-不允许
        /// </summary>
        public bool GuestMode { get; set; }

        /// <summary>
        /// 提交日期
        /// </summary>
        public DateTime? SubmitDate { get; set; }

    }
}
