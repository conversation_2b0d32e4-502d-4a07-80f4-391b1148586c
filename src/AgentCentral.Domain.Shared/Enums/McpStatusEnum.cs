using System.ComponentModel;

namespace AgentCentral.Domain.Shared.Enums
{
    /// <summary>
    /// MCP服务状态枚举
    /// </summary>
    public enum McpStatusEnum
    {
        /// <summary>
        /// 未提交
        /// </summary>
        [Description("未提交")]
        NotSubmitted = 1,

        /// <summary>
        /// 待审核
        /// </summary>
        [Description("待审核")]
        Pending = 2,

        /// <summary>
        /// 自动通过
        /// </summary>
        [Description("自动通过")]
        AutoApproved = 3,

        /// <summary>
        /// 自动拒绝
        /// </summary>
        [Description("自动拒绝")]
        AutoRejected = 4,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Description("已拒绝")]
        Rejected = 5,

        /// <summary>
        /// 已发布
        /// </summary>
        [Description("已发布")]
        Published = 6,

        /// <summary>
        /// 已下架
        /// </summary>
        [Description("已下架")]
        Delisted = 7,

        /// <summary>
        /// 已删除
        /// </summary>
        [Description("已删除")]
        Deleted = 8

    }
} 