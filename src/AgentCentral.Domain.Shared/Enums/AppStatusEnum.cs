namespace AgentCentral.Domain.Shared.Enums
{
    public enum AppStatusEnum
    {
        /// <summary>
        /// 未发布(Not Submit)
        /// </summary>
        Unpublished = 1,

        /// <summary>
        /// 审核中
        /// </summary>
        UnderReview = 2,

        /// <summary>
        /// 未通过
        /// </summary>
        Rejected = 3,

        /// <summary>
        /// 已发布
        /// </summary>
        Published = 4,

        AutoApproved = 5,

        AutoRejected = 6,

        AutoPublished = 7,

        /// <summary>
        /// Dify未发布(Not Published)
        /// </summary>
        DifyUnpublished = 8,

        /// <summary>
        /// 已下架
        /// </summary>
        Delisted = 9,

        /// <summary>
        /// 已删除
        /// </summary>
        Deleted = 10,
    }
}
