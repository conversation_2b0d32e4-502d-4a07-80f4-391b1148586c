namespace AgentCentral.Domain.Shared.Enums
{
    /// <summary>
    /// Application change types (supports bitwise operations, can be combined)
    /// </summary>
    public enum AppChangeTypeEnum
    {
        /// <summary>
        /// Presentation changes (e.g., UI styles, text content)
        /// </summary>
        Presentation = 1 << 0,  // 1

        /// <summary>
        /// Configuration changes (e.g., parameter settings, model configurations)
        /// </summary>
        Configuration = 1 << 1,  // 2

        /// <summary>
        /// Functional changes (e.g., add/modify/delete features)
        /// </summary>
        Functional = 1 << 2,  // 4
    }
}