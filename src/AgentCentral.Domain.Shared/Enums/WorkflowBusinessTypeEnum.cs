using System.ComponentModel;

namespace AgentCentral.Domain.Shared.Enums
{
    /// <summary>
    /// 工作流业务
    /// </summary>
    public enum WorkflowBusinessTypeEnum
    {
        /// <summary>
        /// Vendor审批
        /// </summary>
        [Description("AgentReview")]
        AgentReview = 1,

        /// <summary>
        /// Mp_App审批
        /// </summary>
        [Description("MpAppReview")]
        MpAppReview = 2,

        /// <summary>
        /// 用户注册审批
        /// </summary>
        [Description("UserRegistrationReview")]
        UserRegistrationReview = 3,

        /// <summary>
        /// Mp_Mcp审批
        /// </summary>
        [Description("MpMcpReview")]
        MpMcpReview = 4
    }
}
