using System.Net;
using System.Net.Mail;
using System.Text;

namespace AgentCentral.Infrastructure.Email
{
    public class EmailHelper
    {
        /// <summary>
        /// 发送邮件方法
        /// </summary>
        /// <param name="Subject">邮件标题</param>
        /// <param name="Body">邮件内容</param>
        /// <param name="FromMail">发件人邮箱</param>
        /// <param name="ToMail">收件人邮箱(多个收件人地址用";"号隔开)</param>
        /// <param name="AuthorizationCode">发件人授权码（需要通过在邮箱设置中获取）</param>
        /// <param name="Host">邮件服务器地址</param>
        /// <param name="ReplyTo">对方回复邮件时默认的接收地址（不设置也是可以的）</param>
        /// <param name="CCMail">//邮件的抄送者(多个抄送人用";"号隔开)（不设置也是可以的）</param>
        /// <param name="File_Path">附件的地址（不设置也是可以的）</param>
        public static bool SendMail(string Subject, string Body, string FromMail, string ToMail, string AuthorizationCode, string Host = null, string ReplyTo = null, string CCMail = null, string File_Path = null)
        {
            try
            {
                //实例化一个发送邮件类。
                MailMessage mailMessage = new()
                {
                    IsBodyHtml = true,//邮件正文是否是HTML格式
                    SubjectEncoding = Encoding.UTF8,//如果你的邮件标题包含中文，这里一定要指定，否则对方收到的极有可能是乱码。
                    BodyEncoding = Encoding.UTF8,
                    //邮件的优先级，分为 Low, Normal, High，通常用 Normal即可
                    Priority = MailPriority.Normal,
                    //发件人邮箱地址。
                    From = new MailAddress(FromMail),
                    Subject = Subject,//邮件标题。
                    Body = Body,//邮件内容。
                };

                //收件人邮箱地址。需要群发就写多个
                //拆分邮箱地址
                List<string> ToMaillist = ToMail.Split(';').ToList();
                for (int i = 0; i < ToMaillist.Count; i++)
                {
                    mailMessage.To.Add(new MailAddress(ToMaillist[i]));  //收件人邮箱地址。
                }

                if (ReplyTo is "" or null)
                {
                    ReplyTo = FromMail;
                }

                //对方回复邮件时默认的接收地址(不设置也是可以的哟)
                mailMessage.ReplyToList.Add(new MailAddress(ReplyTo));

                if (CCMail is not "" and not null)
                {
                    List<string> CCMaillist = ToMail.Split(';').ToList();
                    for (int i = 0; i < CCMaillist.Count; i++)
                    {
                        //邮件的抄送者，支持群发
                        mailMessage.CC.Add(new MailAddress(CCMail));
                    }
                }

                //设置邮件的附件，将在客户端选择的附件先上传到服务器保存一个，然后加入到mail中
                if (File_Path is not "" and not null)
                {
                    //将附件添加到邮件
                    mailMessage.Attachments.Add(new Attachment(File_Path));
                    //获取或设置此电子邮件的发送通知。
                    mailMessage.DeliveryNotificationOptions = DeliveryNotificationOptions.OnSuccess;
                }

                //实例化一个SmtpClient类。
                SmtpClient client = new();

                #region 设置邮件服务器地址

                if (string.IsNullOrWhiteSpace(Host) == false)
                {
                    client.Host = Host;
                }
                else
                {
                    //在这里我使用的是163邮箱，所以是smtp.163.com，如果你使用的是qq邮箱，那么就是smtp.qq.com。
                    // client.Host = "smtp.163.com";
                    if (FromMail.Length != 0)
                    {
                        //根据发件人的邮件地址判断发件服务器地址   默认端口一般是25
                        string[] addressor = FromMail.Trim().Split(new char[] { '@', '.' });
                        client.Host = addressor[1] switch
                        {
                            "163" => "smtp.163.com",
                            "126" => "smtp.126.com",
                            "qq" => "smtp.qq.com",
                            "gmail" => "smtp.gmail.com",
                            "hotmail" => "smtp.live.com",//outlook邮箱
                            "foxmail" => "smtp.foxmail.com",
                            "sina" => "smtp.sina.com.cn",
                            _ => "smtp.office365.com",
                        };
                    }
                }

                #endregion 设置邮件服务器地址

                //使用安全加密连接。
                client.EnableSsl = true;
                //不和请求一块发送。
                client.UseDefaultCredentials = false;

                //验证发件人身份(发件人的邮箱，邮箱里的生成授权码);
                client.Credentials = new NetworkCredential(FromMail, AuthorizationCode);

                //如果发送失败，SMTP 服务器将发送 失败邮件告诉我
                mailMessage.DeliveryNotificationOptions = DeliveryNotificationOptions.OnFailure;
                //发送
                client.Send(mailMessage);
                Console.WriteLine("发送成功");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("发送失败" + ex.Message);
                return false;
            }
        }
    }
}