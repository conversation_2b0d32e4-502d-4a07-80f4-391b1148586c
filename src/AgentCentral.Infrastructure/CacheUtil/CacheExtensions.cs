using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using AgentCentral.Infrastructure.EnumUtil;

namespace AgentCentral.Infrastructure.CacheUtil
{
    public static class CacheExtensions
    {
        public static async Task<T> GetObjAsync<T>(this IDistributedCache distributedCache, Enum cacheEnum, object key)
        {
            var attribute = cacheEnum.GetAttribute<CachingAttribute>();
            string cacheKey = attribute?.KeyPrefix ?? "" + ":" + cacheEnum.ToString() + ":" + key.ToString();

            var value = await distributedCache.GetStringAsync(cacheKey);
            if (value == null)
            {
                return default;
            }
            else
            {
                return JsonConvert.DeserializeObject<T>(value);
            }
        }

        public static async Task SetObjAsync<T>(this IDistributedCache distributedCache, Enum cacheEnum, object key, T value)
        {
            //if (!EqualityComparer<T>.Default.Equals(value, default(T)))
            if (value != null)
            {
                DistributedCacheEntryOptions option = new DistributedCacheEntryOptions();

                var attribute = cacheEnum.GetAttribute<CachingAttribute>();
                if (attribute != null && attribute.TimeSpan > 0)
                {
                    option.SetSlidingExpiration(TimeSpan.FromSeconds(attribute.TimeSpan));
                }
                string cacheKey = attribute?.KeyPrefix ?? "" + ":" + cacheEnum.ToString() + ":" + key.ToString();
                await distributedCache.SetStringAsync(cacheKey, JsonConvert.SerializeObject(value), option);
            }
        }
    }
}