namespace AgentCentral.Infrastructure.CacheUtil
{
    public class CachingAttribute : Attribute
    {
        /// <summary>
        /// ���캯��
        /// </summary>
        /// <param name="keyPrefix">CacheKey��ǰ׺</param>
        /// <param name="timeSpan">����ʱ�䣨�룩</param>
        public CachingAttribute(string keyPrefix, int timeSpan)
        {
            KeyPrefix = keyPrefix;
            TimeSpan = timeSpan;
        }

        public int TimeSpan { get; }

        public string KeyPrefix { get; }
    }
}