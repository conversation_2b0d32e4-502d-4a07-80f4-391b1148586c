namespace AgentCentral.Infrastructure.Models
{
    /// <summary>
    /// 通用分页信息模型
    /// </summary>
    public class PageModel<T>
    {
        /// <summary>
        /// 当前页标
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 总页数
        /// </summary>
        public int PageCount { get; set; } = 0;

        /// <summary>
        /// 数据总数
        /// </summary>
        public int DataCount { get; set; } = 0;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { set; get; }

        /// <summary>
        /// 返回数据
        /// </summary>
        public T Data { get; set; }
    }
}