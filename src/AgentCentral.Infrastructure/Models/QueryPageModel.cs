namespace AgentCentral.Infrastructure.Models
{
    /// <summary>
    /// 通用分页检索模型
    /// </summary>
    public class QueryPageModel
    {
        private int _pageIndex = PageConsts.PageIndex;
        private int _pageSize = PageConsts.PageSize;

        /// <summary> 当前页 </summary>
        public int PageIndex
        {
            get { return _pageIndex; }
            set { _pageIndex = value <= 0 ? _pageIndex : value; }
        }

        /// <summary> 页容量 </summary>
        public int PageSize
        {
            get { return _pageSize; }
            set { _pageSize = value <= 0 ? _pageSize : value; }
        }
    }
}