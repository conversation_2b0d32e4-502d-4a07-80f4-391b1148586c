using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace AgentCentral.Infrastructure.RabbitMq.Extensions
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection AddRabbitMq(this IServiceCollection services, IConfigurationSection rabitmqSection)
        {
            return services
                 .Configure<RabbitMqOptions>(rabitmqSection)
                 .AddSingleton<IRabbitMqConnection>(provider =>
                 {
                     var options = provider.GetRequiredService<IOptions<RabbitMqOptions>>();
                     return RabbitMqConnection.GetInstance(options);
                 })
                 .AddSingleton<RabbitMqProducer>();
        }
    }
}