using System.Text;
using Newtonsoft.Json;
using RabbitMQ.Client;

namespace AgentCentral.Infrastructure.RabbitMq
{
    public class RabbitMqProducer : IDisposable
    {
        private readonly IModel _channel;

        public RabbitMqProducer(IRabbitMqConnection rabbitMqConnection)
        {
            _channel = rabbitMqConnection.Connection.CreateModel();
        }

        public virtual void BasicPublish<TMessage>(string exchange, string routingKey, TMessage message, IBasicProperties properties = null, bool mandatory = false)
        {
            var content = JsonConvert.SerializeObject(message);
            var body = Encoding.UTF8.GetBytes(content);
            _channel.BasicPublish(exchange, routingKey, mandatory, basicProperties: properties, body);
        }

        public virtual IBasicProperties CreateBasicProperties()
        {
            return _channel.CreateBasicProperties();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_channel != null)
                {
                    _channel.Dispose();
                }
            }
        }
    }
}