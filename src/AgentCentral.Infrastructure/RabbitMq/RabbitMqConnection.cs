using Microsoft.Extensions.Options;
using RabbitMQ.Client;

namespace AgentCentral.Infrastructure.RabbitMq
{
    public sealed class RabbitMqConnection : IRabbitMqConnection
    {
        private static volatile RabbitMqConnection _uniqueInstance;

        private static readonly object _lockObject = new();

        public IConnection Connection { get; private set; } = default!;

        private RabbitMqConnection()
        {
        }

        internal static RabbitMqConnection GetInstance(IOptions<RabbitMqOptions> options)
        {
            if (_uniqueInstance is null)
            {
                lock (_lockObject)
                {
                    if (_uniqueInstance is null)
                    {
                        _uniqueInstance = new RabbitMqConnection(options);
                    }
                }
            }
            return _uniqueInstance;
        }

        private RabbitMqConnection(IOptions<RabbitMqOptions> options)
        {
            var factory = new ConnectionFactory()
            {
                HostName = options.Value.HostName,
                VirtualHost = options.Value.VirtualHost,
                UserName = options.Value.UserName,
                Password = options.Value.Password,
                Port = options.Value.Port,
                //Rabbitmq集群必需加这两个参数
                AutomaticRecoveryEnabled = true
                //TopologyRecoveryEnabled=true
            };
            Connection = factory.CreateConnection();
        }
    }
}