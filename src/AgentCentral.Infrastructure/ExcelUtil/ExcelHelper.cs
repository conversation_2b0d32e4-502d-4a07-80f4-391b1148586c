using System.Collections.Concurrent;
using System.ComponentModel;
using System.Reflection;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using AgentCentral.Infrastructure.LogUtil;

namespace AgentCentral.Infrastructure.ExcelUtil
{
    public static class ExcelHelper<T> where T : new()
    {
        private static PropertyInfo[] propertyList;

        #region Excel导入

        /// <summary>
        /// 通过文件流导入excel
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="errors">模型校验出错的信息</param>
        /// <param name="ext">文件后缀</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static List<T> ImportFromExcel(Stream stream, out List<RowError> errors, string ext = ".xls")
        {
            errors = new List<RowError>();
            propertyList = GetProperties(typeof(T));

            IWorkbook iworkbook = null;
            ISheet sheet = null;
            switch (ext)
            {
                case ".xls":
                    iworkbook = new HSSFWorkbook(stream);
                    sheet = iworkbook.GetSheetAt(0);
                    break;

                case ".xlsx":
                    iworkbook = new XSSFWorkbook(stream);
                    sheet = iworkbook.GetSheetAt(0);
                    break;

                default:
                    errors.Add(new RowError(0, "Unsupported file format!", "0"));
                    return new List<T>();
            }

            if (sheet.LastRowNum == 0)
            {
                errors.Add(new RowError(0, "Upload data is empty!", "0"));
                return new List<T>();
            }

            List<T> list = new List<T>(sheet.LastRowNum);
            try
            {
                IRow columnRow = sheet.GetRow(0); //第1行为字段名
                Dictionary<int, PropertyInfo> mapPropertyInfoDict = new Dictionary<int, PropertyInfo>();
                for (int j = 0; j < columnRow.LastCellNum; j++)
                {
                    ICell cell = columnRow.GetCell(j);
                    PropertyInfo propertyInfo = MapPropertyInfo(cell.ParseToString());
                    if (propertyInfo != null)
                    {
                        mapPropertyInfoDict.Add(j, propertyInfo);
                    }
                }
                if (mapPropertyInfoDict.Count <= 0)
                {
                    errors.Add(new RowError(0, "Please use the correct template!", "0"));
                    return list;
                }

                for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                    {
                        continue;
                    }

                    T entity = new T();
                    for (int j = 0; j < columnRow.LastCellNum; j++)
                    {
                        if (mapPropertyInfoDict.ContainsKey(j))
                        {
                            ICell cell = row.GetCell(j);
                            PropertyInfo propertyInfo = mapPropertyInfoDict[j];
                            ExcelColumnAttribute attributes = (ExcelColumnAttribute)propertyInfo.GetCustomAttribute(typeof(ExcelColumnAttribute), false);
                            Type type = propertyInfo.PropertyType;
                            if (attributes != null && attributes.MapType != null)
                            {
                                type = attributes.MapType;
                            }

                            try
                            {
                                object convertedValue = CellValueConverter.ConvertCellValue(type, cell);
                                if (convertedValue != null && propertyInfo.PropertyType != typeof(string))
                                {
                                    propertyInfo.SetValue(entity, convertedValue);
                                }
                                else
                                {
                                    propertyInfo.SetValue(entity, convertedValue?.ToString());
                                }
                            }
                            catch (Exception exc)
                            {
                                errors.Add(new RowError(row.RowNum, exc.Message, attributes == null ? propertyInfo.Name : attributes.Name));
                            }
                        }
                    }
                    list.Add(entity);
                }
            }
            catch (Exception ee)
            {
                Log.Error(ee, ee.Message);
                throw;
            }
            finally
            {
                iworkbook?.Close();
            }
            return list;
        }

        public static List<T> ImportFromWorkbook(IWorkbook iworkbook, string sheetName, string ext = ".xls")
        {
            propertyList = GetProperties(typeof(T));
            ISheet sheet = iworkbook.GetSheet(sheetName);
            List<T> list = new List<T>(sheet.LastRowNum);
            try
            {
                IRow columnRow = sheet.GetRow(0); //第1行为字段名
                Dictionary<int, PropertyInfo> mapPropertyInfoDict = new Dictionary<int, PropertyInfo>();
                for (int j = 0; j < columnRow.LastCellNum; j++)
                {
                    ICell cell = columnRow.GetCell(j);
                    PropertyInfo propertyInfo = MapPropertyInfoIgnoreCase(cell.ParseToString());
                    if (propertyInfo != null)
                    {
                        mapPropertyInfoDict.Add(j, propertyInfo);
                    }
                }
                if (mapPropertyInfoDict.Count <= 0)
                {
                    return list;
                }

                for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                    {
                        continue;
                    }

                    T entity = new T();
                    for (int j = 0; j < columnRow.LastCellNum; j++)
                    {
                        if (mapPropertyInfoDict.ContainsKey(j))
                        {
                            ICell cell = row.GetCell(j);
                            PropertyInfo propertyInfo = mapPropertyInfoDict[j];
                            ExcelColumnAttribute attributes = (ExcelColumnAttribute)propertyInfo.GetCustomAttribute(typeof(ExcelColumnAttribute), false);
                            Type type = propertyInfo.PropertyType;
                            if (attributes != null && attributes.MapType != null)
                            {
                                type = attributes.MapType;
                            }

                            if (Nullable.GetUnderlyingType(type) != null && cell.ToString().ToLower() == "null")
                            {
                                propertyInfo.SetValue(entity, null);
                            }
                            else
                            {
                                object convertedValue = CellValueConverter.ConvertCellValue(type, cell);
                                if (convertedValue != null && propertyInfo.PropertyType != typeof(string))
                                {
                                    propertyInfo.SetValue(entity, convertedValue);
                                }
                                else
                                {
                                    propertyInfo.SetValue(entity, convertedValue?.ToString());
                                }
                            }
                        }
                    }
                    list.Add(entity);
                }
            }
            catch (Exception ee)
            {
                Log.Error(ee, ee.Message);
                throw;
            }
            finally
            {
                iworkbook?.Close();
            }
            return list;
        }

        public static Stream ExportExcel(List<T> list, string ext = ".xlsx")
        {
            propertyList = GetProperties(typeof(T));
            IWorkbook iworkbook = null;
            ISheet sheet = null;
            switch (ext)
            {
                case ".xls":
                    iworkbook = new HSSFWorkbook();
                    sheet = iworkbook.CreateSheet();
                    break;

                case ".xlsx":
                    iworkbook = new XSSFWorkbook();
                    sheet = iworkbook.CreateSheet();
                    break;

                default:
                    return null;
            }

            IRow columnRow = sheet.CreateRow(0);
            for (int i = 1; i <= list.Count; i++)
            {
                IRow Row = sheet.CreateRow(i);
                SetRowValue(sheet, Row, list[i - 1]);
            }

            SetRowValue(sheet, columnRow, default);

            MemoryStream file = new MemoryStream();
            iworkbook.Write(file, true);
            iworkbook.Close();

            file.Flush();
            file.Seek(0, SeekOrigin.Begin);
            return file;
        }

        private static void SetCellValue(ICell cell, PropertyInfo property, T entity, ExcelColumnAttribute attributes)
        {
            object val = property.GetValue(entity);
            Type type = property.PropertyType;
            if (attributes != null && attributes.MapType != null)
            {
                type = attributes.MapType;
            }
            if (val != null)
            {
                if (type.IsEnum)
                {
                    bool isResult = Enum.TryParse(type, val.ToString(), out var result);
                    string Description = string.Empty;
                    if (isResult)
                    {
                        var desc = (DescriptionAttribute)result.GetType().GetField(result.ToString())?.GetCustomAttribute(typeof(DescriptionAttribute), false);
                        if (desc != null && !string.IsNullOrEmpty(desc.Description))
                        {
                            Description = desc.Description;
                        }
                        else
                        {
                            Description = result.ToString();
                        }
                    }
                    cell.SetCellValue(Description);
                }
                else
                {
                    if (!string.IsNullOrEmpty(attributes.Format))
                    {
                        val = CellValueConverter.FormatCellValue(type, val, attributes.Format);
                    }
                    cell.SetCellValue(val.ToString());
                }
            }
        }

        private static void SetRowValue(ISheet sheet, IRow row, T entity)
        {
            int currentColumnIndex = -1;
            for (int i = 0; i < propertyList.Length; i++)
            {
                PropertyInfo property = propertyList[i];
                ExcelColumnAttribute attributes = (ExcelColumnAttribute)property.GetCustomAttribute(typeof(ExcelColumnAttribute), false);
                if (attributes != null && !string.IsNullOrEmpty(attributes.Name))
                {
                    currentColumnIndex++;
                    ICell cell = default;
                    if (attributes.ExportColumnIndex != -1)
                    {
                        cell = row.CreateCell(attributes.ExportColumnIndex);
                    }
                    else
                    {
                        cell = row.CreateCell(currentColumnIndex);
                    }

                    if (entity != null)
                    {
                        SetCellValue(cell, property, entity, attributes);
                    }
                    else
                    {
                        sheet.SetColumnWidth(cell.ColumnIndex, Math.Max(sheet.GetColumnWidth(cell.ColumnIndex) + 150, 256 * 15));
                        cell.SetCellValue(attributes.Name);
                    }
                }
            }
        }

        /// <summary>
        /// 查找Excel列名对应的实体属性
        /// </summary>
        /// <param name="columnName"></param>
        /// <returns></returns>
        private static PropertyInfo MapPropertyInfo(string columnName)
        {
            PropertyInfo propertyInfo = propertyList.FirstOrDefault(p => p.Name == columnName);
            if (propertyInfo != null)
            {
                return propertyInfo;
            }
            else
            {
                foreach (PropertyInfo tempPropertyInfo in propertyList)
                {
                    ExcelColumnAttribute[] attributes = (ExcelColumnAttribute[])tempPropertyInfo.GetCustomAttributes(typeof(ExcelColumnAttribute), false);
                    if (attributes.Length > 0)
                    {
                        if (attributes[0].Name == columnName)
                        {
                            return tempPropertyInfo;
                        }
                    }
                }
            }
            return null;
        }

        private static PropertyInfo MapPropertyInfoIgnoreCase(string columnName)
        {
            PropertyInfo propertyInfo = propertyList.FirstOrDefault(p => p.Name.ToLower() == columnName.ToLower());
            if (propertyInfo != null)
            {
                return propertyInfo;
            }
            else
            {
                foreach (PropertyInfo tempPropertyInfo in propertyList)
                {
                    ExcelColumnAttribute[] attributes = (ExcelColumnAttribute[])tempPropertyInfo.GetCustomAttributes(typeof(ExcelColumnAttribute), false);
                    if (attributes.Length > 0)
                    {
                        if (attributes[0].Name.ToLower() == columnName.ToLower())
                        {
                            return tempPropertyInfo;
                        }
                    }
                }
            }
            return null;
        }

        private static ConcurrentDictionary<string, object> dictCache = new ConcurrentDictionary<string, object>();

        #region 得到类里面的属性集合

        /// <summary>
        /// 得到类里面的属性集合
        /// </summary>
        /// <param name="type"></param>
        /// <param name="columns"></param>
        /// <returns></returns>
        private static PropertyInfo[] GetProperties(Type type, string[] columns = null)
        {
            PropertyInfo[] properties = null;
            if (dictCache.ContainsKey(type.FullName))
            {
                properties = dictCache[type.FullName] as PropertyInfo[];
            }
            else
            {
                properties = type.GetProperties();
                dictCache.TryAdd(type.FullName, properties);
            }

            if (columns != null && columns.Length > 0)
            {
                //  按columns顺序返回属性
                var columnPropertyList = new List<PropertyInfo>();
                foreach (var column in columns)
                {
                    var columnProperty = properties.Where(p => p.Name == column).FirstOrDefault();
                    if (columnProperty != null)
                    {
                        columnPropertyList.Add(columnProperty);
                    }
                }
                return columnPropertyList.ToArray();
            }
            else
            {
                return properties;
            }
        }

        #endregion 得到类里面的属性集合

        #endregion Excel导入
    }
}