using System.Reflection;

namespace AgentCentral.Infrastructure.ExcelUtil
{
    public static partial class Extensions
    {
        #region 转换为long

        /// <summary>
        /// 将string转换为long，若转换失败，则返回默认值。不抛出异常。
        /// </summary>
        /// <param name="str"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static long ParseToLong(this string str)
        {
            long.TryParse(str, out long defaultValue);
            return defaultValue;
        }

        #endregion 转换为long

        #region 转换为int

        /// <summary>
        /// 将string转换为int，若转换失败，则返回默认值。不抛出异常。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static int ParseToInt(this string str)
        {
            int.TryParse(str, out int defaultValue);
            return defaultValue;
        }

        #endregion 转换为int

        public static byte ParseToByte(this string str)
        {
            byte.TryParse(str, out byte defaultValue);
            return defaultValue;
        }

        public static short ParseToInt16(this string str)
        {
            short.TryParse(str, out short defaultValue);
            return defaultValue;
        }

        #region 转换为demical

        /// <summary>
        /// 将string转换为demical，若转换失败，则返回默认值。不抛出异常。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static decimal ParseToDecimal(this string str)
        {
            decimal.TryParse(str, out decimal defaultValue);
            return defaultValue;
        }

        #endregion 转换为demical

        #region 转化为bool

        /// <summary>
        /// 将string转换为bool，若转换失败，则返回默认值。不抛出异常。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool ParseToBool(this string str)
        {
            bool.TryParse(str, out bool defaultValue);
            return defaultValue;
        }

        #endregion 转化为bool

        /// <summary>
        /// 将string转换为DateTime，若转换失败，则返回日期默认值。不抛出异常。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static DateTimeOffset ParseToDateTimeOffset(this string str)
        {
            DateTimeOffset.TryParse(str, out DateTimeOffset result);
            return result;
        }

        #region 转换为string

        /// <summary>
        /// 将NPOI.SS.UserModel.ICell或null转换为string，若转换失败，则返回""。不抛出异常。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string ParseToString(this NPOI.SS.UserModel.ICell obj)
        {
            if (obj == null)
                return string.Empty;
            try
            {
                return obj.ToString();
            }
            catch (Exception)
            {
            }
            return string.Empty;
        }

        #endregion 转换为string

        #region 转换为double

        /// <summary>
        /// 将string转换为double，若转换失败，则返回默认值。不抛出异常。
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static double ParseToDouble(this string str)
        {
            double.TryParse(str, out double defaultValue);
            return defaultValue;
        }

        #endregion 转换为double

        public static ExcelColumnAttribute GetColumn<T>(this T str, string name) where T : class
        {
            return str.GetType().GetProperty(name).GetCustomAttribute<ExcelColumnAttribute>();
        }
    }
}