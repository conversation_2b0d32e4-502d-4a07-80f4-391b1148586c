using NPOI.SS.UserModel;
using AgentCentral.Infrastructure.EnumUtil;

namespace AgentCentral.Infrastructure.ExcelUtil
{
    public class CellValueConverter
    {
        public static object FormatCellValue(Type type, object cell, string format)
        {
            if (type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?))
            {
                return cell.ToString().ParseToDateTimeOffset().ToString(format);
            }
            return cell;
        }

        public static object ConvertCellValue(Type type, ICell cell)
        {
            if (cell == null)
                return null;

            if (cell.CellType == CellType.String)
            {
                if (type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?))
                {
                    return cell.ParseToString().ParseToDateTimeOffset();
                }
                var value = cell.StringCellValue;
                object obj = ParseStringToType(type, value);
                if (obj is null)
                {
                    throw new InvalidCastException($"This value is invalid");
                }
                return obj;
            }
            else if (cell.CellType == CellType.Numeric && (type == typeof(DateTime) || type == typeof(DateTime?)))
            {
                return cell.DateCellValue != default ? cell.DateCellValue : DateTime.FromOADate(cell.NumericCellValue);
            }
            else if (cell.CellType == CellType.Boolean)
            {
                return cell.BooleanCellValue;
            }
            else if (cell.CellType == CellType.Numeric)
            {
                if (type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?))
                {
                    return new DateTimeOffset(cell.DateCellValue);
                }
                double numericValue = cell.NumericCellValue;
                try
                {
                    return ConvertHelper.ChangeType(numericValue, type);
                }
                catch
                {
                    throw new InvalidCastException($"This value is invalid");
                }
            }

            return null;
        }

        private static object ParseStringToType(Type targetType, string value)
        {
            if (targetType.IsEnum)
            {
                if (targetType == typeof(Enum))
                {
                    if (Enum.TryParse(targetType, value, out var val))
                    {
                        return val;
                    }
                }
                return targetType.GetValueByDesc(value);
            }
            else
            {
                return ConvertHelper.ChangeType(value, targetType);
            }
        }
    }
}