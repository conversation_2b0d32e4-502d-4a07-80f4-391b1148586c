using Newtonsoft.Json;
using AgentCentral.Infrastructure.LogUtil;
using System.Text;

namespace AgentCentral.Infrastructure.HttpUtil
{
    public static class HttpClientExtensions
    {
        public static async Task PostJsonAsync<T>(this HttpClient httpClient, string url, T requestBody)
        {
            string json = JsonConvert.SerializeObject(requestBody);
            StringContent content = new(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = await httpClient.PostAsync(url, content);
            _ = response.EnsureSuccessStatusCode();
        }

        public static async Task<TResponse> GetObjectAsync<TResponse>(this HttpClient httpClient, string url, Dictionary<string, string> headers = default)
        {
            string result = string.Empty;
            try
            {
                HttpRequestMessage request = new(HttpMethod.Get, url);

                if (headers != null)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }

                HttpResponseMessage response = await httpClient.SendAsync(request);
                result = await response.Content.ReadAsStringAsync();
                _ = response.EnsureSuccessStatusCode();
                return JsonConvert.DeserializeObject<TResponse>(result);
            }
            catch (HttpRequestException httpex)
            {
                Log.Error(httpex, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Statuscode_{httpex.StatusCode}_Exception_{httpex.Message}");
                if (!string.IsNullOrEmpty(result))
                {
                    return JsonConvert.DeserializeObject<TResponse>(result);
                }
            }
            catch (Exception ext)
            {
                Log.Error(ext, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Exception_{ext.Message}");
            }
            return default;
        }

        public static async Task<TResponse> PostJsonAsync<TResponse>(this HttpClient httpClient, string url, object requestBody, Dictionary<string, string> headers = default, JsonSerializerSettings requestSettings = null, JsonSerializerSettings responseSettings = null, CancellationToken cancellationToken = default)
        {
            string result = string.Empty;
            try
            {
                string json = requestBody is string data ? data : JsonConvert.SerializeObject(requestBody, requestSettings);

                StringContent content = new(json, Encoding.UTF8, "application/json");

                HttpRequestMessage request = new(HttpMethod.Post, url)
                {
                    Content = content
                };

                if (headers != default)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        _ = request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                result = await response.Content.ReadAsStringAsync(cancellationToken);
                _ = response.EnsureSuccessStatusCode();
                Log.Information("client api response" + result);
                return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
            }
            catch (HttpRequestException httpex)
            {
                Log.Error(httpex, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Statuscode_{httpex.StatusCode}_Exception_{httpex.Message}");
                if (!string.IsNullOrEmpty(result))
                {
                    return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
                }
            }
            catch (Exception ext)
            {
                Log.Error(ext, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Exception_{ext.Message}");
            }

            return default;
        }

        public static async Task<TResponse> PutJsonAsync<TResponse>(this HttpClient httpClient, string url, object requestBody, Dictionary<string, string> headers = default, JsonSerializerSettings requestSettings = null, JsonSerializerSettings responseSettings = null, CancellationToken cancellationToken = default)
        {
            string result = string.Empty;
            try
            {
                string json = requestBody is string data ? data : JsonConvert.SerializeObject(requestBody, requestSettings);

                StringContent content = new(json, Encoding.UTF8, "application/json");

                HttpRequestMessage request = new(HttpMethod.Put, url)
                {
                    Content = content
                };

                if (headers != default)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        _ = request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                result = await response.Content.ReadAsStringAsync(cancellationToken);

                _ = response.EnsureSuccessStatusCode();

                return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
            }
            catch (HttpRequestException httpex)
            {
                Log.Error(httpex, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Statuscode_{httpex.StatusCode}_Exception_{httpex.Message}");
                if (!string.IsNullOrEmpty(result))
                {
                    return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
                }
            }
            catch (Exception ext)
            {
                Log.Error(ext, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Exception_{ext.Message}");
            }

            return default;
        }

        public static async Task<TResponse> DeleteJsonAsync<TResponse>(this HttpClient httpClient, string url, Dictionary<string, string> headers = default, JsonSerializerSettings responseSettings = null, CancellationToken cancellationToken = default)
        {
            string result = string.Empty;
            try
            {
                HttpRequestMessage request = new(HttpMethod.Delete, url);

                if (headers != default)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        _ = request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                result = await response.Content.ReadAsStringAsync(cancellationToken);

                _ = response.EnsureSuccessStatusCode();

                return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
            }
            catch (HttpRequestException httpex)
            {
                Log.Error(httpex, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Statuscode_{httpex.StatusCode}_Exception_{httpex.Message}");
                if (!string.IsNullOrEmpty(result))
                {
                    return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
                }
            }
            catch (Exception ext)
            {
                Log.Error(ext, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Exception_{ext.Message}");
            }

            return default;
        }

        public static async Task<TResponse> PostFormUrlEncodedAsync<TResponse>(
            this HttpClient httpClient,
            string url,
            Dictionary<string, string> formData,
            Dictionary<string, string> headers = null,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            string result = string.Empty;
            try
            {
                var content = new FormUrlEncodedContent(formData);
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, url);
                request.Content = content;

                if (headers != null)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                        request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }

                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                result = await response.Content.ReadAsStringAsync(cancellationToken);
                response.EnsureSuccessStatusCode();
                return JsonConvert.DeserializeObject<TResponse>(result);
            }
            catch (HttpRequestException ex)
            {
                if (!string.IsNullOrEmpty(result))
                    return JsonConvert.DeserializeObject<TResponse>(result);
                Log.Error(ex, $"Request to {httpClient.BaseAddress}/{url} failed with result: {result}");
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Request to {httpClient.BaseAddress}/{url} failed with exception: {ex.Message}");
            }
            return default(TResponse);
        }

        public static async Task<TResponse> PatchJsonAsync<TResponse>(this HttpClient httpClient, string url, object requestBody, Dictionary<string, string> headers = default, JsonSerializerSettings requestSettings = null, JsonSerializerSettings responseSettings = null, CancellationToken cancellationToken = default)
        {
            string result = string.Empty;
            try
            {
                string json = requestBody is string data ? data : JsonConvert.SerializeObject(requestBody, requestSettings);

                StringContent content = new(json, Encoding.UTF8, "application/json");

                HttpRequestMessage request = new(HttpMethod.Patch, url)
                {
                    Content = content
                };

                if (headers != default)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        _ = request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                result = await response.Content.ReadAsStringAsync(cancellationToken);
                _ = response.EnsureSuccessStatusCode();
                Log.Information("client api response" + result);
                return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
            }
            catch (HttpRequestException httpex)
            {
                Log.Error(httpex, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Statuscode_{httpex.StatusCode}_Exception_{httpex.Message}");
                if (!string.IsNullOrEmpty(result))
                {
                    return JsonConvert.DeserializeObject<TResponse>(result, responseSettings);
                }
            }
            catch (Exception ext)
            {
                Log.Error(ext, $"Request_{httpClient.BaseAddress}/{url}_result_{result}_Exception_{ext.Message}");
            }

            return default;
        }
        /// <summary>
        /// 发送POST请求并以SSE方式流式接收响应
        /// </summary>
        /// <typeparam name="TEvent">SSE事件类型</typeparam>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="url">请求URL</param>
        /// <param name="requestBody">请求体</param>
        /// <param name="onEventReceived">事件接收回调</param>
        /// <param name="headers">请求头</param>
        /// <param name="requestSettings">请求序列化设置</param>
        /// <param name="responseSettings">响应反序列化设置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public static async Task PostJsonStreamAsync<TEvent>(
            this HttpClient httpClient,
            string url,
            object requestBody,
            Func<string, TEvent, Task> onEventReceived,
            Dictionary<string, string> headers = default,
            JsonSerializerSettings requestSettings = null,
            JsonSerializerSettings responseSettings = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                string json = requestBody is string data ? data : JsonConvert.SerializeObject(requestBody, requestSettings);
                StringContent content = new(json, Encoding.UTF8, "application/json");

                HttpRequestMessage request = new(HttpMethod.Post, url)
                {
                    Content = content
                };

                // 设置Accept头为text/event-stream
                request.Headers.Accept.Clear();
                request.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("text/event-stream"));

                if (headers != null)
                {
                    foreach (KeyValuePair<string, string> header in headers)
                    {
                        _ = request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                using HttpResponseMessage response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
                _ = response.EnsureSuccessStatusCode();

                using Stream stream = await response.Content.ReadAsStreamAsync(cancellationToken);
                using StreamReader reader = new(stream);

                await ProcessServerSentEventsAsync(reader, onEventReceived, responseSettings, cancellationToken);
            }
            catch (HttpRequestException httpex)
            {
                Log.Error(httpex, $"SSE Request to {httpClient.BaseAddress}/{url} failed with status: {httpex.StatusCode}, message: {httpex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"SSE Request to {httpClient.BaseAddress}/{url} failed with exception: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理服务器发送事件流
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="reader">流读取器</param>
        /// <param name="onEventReceived">事件接收回调</param>
        /// <param name="responseSettings">响应反序列化设置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        private static async Task ProcessServerSentEventsAsync<TEvent>(
            StreamReader reader,
            Func<string, TEvent, Task> onEventReceived,
            JsonSerializerSettings responseSettings,
            CancellationToken cancellationToken)
        {
            string line;
            StringBuilder eventData = new();
            string eventType = null;

            while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
            {
                // 空行表示一个事件的结束
                if (string.IsNullOrEmpty(line))
                {
                    if (eventData.Length > 0)
                    {
                        await ProcessSingleEventAsync(eventData.ToString(), eventType, onEventReceived, responseSettings);
                        eventData.Clear();
                        eventType = null;
                    }
                    continue;
                }

                // 解析SSE格式
                if (line.StartsWith("data: "))
                {
                    string data = line.Substring(6); // 移除 "data: " 前缀
                    if (data == "[DONE]") // 流结束标志
                    {
                        break;
                    }
                    eventData.AppendLine(data);
                }
                else if (line.StartsWith("event: "))
                {
                    eventType = line.Substring(7); // 移除 "event: " 前缀
                }
                else if (line.StartsWith("id: ") || line.StartsWith("retry: "))
                {
                    // 暂时忽略id和retry字段
                    continue;
                }
                else
                {
                    // 其他格式的数据行
                    eventData.AppendLine(line);
                }
            }

            // 处理最后一个事件（如果存在）
            if (eventData.Length > 0)
            {
                await ProcessSingleEventAsync(eventData.ToString(), eventType, onEventReceived, responseSettings);
            }
        }

        /// <summary>
        /// 处理单个SSE事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="eventDataString">事件数据字符串</param>
        /// <param name="eventType">事件类型</param>
        /// <param name="onEventReceived">事件接收回调</param>
        /// <param name="responseSettings">响应反序列化设置</param>
        /// <returns>异步任务</returns>
        private static async Task ProcessSingleEventAsync<TEvent>(
            string eventDataString,
            string eventType,
            Func<string, TEvent, Task> onEventReceived,
            JsonSerializerSettings responseSettings)
        {
            try
            {
                TEvent eventObj = JsonConvert.DeserializeObject<TEvent>(eventDataString.Trim(), responseSettings);
                if (!object.Equals(eventObj, default(TEvent)))
                {
                    await onEventReceived(eventType, eventObj);
                }
            }
            catch (JsonException jsonEx)
            {
                Log.Error(jsonEx, $"Failed to deserialize SSE event data: {eventDataString}");
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Error processing SSE event: {ex.Message}");
            }
        }
    }
}