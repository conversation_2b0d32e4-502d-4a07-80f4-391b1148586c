using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Infrastructure.DataAnnotations
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class DateAttribute : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            if (value == null)
                return false;
            bool val = DateTimeOffset.TryParse(value.ToString(), out DateTimeOffset result);
            if (result == default)
            {
                return false;
            }
            return val;
        }
    }
}