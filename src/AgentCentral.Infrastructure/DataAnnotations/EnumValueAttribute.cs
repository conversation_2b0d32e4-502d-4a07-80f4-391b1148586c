using AgentCentral.Infrastructure.EnumUtil;
using System.ComponentModel.DataAnnotations;

namespace AgentCentral.Infrastructure.DataAnnotations
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class EnumValueAttribute : ValidationAttribute
    {
        public Type Type { get; set; }

        public override bool IsValid(object value)
        {
            if (value == null)
                return false;
            Enum @enum = Type.GetValueByDesc(value.ToString());
            if (@enum != null)
            {
                return Enum.IsDefined(Type, value);
            }
            return false;
        }
    }
}