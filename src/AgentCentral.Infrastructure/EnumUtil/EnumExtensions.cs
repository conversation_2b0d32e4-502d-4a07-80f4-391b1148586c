using AgentCentral.Infrastructure.DataAnnotations;
using System.ComponentModel;
using System.Reflection;

namespace AgentCentral.Infrastructure.EnumUtil
{
    public static class EnumExtensions
    {
        public static T GetAttribute<T>(this Enum enumItem) where T : Attribute
        {
            MemberInfo[] memberInfo = enumItem.GetType().GetMember(enumItem.ToString());
            return memberInfo[0].GetCustomAttribute<T>();
        }

        public static Dictionary<int, string> GetDescriptionItems(this Type enumType)
        {
            FieldInfo[] enumItems = enumType.GetFields(BindingFlags.Public | BindingFlags.Static);
            Dictionary<int, string> names = new(enumItems.Length);
            foreach (FieldInfo enumItem in enumItems)
            {
                object[] attributes = enumItem.GetCustomAttributes(typeof(DescriptionAttribute), false);
                string name = attributes.Length == 0 ? enumItem.Name : (attributes[0] as DescriptionAttribute).Description;
                names[(int)enumItem.GetValue(enumType)] = name;
            }
            return names;
        }

        public static Enum GetValueByDesc<T>(this string desc) where T : Enum
        {
            foreach (T item in Enum.GetValues(typeof(T)))
            {
                if (item.GetDescription() == desc.Trim())
                {
                    return item;
                }
            }
            return Enum.TryParse(typeof(T), desc.Trim(), out object result) ? (Enum)result : null;
        }

        public static Enum GetValueByDesc(this Type type, string desc)
        {
            foreach (object item in Enum.GetValues(type))
            {
                if (((Enum)item).GetDescription() == desc.Trim())
                {
                    return (Enum)item;
                }
            }
            return Enum.TryParse(type, desc.Trim(), out object result) ? (Enum)result : null;
        }

        public static string GetDescription(this Enum enumValue)
        {
            string value = enumValue.ToString();
            FieldInfo field = enumValue.GetType().GetField(value);
            if (field == null)
            {
                return value;
            }
            object[] objs = field.GetCustomAttributes(typeof(DescriptionAttribute), false);
            if (objs.Length == 0)
            {
                return value;
            }
            DescriptionAttribute descriptionAttribute = (DescriptionAttribute)objs[0];
            return descriptionAttribute.Description;
        }
        public static (string name, string value) GetEnumDisplay(this Enum enumValue)
        {
            string value = enumValue.ToString();
            FieldInfo field = enumValue.GetType().GetField(value);
            if (field == null)
            {
                return (string.Empty, string.Empty);
            }
            object[] objs = field.GetCustomAttributes(typeof(EnumDisplayAttribute), false);
            if (objs.Length == 0)
            {
                return (string.Empty, string.Empty);
            }
            EnumDisplayAttribute enumDisplayAttribute = (EnumDisplayAttribute)objs[0];
            return (enumDisplayAttribute.Name, enumDisplayAttribute.Value);
        }
    }
}