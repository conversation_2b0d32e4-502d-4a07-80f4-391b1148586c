using Asp.Versioning;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using AgentCentral.Infrastructure.OpenApi.Swagger;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace AgentCentral.Infrastructure.OpenApi.Extensions
{
    public static class SwaggerExtension
    {
        /// <summary>
        /// Add swagger to the service collection
        /// integrate xml comments
        //  if you use this, you need to add the following to your .csproj file:
        //  < PropertyGroup >
        //      < GenerateDocumentationFile > true </ GenerateDocumentationFile >
        //  </ PropertyGroup >
        /// </summary>
        /// <param name="serviceDescriptors"></param>
        /// <param name="hostType"></param>
        /// <returns></returns>
        public static IServiceCollection AddSwaggerGenExt(this IServiceCollection serviceDescriptors, Action<ApiVersioningOptions> setupAction = default)
        {
            //serviceDescriptors.AddApiVersioningExt(setupAction);//版本控制

            _ = serviceDescriptors.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
            _ = serviceDescriptors.AddSwaggerGen(
                options =>
                {
                    #region IncludeXmlComments

                    // add a custom operation filter which sets default values
                    options.OperationFilter<SwaggerDefaultValues>();

                    string filePath = Path.Combine(AppContext.BaseDirectory, $"{AppDomain.CurrentDomain.FriendlyName}.xml");
                    string modelFilePath = Path.Combine(AppContext.BaseDirectory, "AgentCentral.Application.Contracts.xml");

                    if (File.Exists(filePath))
                    {
                        options.IncludeXmlComments(filePath, true);
                    }
                    if (File.Exists(modelFilePath))
                    {
                        options.IncludeXmlComments(modelFilePath, true);
                    }

                    #endregion IncludeXmlComments

                    #region Authorization

                    options.AddSecurityDefinition("JwtBearerAuth", new OpenApiSecurityScheme()
                    {
                        Description = "Token",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.Http,
                        Scheme = "Bearer"
                    });

                    //options.AddSecurityDefinition("BNPAuth", new OpenApiSecurityScheme()
                    //{
                    //    Description = "BNPAuth",
                    //    Name = "Authorization",
                    //    In = ParameterLocation.Header,
                    //    Type = SecuritySchemeType.Http,
                    //    Scheme = "basic"
                    //});
                    //options.AddSecurityDefinition("ApiKeyAuth", new OpenApiSecurityScheme()
                    //{
                    //    Description = "ApiKeyAuth",
                    //    Name = "x-api-key",
                    //    In = ParameterLocation.Header,
                    //    Type = SecuritySchemeType.ApiKey,
                    //});

                    //声明一个Scheme，注意下面的Id要和上面AddSecurityDefinition中的参数name一致
                    OpenApiSecurityScheme scheme = new()
                    {
                        Reference = new OpenApiReference() { Type = ReferenceType.SecurityScheme, Id = "JwtBearerAuth" }
                    };
                    //注册全局认证（所有的接口都可以使用认证）, 添加ApiKey验证支持
                    options.AddSecurityRequirement(new OpenApiSecurityRequirement()
                    {
                        [scheme] = Array.Empty<string>()
                    });

                    #endregion Authorization

                    options.CustomSchemaIds(CustomSchemaIdSelector);
                    static string CustomSchemaIdSelector(Type modelType)
                    {
                        if (!modelType.IsConstructedGenericType)
                        {
                            return modelType.FullName.Replace("[]", "Array");
                        }

                        string prefix = modelType.GetGenericArguments()
                            .Select(genericArg => CustomSchemaIdSelector(genericArg))
                            .Aggregate((previous, current) => previous + current);

                        return prefix + modelType.FullName.Split('`').First();
                    }
                });

            return serviceDescriptors;
        }

        public static WebApplication UseSwaggerExt(this WebApplication app, IConfigurationRoot config)
        {
            //if (app.Environment.IsDevelopment())
            //{
            //虚拟目录名称（当部署到iis时作为子应用时，需要添加虚拟目录名称作为前缀才能正常访问swagger）
            string virtualPrefix = config["virtualPrefix"];
            virtualPrefix = string.IsNullOrEmpty(virtualPrefix) ? string.Empty : $"/{virtualPrefix}";

            _ = app.UseSwagger();
            _ = app.UseSwaggerUI(
                options =>
                {
                    options.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
                    options.DisplayRequestDuration(); // 显示请求持续时间

                    //var descriptions = app.DescribeApiVersions();

                    //// build a swagger endpoint for each discovered API version
                    //foreach (var description in descriptions)
                    //{
                    //    var url = $"{virtualPrefix}/swagger/{description.GroupName}/swagger.json";
                    //    var name = description.GroupName.ToUpperInvariant();
                    //    options.SwaggerEndpoint(url, name);
                    //    options.DisplayRequestDuration(); // 显示请求持续时间
                    //}
                });
            //}
            return app;
        }
    }
}