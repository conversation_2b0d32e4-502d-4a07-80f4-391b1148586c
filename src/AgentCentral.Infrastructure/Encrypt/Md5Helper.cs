using System.Security.Cryptography;
using System.Text;

namespace AgentCentral.Infrastructure.Encrypt
{
    public static class Md5Helper
    {
        public static string Md5(string srcString, bool isLower = false, int length = 32)
        {
            string str_md5_out = string.Empty;
            using MD5 md5 = MD5.Create();
            byte[] bytes_md5_in = Encoding.UTF8.GetBytes(srcString);
            byte[] bytes_md5_out = md5.ComputeHash(bytes_md5_in);

            str_md5_out = length == 32
                ? BitConverter.ToString(bytes_md5_out)
                : BitConverter.ToString(bytes_md5_out, 4, 8);

            str_md5_out = str_md5_out.Replace("-", "");
            return isLower ? str_md5_out.ToLower() : str_md5_out;
        }

        public static string Md5HMAC(string srcString, string key, bool isLower = false)
        {
            byte[] secrectKey = Encoding.UTF8.GetBytes(key);
            using HMACMD5 md5 = new HMACMD5(secrectKey);
            byte[] bytes_md5_in = Encoding.UTF8.GetBytes(srcString);
            byte[] bytes_md5_out = md5.ComputeHash(bytes_md5_in);
            string str_md5_out = BitConverter.ToString(bytes_md5_out);
            str_md5_out = str_md5_out.Replace("-", "");
            return isLower ? str_md5_out.ToLower() : str_md5_out;
        }
    }
}