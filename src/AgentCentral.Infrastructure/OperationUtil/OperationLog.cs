using System.Text;

namespace AgentCentral.Infrastructure.OperationUtil
{
    public class OperationLog
    {
        public static string OperationRecord<T>(T oldWod, T newWod, OperationType operation)
        {
            StringBuilder builder = new StringBuilder();
            switch (operation)
            {
                case OperationType.Update:
                    builder.AppendLine($"edit:");
                    break;

                default:
                    break;
            }
            oldWod.GetType().GetProperties().ToList().ForEach(x =>
            {
                var oldValue = x.GetValue(oldWod);
                switch (operation)
                {
                    case OperationType.Update:
                        var newValue = x.GetValue(newWod);
                        if (oldValue == null)
                        {
                            builder.AppendLine($"{x.Name}=  add  =>{newValue}");
                        }
                        else
                        {
                            if (!oldValue.Equals(newValue))
                            {
                                builder.AppendLine($"{x.Name} was changed from {oldValue} to {newValue}");
                            }
                        }
                        break;

                    default:
                        break;
                }
            });
            return builder.ToString();
        }
    }

    public enum OperationType
    {
        Add = 1,
        Update = 2
    }
}