using AgentCentral.Infrastructure.Redis;
using AgentCentral.Infrastructure.Redis.Enum;
using Microsoft.Extensions.Options;
using Serilog;

namespace AgentCentral.Infrastructure
{
    public class SqlSugarWorkIdProvider
    {
        private readonly IRedisService _redisService;

        private readonly WorkIdOptions _options;

        private static string ContainerName = Environment.MachineName;

        public SqlSugarWorkIdProvider(IRedisService redisService, IOptions<WorkIdOptions> options)
        {
            _redisService = redisService;
            _options = options.Value;
        }

        public async Task<int> GetWorkIdAsync()
        {
            Log.Information("ContainerName:" + ContainerName);
            var workIdStr = await _redisService.StringGetAsync(RedisDataType.Token, $"{_options.WorkIdKeyPrefix}:{ContainerName}");
            if (!string.IsNullOrEmpty(workIdStr) && int.TryParse(workIdStr, out int workId))
            {
                return workId;
            }

            workId = (int)await _redisService.StringIncrementAsync(RedisDataType.Token, $"{_options.WorkIdKeyPrefix}");
            await _redisService.StringSetAsync(RedisDataType.Token, $"{_options.WorkIdKeyPrefix}:{ContainerName}", workId.ToString());

            return workId;
        }
    }
}
