namespace AgentCentral.Infrastructure
{
    /// <summary>
    /// 表示每次请求都会创建一个新的服务实例，并且每次请求都使用不同的实例
    /// </summary>
    public interface ITransientService
    {
    }

    /// <summary>
    /// 表示每个请求或操作都会创建一个新的服务实例，并且在整个请求或操作期间使用同一个实例
    /// </summary>
    public interface IScopedService
    {
    }

    /// <summary>
    /// 表示在整个应用程序生命周期中只会创建一个服务实例，并且所有请求都会使用同一个实例
    /// </summary>
    public interface ISingletonService
    {
    }
}