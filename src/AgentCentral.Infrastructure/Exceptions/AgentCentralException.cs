namespace AgentCentral.Infrastructure.Exceptions
{
    public class AgentCentralException : Exception
    {
        public ErrorCodeEnum Code { get; }
        public override string Message { get; }

        public AgentCentralException(string message)
        {
            Code = ErrorCodeEnum.SystemError;
            Message = message;
        }

        public AgentCentralException(ErrorCodeEnum code, string message)
        {
            Code = code;
            Message = message;
        }
    }
}