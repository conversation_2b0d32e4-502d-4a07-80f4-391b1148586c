namespace AgentCentral.Infrastructure.Exceptions
{
    public enum ErrorCodeEnum
    {
        /// <summary>
        /// System exception
        /// </summary>
        SystemError = 1000,

        /// <summary>
        /// Incorrect request, protocol, or parameter
        /// </summary>
        BadReqeust = 1001,

        /// <summary>
        /// User authentication failure
        /// </summary>
        AuthenticationFail = 1002,

        /// <summary>
        /// Data decryption error
        /// </summary>
        DecryptError = 1010,//

        /// <summary>
        /// Incorrect parameter
        /// </summary>
        ParamInvalid = 1100,

        /// <summary>
        /// The data format is incorrect
        /// </summary>
        DataFormatInvalid = 1101,

        /// <summary>
        /// Upload file too large
        /// </summary>
        UploadFileTooLarge = 1102,

        /// <summary>
        /// Login failure
        /// </summary>
        LoginFail = 1103,

        /// <summary>
        /// Refresh Token
        /// </summary>
        RefreshTokenFail = 1104,

        /// <summary>
        /// The entry object is empty
        /// </summary>
        ParamIsNullError = 1105,

        /// <summary>
        /// No related information is found in the database. Procedure
        /// </summary>
        DataIsNullError = 1106,

        /// <summary>
        /// Incorrect data status
        /// </summary>
        DataStatusError = 1107,

        /// <summary>
        /// Data already exists
        /// </summary>
        DataAlreadyExists = 1108,

        /// <summary>
        /// Service exception
        /// </summary>
        BusinessError = 1109,

        /// <summary>
        /// The uploaded data exceeds the limit
        /// </summary>
        Datalimit = 1110,

        /// <summary>
        /// Unsupported file types
        /// </summary>
        UnsupportedfileUploadTypes = 1111,

        /// <summary>
        /// 404
        /// </summary>
        NotFound = 1112,

        /// <summary>
        ///
        /// </summary>
        DataIsNullOrEmpty = 1113,

        NoBusinessError = 0001,
        CreateFacilityFail = 1114,
        StripeAccountActiveFail = 1115,
    }
}