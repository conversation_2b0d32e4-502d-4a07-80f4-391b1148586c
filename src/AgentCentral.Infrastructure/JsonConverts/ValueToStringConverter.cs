using Newtonsoft.Json;

namespace AgentCentral.Infrastructure.JsonConverts
{
    public class ValueToStringConverter : JsonConverter
    {
        public override bool CanRead => false;

        public override bool CanConvert(Type objectType)
        {
            return objectType.IsValueType;
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            throw new NotSupportedException();
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, object value, JsonSerializer serializer)
        {
            string value2 = value?.ToString();
            writer.WriteValue(value2);
        }
    }
}