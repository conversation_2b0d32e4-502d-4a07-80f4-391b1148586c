using System.Globalization;
using Newtonsoft.Json;

namespace AgentCentral.Infrastructure.JsonConverts
{
    /// <summary>
    /// 用法：在DTP上加上特性 [JsonConverter(typeof(JsonConverterDateTimeOffset), "yyyy-MM-dd HH:mm:ss")]
    /// </summary>
    public class JsonConverterDateTimeOffset : JsonConverter<DateTimeOffset>
    {
        private readonly string _dateFormat;

        public JsonConverterDateTimeOffset(string writeFormatter)
        {
            _dateFormat = writeFormatter;
        }

        public override DateTimeOffset ReadJson(JsonReader reader, Type objectType, DateTimeOffset existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            return existingValue;
        }

        public override void WriteJson(JsonWriter writer, DateTimeOffset value, JsonSerializer serializer)
        {
            if (value == DateTimeOffset.MinValue)
            {
                writer.WriteValue(string.Empty);
            }
            else
            {
                writer.WriteValue(value.ToString(_dateFormat, CultureInfo.InvariantCulture));
            }
        }
    }
}