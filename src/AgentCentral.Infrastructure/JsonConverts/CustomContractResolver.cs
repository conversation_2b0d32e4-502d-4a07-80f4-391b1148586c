using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using AgentCentral.Infrastructure.DataAnnotations;
using System.Reflection;
using Microsoft.AspNetCore.Http;

namespace AgentCentral.Infrastructure.JsonConverts
{
    public class CustomContractResolver : CamelCasePropertyNamesContractResolver
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CustomContractResolver(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        ///// <summary>
        ///// 实现首字母小写
        ///// </summary>
        ///// <param name="propertyName"></param>
        ///// <returns></returns>
        //protected override string ResolvePropertyName(string propertyName)
        //{
        //    return propertyName.Substring(0, 1).ToLower() + propertyName.Substring(1);
        //}

        /// <summary>
        /// 对长整型做处理
        /// </summary>
        /// <param name="objectType"></param>
        /// <returns></returns>
        protected override JsonConverter ResolveContractConverter(Type objectType)
        {
            return objectType == typeof(long) ? new JsonConverterLong() : base.ResolveContractConverter(objectType);
        }

        protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
        {
            JsonProperty jsonProperty = base.CreateProperty(member, memberSerialization);

            if (jsonProperty.PropertyType == typeof(decimal))
            {
                Attribute attr = jsonProperty.AttributeProvider.GetAttributes(false).FirstOrDefault(x => x.GetType() == typeof(DecimalAttribute));
                jsonProperty.Converter = attr != null ? new JsonConverterDecimal(4) : (JsonConverter)new JsonConverterDecimal(2);
            }

            // 检查是否有EmailDesensitize特性
            if (member.GetCustomAttribute<EmailDesensitizeAttribute>() != null)
            {
                var originalValueGetter = jsonProperty.ValueProvider;
                jsonProperty.Converter = new EmailDesensitizeConverter(_httpContextAccessor);
            }
            return jsonProperty;
        }
    }
}