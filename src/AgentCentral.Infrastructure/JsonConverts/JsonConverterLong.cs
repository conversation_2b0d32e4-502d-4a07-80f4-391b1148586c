using Newtonsoft.Json;

namespace AgentCentral.Infrastructure.JsonConverts
{
    public class JsonConverterLong : JsonConverter<long>
    {
        public override long ReadJson(JsonReader reader, Type objectType, long existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            long.TryParse(reader.Value != null ? reader.Value.ToString() : "", out long value);
            return value;
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, long value, JsonSerializer serializer)
        {
            writer.WriteValue(value.ToString());
        }

        public static List<long> JsonToLong(List<string> stringList)
        {
            List<long> longList = stringList
                .Select(s => long.TryParse(s, out long result) ? (long?)result : null)
                .Where(l => l.HasValue)
                .Select(l => l.Value)
                .ToList();

            return longList;
        }
    }
}