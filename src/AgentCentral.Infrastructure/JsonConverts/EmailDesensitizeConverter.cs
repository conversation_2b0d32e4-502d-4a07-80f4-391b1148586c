using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace AgentCentral.Infrastructure.JsonConverts
{
    public class EmailDesensitizeConverter : JsonConverter
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public EmailDesensitizeConverter(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(string);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var email = value as string;
            if (string.IsNullOrEmpty(email))
            {
                writer.WriteNull();
                return;
            }

            // 检查是否为匿名访问
            bool isAuthenticated = _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
            writer.WriteValue(isAuthenticated ? email : DesensitizeEmail(email));
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            return reader.Value?.ToString();
        }

        private static string DesensitizeEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return email;
            }

            // 判断是否是邮箱（简单匹配 '@' 且格式类似邮箱）
            bool isEmail = Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");

            if (isEmail)
            {
                var parts = email.Split('@');
                var name = parts[0];
                var domain = parts[1];

                if (name.Length <= 2)
                {
                    return $"{name[0]}****@{domain}";
                }

                return $"{name.Substring(0, 3)}****@{domain}";
            }
            else
            {
                if (email.Length == 2)
                {
                    return $"{email[0]}*";
                }

                if (email.Length > 2)
                {
                    return $"{email[0]}{new string('*', email.Length - 2)}{email[^1]}";
                }

                return email;
            }
        }
    }
}
