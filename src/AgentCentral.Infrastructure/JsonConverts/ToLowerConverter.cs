using System.Text.Json;
using System.Text.Json.Serialization;
using Newtonsoft.Json.Linq;

namespace AgentCentral.Infrastructure.JsonConverts
{
    public class ToLowerConverter : JsonConverter<string>
    {
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.GetString();
        }

        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value?.ToLower());
        }

        public static JObject ToLowercase(JObject json)
        {
            JObject newJson = new JObject();
            foreach (var property in json.Properties())
            {
                string propertyName = property.Name.ToLower();
                JToken value = property.Value;

                if (value.Type == JTokenType.Object)
                {
                    value = ToLowercase((JObject)value);
                }
                else if (value.Type == JTokenType.Array)
                {
                    JArray array = new JArray();
                    foreach (var item in (JArray)value)
                    {
                        array.Add(item.Type == JTokenType.Object ? ToLowercase((JObject)item) : item);
                    }
                    value = array;
                }

                newJson.Add(propertyName, value);
            }
            return newJson;
        }
    }
}