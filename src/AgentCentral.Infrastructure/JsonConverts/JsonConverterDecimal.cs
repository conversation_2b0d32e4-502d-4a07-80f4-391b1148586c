using Newtonsoft.Json;

namespace AgentCentral.Infrastructure.JsonConverts
{
    public class JsonConverterDecimal : JsonConverter<decimal>
    {
        private int _decimalPlaces { get; set; }

        public JsonConverterDecimal(int decimalPlaces)
        {
            _decimalPlaces = decimalPlaces;
        }

        public override decimal ReadJson(JsonReader reader, Type objectType, decimal existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            _ = decimal.TryParse(reader.Value != null ? reader.Value.ToString() : "", out decimal value);
            return value;
        }

        public override void WriteJson(JsonWriter writer, decimal value, JsonSerializer serializer)
        {
            writer.WriteValue(value.ToString("F" + _decimalPlaces));
        }
    }
}