using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;

namespace AgentCentral.Infrastructure.Authentication.JwtBearer
{
    public class JwtBearerTokenValidatedContext : ResultContext<AuthenticationSchemeOptions>
    {
        public JwtBearerTokenValidatedContext(HttpContext context, AuthenticationScheme scheme, AuthenticationSchemeOptions options)
            : base(context, scheme, options)
        {
        }
    }
}