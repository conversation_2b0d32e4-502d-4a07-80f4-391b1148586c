//using System.Security.Claims;
//using System.Text.Encodings.Web;
//using Microsoft.AspNetCore.Authentication;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Http;
//using Microsoft.Extensions.Logging;
//using Microsoft.Extensions.Options;
//using Serilog;

//namespace AgentCentral.Infrastructure.Authentication.JwtBearer
//{
//    /// <summary>
//    /// Jwt认证处理器基类
//    /// </summary>
//    public abstract class AbstractJwtBearerAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
//    {
//        public AbstractJwtBearerAuthenticationHandler(
//            IOptionsMonitor<AuthenticationSchemeOptions> options,
//            ILoggerFactory logger,
//            UrlEncoder encoder
//            )
//            : base(options, logger, encoder)
//        {
//        }

//        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
//        {
//            try
//            {
//                // 获取当前的Endpoint
//                Endpoint endpoint = Context.GetEndpoint();

//                // 如果Endpoint标记了AllowAnonymous，返回NoResult
//                if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null)
//                {
//                    return await Task.FromResult(AuthenticateResult.NoResult());
//                }

//                // 获取Authorization头部
//                string token = Request.Headers.Authorization.ToString();

//                // 如果Authorization头部为空，返回失败的认证结果
//                if (string.IsNullOrWhiteSpace(token))
//                {
//                    return await Task.FromResult(AuthenticateResult.Fail("Missing Authorization header"));
//                }

//                // 移除Bearer前缀
//                token = token.Replace(Scheme.Name, string.Empty).Trim();
//                // 验证token并获取用户信息
//                (bool isValid, long userId, string userName, string tenantId, DateTime createTime, List<string> tags) = await ValidateTokenAsync(token);

//                if (!isValid || userId == 0 || string.IsNullOrEmpty(tenantId))
//                {
//                    return await Task.FromResult(AuthenticateResult.Fail("Invalid token"));
//                }

//                // 验证成功后的处理
//                await OnTokenValidated(userId, userName, tenantId, token);

//                // 创建认证票据
//                Claim[] claims = new[]
//                {
//                    new Claim(ClaimTypes.NameIdentifier, userId.ToString())
//                };

//                ClaimsIdentity identity = new(claims, Scheme.Name);
//                ClaimsPrincipal principal = new(identity);
//                AuthenticationTicket ticket = new(principal, Scheme.Name);

//                return await Task.FromResult(AuthenticateResult.Success(ticket));
//            }
//            catch (Exception ex)
//            {
//                Log.Error(ex, "Token validation error: {Message}", ex.Message);
//                return await Task.FromResult(AuthenticateResult.Fail(ex.Message));
//            }
//        }

//        protected abstract Task<(bool isValid, long userId, string userName, string tenantId,DateTime createTime,List<string> tags) ValidateTokenAsync(string token);

//        protected abstract Task OnTokenValidated(long userId, string userName, string tenantId, string token);
//    }
//}