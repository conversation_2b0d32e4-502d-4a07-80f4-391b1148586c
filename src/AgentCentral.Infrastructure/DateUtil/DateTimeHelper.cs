namespace AgentCentral.Infrastructure.DateUtil
{
    public class DateTimeHelper
    {
        public static DateTime GetDateTimeNow(DateTime dateTime)
        {
            // 获取洛杉矶时间的TimeZoneInfo对象
            TimeZoneInfo losAngelesTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");
            // 转换为洛杉矶时间
            return TimeZoneInfo.ConvertTime(dateTime, losAngelesTimeZone);
        }
    }
}