using System.Reflection;
using Microsoft.Extensions.Localization;

namespace AgentCentral.Infrastructure.Resources
{
    public class LocalizationService
    {
        private readonly IStringLocalizer _localizer;

        public LocalizationService(IStringLocalizerFactory factory)
        {
            _localizer = factory.Create("LanguageResource", Assembly.GetExecutingAssembly().GetName().Name);
        }

        public string GetLocalizedString(string key)
        {
            return _localizer[key];
        }
    }
}