using Serilog.Events;

namespace AgentCentral.Infrastructure.LogUtil
{
    public static class Log
    {
        /// <summary>
        /// Write a log event with the <see cref="LogEventLevel.Information"/> level.
        /// </summary>
        /// <param name="messageTemplate"></param>
        public static void Information(string messageTemplate)
        {
            Serilog.Log.Information(messageTemplate);
        }

        /// <summary>
        /// Write a log event with the <see cref="LogEventLevel.Error"/> level.
        /// </summary>
        /// <param name="exception"></param>
        /// <param name="messageTemplate"></param>
        public static void Error(Exception exception, string messageTemplate)
        {
            Serilog.Log.Error(exception, messageTemplate);
        }

        public static void Error(string messageTemplate)
        {
            Serilog.Log.Error(messageTemplate);
        }

        /// <summary>
        /// Write a log event with the <see cref="LogEventLevel.Debug"/> level.
        /// </summary>
        /// <param name="messageTemplate"></param>
        public static void Debug(string messageTemplate)
        {
            Serilog.Log.Debug(messageTemplate);
        }

        /// <summary>
        /// Write a log event with the <see cref="LogEventLevel.Warning"/> level.
        /// </summary>
        /// <param name="messageTemplate"></param>
        public static void Warning(string messageTemplate)
        {
            Serilog.Log.Warning(messageTemplate);
        }
    }
}