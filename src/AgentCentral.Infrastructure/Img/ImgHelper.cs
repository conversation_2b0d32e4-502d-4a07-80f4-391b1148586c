using Microsoft.AspNetCore.Http;
using SkiaSharp;
using SkiaSharp.QrCode;

namespace AgentCentral.Infrastructure.Img
{
    public class ImgHelper
    {
        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="text">二维码内容</param>
        /// <param name="width">宽</param>
        /// <param name="height">高</param>
        /// <returns></returns>
        public static byte[] GetQrCode(string text, int width = 500, int height = 500)
        {
            //QR码的纠错能力通常分为四个级别，从低到高依次为：L、M、Q、H。其中，纠错能力最低的级别为L，最高的级别为H。纠错能力越高，二维码中包含的冗余信息就越多，因此可以更好地恢复丢失或损坏的数据。
            using QRCodeGenerator generator = new();
            using QRCodeData qr = generator.CreateQrCode(text, ECCLevel.L);
            SKImageInfo info = new(width, height);

            using SKSurface surface = SKSurface.Create(info);
            using SKCanvas canvas = surface.Canvas;
            canvas.Render(qr, info.Width, info.Height, SKColors.White, SKColors.Black);

            using SKImage image = surface.Snapshot();
            using SKData data = image.Encode(SKEncodedImageFormat.Png, 100);
            return data.ToArray();
        }

        /// <summary>
        /// 从图片截取部分区域
        /// </summary>
        /// <param name="fromImagePath">源图路径</param>
        /// <param name="offsetX">距上</param>
        /// <param name="offsetY">距左</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns></returns>
        public static byte[] Screenshot(string fromImagePath, int offsetX, int offsetY, int width, int height)
        {
            using SKBitmap original = SKBitmap.Decode(fromImagePath);
            using SKBitmap bitmap = new(width, height);
            using SKCanvas canvas = new(bitmap);
            SKRect sourceRect = new(offsetX, offsetY, offsetX + width, offsetY + height);
            SKRect destRect = new(0, 0, width, height);

            canvas.DrawBitmap(original, sourceRect, destRect);

            using SKImage img = SKImage.FromBitmap(bitmap);
            using SKData p = img.Encode(SKEncodedImageFormat.Png, 100);
            return p.ToArray();
        }

        /// <summary>
        /// 获取图像数字验证码
        /// </summary>
        /// <param name="text">验证码内容，如4为数字</param>
        /// <returns></returns>
        public static byte[] GetVerifyCode(string text)
        {
            int width = 128;
            int height = 45;

            Random random = new();

            //创建bitmap位图
            using SKBitmap image = new(width, height, SKColorType.Bgra8888, SKAlphaType.Premul);
            //创建画笔
            using SKCanvas canvas = new(image);
            //填充背景颜色为白色
            canvas.DrawColor(SKColors.White);

            //画图片的背景噪音线
            for (int i = 0; i < width * height * 0.015; i++)
            {
                using SKPaint drawStyle = new();
                drawStyle.Color = new(Convert.ToUInt32(random.Next(int.MaxValue)));

                canvas.DrawLine(random.Next(0, width), random.Next(0, height), random.Next(0, width), random.Next(0, height), drawStyle);
            }

            //将文字写到画布上
            using (SKPaint drawStyle = new())
            {
                drawStyle.Color = SKColors.Red;
                drawStyle.TextSize = height;
                drawStyle.StrokeWidth = 1;

                float emHeight = height - height * (float)0.14;
                float emWidth = (float)width / text.Length - width * (float)0.13;

                canvas.DrawText(text, emWidth, emHeight, drawStyle);
            }

            //画图片的前景噪音点
            for (int i = 0; i < width * height * 0.6; i++)
            {
                image.SetPixel(random.Next(0, width), random.Next(0, height), new SKColor(Convert.ToUInt32(random.Next(int.MaxValue))));
            }

            using SKImage img = SKImage.FromBitmap(image);
            using SKData p = img.Encode(SKEncodedImageFormat.Png, 100);
            return p.ToArray();
        }

        /// <summary>
        /// 流转图片
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static async Task<IFormFile> ConvertStreamToImageFileAsync(Stream stream, string fileName)
        {
            // 读取Stream中的数据到一个字节数组
            using (var memoryStream = new MemoryStream())
            {
                await stream.CopyToAsync(memoryStream);
                var bytes = memoryStream.ToArray();

                // 创建一个新的IFormFile对象
                var formFile = new FormFile(memoryStream, 0, bytes.Length, "imageFile", fileName)
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "image/jpeg" // 设置ContentType为image/jpeg
                };

                return formFile;
            }
        }

        /// <summary>
        /// base64图片压缩
        /// </summary>
        /// <param name="base64Input"></param>
        /// <param name="quality"></param>
        /// <returns></returns>
        public static string CompressImageBase64(string base64Input, int quality = 75)
        {
            // Decode the base64 string to a byte array
            byte[] imageBytes = Convert.FromBase64String(base64Input);

            using (var inputStream = new MemoryStream(imageBytes))
            using (var inputImage = SKBitmap.Decode(inputStream))
            {
                int targetWidth, targetHeight;

                // Check the dimensions of the image and set the target width and height
                if (inputImage.Width >= 100 && inputImage.Width <= 300 &&
                    inputImage.Height >= 100 && inputImage.Height <= 300)
                {
                    // Maintain 1:1 aspect ratio if dimensions are within 100 to 300
                    int minDimension = Math.Min(inputImage.Width, inputImage.Height);
                    targetWidth = minDimension;
                    targetHeight = minDimension;
                }
                else
                {
                    // Set aspect ratio to 4:3 based on the dimensions of the image
                    if (inputImage.Width > inputImage.Height)
                    {
                        targetWidth = inputImage.Width;
                        targetHeight = inputImage.Width * 3 / 4;
                    }
                    else
                    {
                        targetHeight = inputImage.Height;
                        targetWidth = inputImage.Height * 4 / 3;
                    }
                }

                // Resize the image to the target dimensions with high filter quality
                using (var resizedImage = inputImage.Resize(new SKImageInfo(targetWidth, targetHeight), SKFilterQuality.High))
                {
                    // Create an SKImage from the resized bitmap
                    using (var image = SKImage.FromBitmap(resizedImage))
                    {
                        // Encode the image to JPEG format with the specified quality
                        var data = image.Encode(SKEncodedImageFormat.Png, quality);

                        // Convert the encoded data to a base64 string
                        return Convert.ToBase64String(data.ToArray());
                    }
                }
            }
        }

        /// <summary>
        /// url图片压缩
        /// </summary>
        /// <param name="inputPath"></param>
        /// <param name="outputPath"></param>
        /// <param name="quality"></param>
        /// <returns></returns>
        public static string CompressImageUrl(string inputPath, string outputPath, int quality = 75)
        {
            using (var inputStream = File.OpenRead(inputPath))
            using (var inputImage = SKBitmap.Decode(inputStream))
            {
                int targetWidth, targetHeight;

                // 检查图像尺寸并设置目标宽度和高度
                if (inputImage.Width >= 100 && inputImage.Width <= 300 &&
                    inputImage.Height >= 100 && inputImage.Height <= 300)
                {
                    // 1:1 比例
                    int minDimension = Math.Min(inputImage.Width, inputImage.Height);
                    targetWidth = minDimension;
                    targetHeight = minDimension;
                }
                else
                {
                    // 4:3 比例
                    if (inputImage.Width > inputImage.Height)
                    {
                        //targetWidth = inputImage.Width;
                        //targetHeight = (inputImage.Width * 3) / 4;
                        targetHeight = inputImage.Height;
                        targetWidth = inputImage.Height * 4 / 3;
                    }
                    else
                    {
                        //targetHeight = inputImage.Height;
                        //targetWidth = (inputImage.Height * 4) / 3;
                        targetWidth = inputImage.Width;
                        targetHeight = inputImage.Width * 3 / 4;
                    }
                }

                using (var resizedImage = inputImage.Resize(new SKImageInfo(targetWidth, targetHeight), SKFilterQuality.High))
                using (var image = SKImage.FromBitmap(resizedImage))
                using (var outputStream = File.OpenWrite(outputPath))
                {
                    var data = image.Encode(SKEncodedImageFormat.Png, quality);
                    data.SaveTo(outputStream);
                }
            }

            return outputPath;
        }
    }
}