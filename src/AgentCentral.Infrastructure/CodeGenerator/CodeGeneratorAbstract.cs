using AgentCentral.Infrastructure.Redis;
using AgentCentral.Infrastructure.Redis.Enum;

namespace AgentCentral.Infrastructure.CodeGenerator;

public abstract class CodeGeneratorAbstract
{
    protected abstract string CodePrefix { get; set; }

    protected abstract string CodeSeparator { get; }

    protected abstract int CodeMaxNum { get; }

    protected abstract int MinCodeLength { get; }

    private int PrefixPartCount => CodePrefix.Length + CodeSeparator.Length;

    private int UniqueIdLength => CodeMaxNum - PrefixPartCount;

    private static readonly char PaddingChar = '0';

    private readonly IRedisService _redisService;

    protected CodeGeneratorAbstract(IRedisService redisService)
    {
        _redisService = redisService;
    }

    protected virtual async Task<long> GenerateUniqueIdAsync(string counterKey)
    {
        return await _redisService.StringIncrementAsync(RedisDataType.Token, counterKey);
    }

    protected virtual async Task<string> GenerateCodeAsync(
        string? codePrefix = null,
        int? codeLength = null)
    {
        var counterKey = GetCounterKey();
        var uniqueId = await GenerateUniqueIdAsync(counterKey);
        var uniqueIdLength = uniqueId.ToString().Length < (MinCodeLength - PrefixPartCount) ? (MinCodeLength - PrefixPartCount) : UniqueIdLength;
        return $"{CodePrefix}{CodeSeparator}{uniqueId.ToString().PadLeft(uniqueIdLength, PaddingChar)}";
    }

    private string GetCounterKey()
    {
        return $"{CodePrefix}:count";
    }
}
