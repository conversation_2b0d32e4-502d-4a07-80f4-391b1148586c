using AgentCentral.Infrastructure.DateUtil;
using AgentCentral.Infrastructure.EnumUtil;
using AgentCentral.Infrastructure.LogUtil;
using AgentCentral.Infrastructure.Redis.Enum;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace AgentCentral.Infrastructure.Redis
{
    public class RedisService : IRedisService
    {
        private readonly IDatabase _redisDb;

        private readonly IRedisDatabaseFactory _factory;

        private readonly IEnumerable<IServer> _servers;

        private const string KeyPrefix = "aihub:";

        public RedisService(IRedisDatabaseFactory factory)
        {
            _factory = factory;
            _redisDb = _factory.Create();
            _servers = _factory.GetServers();
        }

        #region 辅助方法

        /// <summary>
        /// 添加 Key 的前缀
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public string AddKeyPrefix(RedisDataType redisDataType, string redisKey)
        {
            return $"{KeyPrefix}{redisDataType.GetDescription()}{(string.IsNullOrWhiteSpace(redisKey) ? string.Empty : ":")}{redisKey}";
        }

        private string AddKeyPrefix(RedisDataType redisDataType)
        {
            return $"{KeyPrefix}{redisDataType.GetDescription()}";
        }

        #endregion 辅助方法

        #region Keys

        public bool KeyDel(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = _redisDb.KeyDelete(cacheKey);
            return flag;
        }

        public async Task<bool> KeyDelAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = await _redisDb.KeyDeleteAsync(cacheKey);
            return flag;
        }

        public bool KeyExpire(RedisDataType redisDataType, string cacheKey, int second)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = _redisDb.KeyExpire(cacheKey, TimeSpan.FromSeconds(second));
            return flag;
        }

        public async Task<bool> KeyExpireAsync(RedisDataType redisDataType, string cacheKey, int second)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = await _redisDb.KeyExpireAsync(cacheKey, TimeSpan.FromSeconds(second));
            return flag;
        }

        public bool KeyExists(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = _redisDb.KeyExists(cacheKey);
            return flag;
        }

        public async Task<bool> KeyExistsAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = await _redisDb.KeyExistsAsync(cacheKey);
            return flag;
        }

        public long TTL(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            TimeSpan? ts = _redisDb.KeyTimeToLive(cacheKey);
            return ts.HasValue ? (long)ts.Value.TotalSeconds : -1;
        }

        public async Task<long> TTLAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            TimeSpan? ts = await _redisDb.KeyTimeToLiveAsync(cacheKey);
            return ts.HasValue ? (long)ts.Value.TotalSeconds : -1;
        }

        public string[] Keys(string pattern)
        {
            List<RedisKey> keys = [];

            foreach (IServer server in _servers)
            {
                keys.AddRange(server.Keys(_redisDb.Database, pattern, 200));
            }

            return keys.Distinct().Select(k => k.ToString()).ToArray();
        }

        #endregion Keys

        #region String

        public bool StringSet(RedisDataType redisDataType, string cacheKey, string cacheValue, TimeSpan? expiration = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = _redisDb.StringSet(cacheKey, cacheValue, expiration);
            return flag;
        }

        public bool StringSetWhenNotExists(RedisDataType redisDataType, string cacheKey, string cacheValue, TimeSpan? expiration = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = _redisDb.StringSet(cacheKey, cacheValue, expiration, When.NotExists);
            return flag;
        }

        public async Task<bool> StringSetAsync(RedisDataType redisDataType, string cacheKey, string cacheValue, TimeSpan? expiration = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = await _redisDb.StringSetAsync(cacheKey, cacheValue, expiration);
            return flag;
        }

        public string StringGet(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue val = _redisDb.StringGet(cacheKey);
            return val;
        }

        public Task<long> StringIncrementAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            Task<long> val = _redisDb.StringIncrementAsync(cacheKey);
            return val;
        }

        public long StringIncrement(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            long val = _redisDb.StringIncrement(cacheKey);
            return val;
        }

        public async Task<string> StringGetAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue val = await _redisDb.StringGetAsync(cacheKey);
            return val;
        }

        #endregion String

        #region Hashes

        public bool HMSet(RedisDataType redisDataType, string cacheKey, Dictionary<string, string> vals, TimeSpan? expiration = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            if (expiration.HasValue)
            {
                List<HashEntry> list = [];

                foreach (KeyValuePair<string, string> item in vals)
                {
                    list.Add(new HashEntry(item.Key, item.Value));
                }

                _redisDb.HashSet(cacheKey, list.ToArray());

                bool flag = _redisDb.KeyExpire(cacheKey, expiration);

                return flag;
            }
            else
            {
                List<HashEntry> list = [];

                foreach (KeyValuePair<string, string> item in vals)
                {
                    list.Add(new HashEntry(item.Key, item.Value));
                }

                _redisDb.HashSet(cacheKey, list.ToArray());

                return true;
            }
        }

        public bool HSet(RedisDataType redisDataType, string cacheKey, string field, string cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return _redisDb.HashSet(cacheKey, field, cacheValue);
        }

        public bool HExists(RedisDataType redisDataType, string cacheKey, string field)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return _redisDb.HashExists(cacheKey, field);
        }

        public long HDel(RedisDataType redisDataType, string cacheKey, IList<string> fields = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            if (fields != null && fields.Any())
            {
                return _redisDb.HashDelete(cacheKey, fields.Select(x => (RedisValue)x).ToArray());
            }
            else
            {
                bool flag = _redisDb.KeyDelete(cacheKey);
                return flag ? 1 : 0;
            }
        }

        public string HGet(RedisDataType redisDataType, string cacheKey, string field)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue res = _redisDb.HashGet(cacheKey, field);
            return res;
        }

        public Dictionary<string, string> HGetAll(RedisDataType redisDataType, string cacheKey)
        {
            Dictionary<string, string> dict = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            HashEntry[] vals = _redisDb.HashGetAll(cacheKey);

            foreach (HashEntry item in vals)
            {
                if (!dict.ContainsKey(item.Name))
                {
                    dict.Add(item.Name, item.Value);
                }
            }

            return dict;
        }

        public List<string> HKeys(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] keys = _redisDb.HashKeys(cacheKey);
            return keys.Select(x => x.ToString()).ToList();
        }

        public List<string> HVals(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return _redisDb.HashValues(cacheKey).Select(x => x.ToString()).ToList();
        }

        public Dictionary<string, string> HMGet(RedisDataType redisDataType, string cacheKey, IList<string> fields)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            Dictionary<string, string> dict = [];

            RedisValue[] list = _redisDb.HashGet(cacheKey, fields.Select(x => (RedisValue)x).ToArray());

            for (int i = 0; i < fields.Count(); i++)
            {
                if (!dict.ContainsKey(fields[i]))
                {
                    dict.Add(fields[i], list[i]);
                }
            }

            return dict;
        }

        public async Task<bool> HMSetAsync(RedisDataType redisDataType, string cacheKey, Dictionary<string, string> vals, TimeSpan? expiration = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            if (expiration.HasValue)
            {
                List<HashEntry> list = [];

                foreach (KeyValuePair<string, string> item in vals)
                {
                    list.Add(new HashEntry(item.Key, item.Value));
                }

                await _redisDb.HashSetAsync(cacheKey, list.ToArray());

                bool flag = await _redisDb.KeyExpireAsync(cacheKey, expiration.Value);

                return flag;
            }
            else
            {
                List<HashEntry> list = [];

                foreach (KeyValuePair<string, string> item in vals)
                {
                    list.Add(new HashEntry(item.Key, item.Value));
                }

                await _redisDb.HashSetAsync(cacheKey, list.ToArray());
                return true;
            }
        }

        public async Task<bool> HSetAsync(RedisDataType redisDataType, string cacheKey, string field, string cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return await _redisDb.HashSetAsync(cacheKey, field, cacheValue);
        }

        public async Task<bool> HExistsAsync(RedisDataType redisDataType, string cacheKey, string field)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            return await _redisDb.HashExistsAsync(cacheKey, field);
        }

        public async Task<long> HDelAsync(RedisDataType redisDataType, string cacheKey, IList<string> fields)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            if (fields != null && fields.Any())
            {
                return await _redisDb.HashDeleteAsync(cacheKey, fields.Select(x => (RedisValue)x).ToArray());
            }
            else
            {
                bool flag = await _redisDb.KeyDeleteAsync(cacheKey);
                return flag ? 1 : 0;
            }
        }

        public async Task<long> HDelAsync(RedisDataType redisKey, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisKey, cacheKey);
            bool flag = await _redisDb.KeyDeleteAsync(cacheKey);
            return flag ? 1 : 0;
        }

        public async Task<string> HGetAsync(RedisDataType redisDataType, string cacheKey, string field)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue res = await _redisDb.HashGetAsync(cacheKey, field);
            return res;
        }

        public async Task<Dictionary<string, string>> HGetAllAsync(RedisDataType redisDataType, string cacheKey)
        {
            Dictionary<string, string> dict = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            HashEntry[] vals = await _redisDb.HashGetAllAsync(cacheKey);

            foreach (HashEntry item in vals)
            {
                if (!dict.ContainsKey(item.Name))
                {
                    dict.Add(item.Name, item.Value);
                }
            }

            return dict;
        }

        public async Task<List<string>> HKeysAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] keys = await _redisDb.HashKeysAsync(cacheKey);
            return keys.Select(x => x.ToString()).ToList();
        }

        public async Task<List<string>> HValsAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return (await _redisDb.HashValuesAsync(cacheKey)).Select(x => x.ToString()).ToList();
        }

        public async Task<Dictionary<string, string>> HMGetAsync(RedisDataType redisDataType, string cacheKey, IList<string> fields)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            Dictionary<string, string> dict = [];

            RedisValue[] res = await _redisDb.HashGetAsync(cacheKey, fields.Select(x => (RedisValue)x).ToArray());

            for (int i = 0; i < fields.Count(); i++)
            {
                if (!dict.ContainsKey(fields[i]))
                {
                    dict.Add(fields[i], res[i]);
                }
            }

            return dict;
        }

        #endregion Hashes

        #region List

        public T LIndex<T>(RedisDataType redisDataType, string cacheKey, long index)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = _redisDb.ListGetByIndex(cacheKey, index);
            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public long LLen(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return _redisDb.ListLength(cacheKey);
        }

        public T LPop<T>(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = _redisDb.ListLeftPop(cacheKey);
            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public long LPush<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<RedisValue> list = [];

            foreach (T item in cacheValues)
            {
                list.Add(JsonConvert.SerializeObject(item));
            }

            long len = _redisDb.ListLeftPush(cacheKey, list.ToArray());
            return len;
        }

        public List<T> LRange<T>(RedisDataType redisDataType, string cacheKey, long start, long stop)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = _redisDb.ListRange(cacheKey, start, stop);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public long LRem<T>(RedisDataType redisDataType, string cacheKey, long count, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            return _redisDb.ListRemove(cacheKey, bytes, count);
        }

        public bool LSet<T>(RedisDataType redisDataType, string cacheKey, long index, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            _redisDb.ListSetByIndex(cacheKey, index, bytes);
            return true;
        }

        public bool LTrim(RedisDataType redisDataType, string cacheKey, long start, long stop)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            _redisDb.ListTrim(cacheKey, start, stop);
            return true;
        }

        public long LPushX<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            return _redisDb.ListLeftPush(cacheKey, bytes, When.Exists);
        }

        public long LInsertBefore<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue)
        {
            string pivotBytes = JsonConvert.SerializeObject(pivot);
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string cacheValueBytes = JsonConvert.SerializeObject(cacheValue);
            return _redisDb.ListInsertBefore(cacheKey, pivotBytes, cacheValueBytes);
        }

        public long LInsertAfter<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue)
        {
            string pivotBytes = JsonConvert.SerializeObject(pivot);
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string cacheValueBytes = JsonConvert.SerializeObject(cacheValue);
            return _redisDb.ListInsertAfter(cacheKey, pivotBytes, cacheValueBytes);
        }

        public long RPushX<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            return _redisDb.ListRightPush(cacheKey, bytes, When.Exists);
        }

        public long RPush<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<RedisValue> list = [];

            foreach (T item in cacheValues)
            {
                list.Add(JsonConvert.SerializeObject(item));
            }

            long len = _redisDb.ListRightPush(cacheKey, list.ToArray());
            return len;
        }

        public T RPop<T>(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = _redisDb.ListRightPop(cacheKey);
            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public async Task<T> LIndexAsync<T>(RedisDataType redisDataType, string cacheKey, long index)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = await _redisDb.ListGetByIndexAsync(cacheKey, index);
            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public async Task<long> LLenAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            return await _redisDb.ListLengthAsync(cacheKey);
        }

        public async Task<T> LPopAsync<T>(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = await _redisDb.ListLeftPopAsync(cacheKey);
            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public async Task<long> LPushAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<RedisValue> list = [];

            foreach (T item in cacheValues)
            {
                list.Add(JsonConvert.SerializeObject(item));
            }

            long len = await _redisDb.ListLeftPushAsync(cacheKey, list.ToArray());
            return len;
        }

        public async Task<List<T>> LRangeAsync<T>(RedisDataType redisDataType, string cacheKey, long start, long stop)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = await _redisDb.ListRangeAsync(cacheKey, start, stop);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public async Task<long> LRemAsync<T>(RedisDataType redisDataType, string cacheKey, long count, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            return await _redisDb.ListRemoveAsync(cacheKey, bytes, count);
        }

        public async Task<bool> LSetAsync<T>(RedisDataType redisDataType, string cacheKey, long index, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            await _redisDb.ListSetByIndexAsync(cacheKey, index, bytes);
            return true;
        }

        public async Task<bool> LTrimAsync(RedisDataType redisDataType, string cacheKey, long start, long stop)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            await _redisDb.ListTrimAsync(cacheKey, start, stop);
            return true;
        }

        public async Task<long> LPushXAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            return await _redisDb.ListLeftPushAsync(cacheKey, bytes, When.Exists);
        }

        public async Task<long> LInsertBeforeAsync<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue)
        {
            string pivotBytes = JsonConvert.SerializeObject(pivot);
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string cacheValueBytes = JsonConvert.SerializeObject(cacheValue);
            return await _redisDb.ListInsertBeforeAsync(cacheKey, pivotBytes, cacheValueBytes);
        }

        public async Task<long> LInsertAfterAsync<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue)
        {
            string pivotBytes = JsonConvert.SerializeObject(pivot);
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string cacheValueBytes = JsonConvert.SerializeObject(cacheValue);
            return await _redisDb.ListInsertAfterAsync(cacheKey, pivotBytes, cacheValueBytes);
        }

        public async Task<long> RPushAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues)
        {
            List<RedisValue> list = [];
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            foreach (T item in cacheValues)
            {
                list.Add(JsonConvert.SerializeObject(item));
            }

            long len = await _redisDb.ListRightPushAsync(cacheKey, list.ToArray());
            return len;
        }

        public async Task<long> RPushXAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);
            return await _redisDb.ListRightPushAsync(cacheKey, bytes, When.Exists);
        }

        public async Task<T> RPopAsync<T>(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = await _redisDb.ListRightPopAsync(cacheKey);
            return JsonConvert.DeserializeObject<T>(bytes);
        }

        #endregion List

        #region Set

        public long SAdd<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues, TimeSpan? expiration = null)
        {
            List<RedisValue> list = [];
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            foreach (T item in cacheValues)
            {
                list.Add(JsonConvert.SerializeObject(item));
            }

            long len = _redisDb.SetAdd(cacheKey, list.ToArray());

            if (expiration.HasValue)
            {
                _ = _redisDb.KeyExpire(cacheKey, expiration.Value);
            }

            return len;
        }

        public long SCard(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            long len = _redisDb.SetLength(cacheKey);
            return len;
        }

        public bool SIsMember<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = _redisDb.SetContains(cacheKey, bytes);
            return flag;
        }

        public List<T> SMembers<T>(RedisDataType redisDataType, string cacheKey)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = _redisDb.SetMembers(cacheKey);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public T SPop<T>(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = _redisDb.SetPop(cacheKey);

            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public List<T> SRandMember<T>(RedisDataType redisDataType, string cacheKey, int count = 1)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = _redisDb.SetRandomMembers(cacheKey, count);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public long SRem<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues = null)
        {
            long len;
            if (cacheValues != null && cacheValues.Any())
            {
                cacheKey = AddKeyPrefix(redisDataType, cacheKey);
                List<RedisValue> bytes = [];

                foreach (T item in cacheValues)
                {
                    bytes.Add(JsonConvert.SerializeObject(item));
                }

                len = _redisDb.SetRemove(cacheKey, bytes.ToArray());
            }
            else
            {
                cacheKey = AddKeyPrefix(redisDataType, cacheKey);
                bool flag = _redisDb.KeyDelete(cacheKey);
                len = flag ? 1 : 0;
            }

            return len;
        }

        public async Task<long> SAddAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues, TimeSpan? expiration = null)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<RedisValue> list = [];

            foreach (T item in cacheValues)
            {
                list.Add(JsonConvert.SerializeObject(item));
            }

            long len = await _redisDb.SetAddAsync(cacheKey, list.ToArray());

            if (expiration.HasValue)
            {
                _ = await _redisDb.KeyExpireAsync(cacheKey, expiration.Value);
            }

            return len;
        }

        public async Task<long> SCardAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            long len = await _redisDb.SetLengthAsync(cacheKey);
            return len;
        }

        public async Task<bool> SIsMemberAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            bool flag = await _redisDb.SetContainsAsync(cacheKey, bytes);
            return flag;
        }

        public async Task<List<T>> SMembersAsync<T>(RedisDataType redisDataType, string cacheKey)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] vals = await _redisDb.SetMembersAsync(cacheKey);

            foreach (RedisValue item in vals)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public async Task<T> SPopAsync<T>(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue bytes = await _redisDb.SetPopAsync(cacheKey);

            return JsonConvert.DeserializeObject<T>(bytes);
        }

        public async Task<List<T>> SRandMemberAsync<T>(RedisDataType redisDataType, string cacheKey, int count = 1)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = await _redisDb.SetRandomMembersAsync(cacheKey, count);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public async Task<long> SRemAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues = null)
        {
            long len;
            if (cacheValues != null && cacheValues.Any())
            {
                cacheKey = AddKeyPrefix(redisDataType, cacheKey);
                List<RedisValue> bytes = [];

                foreach (T item in cacheValues)
                {
                    bytes.Add(JsonConvert.SerializeObject(item));
                }

                len = await _redisDb.SetRemoveAsync(cacheKey, bytes.ToArray());
            }
            else
            {
                cacheKey = AddKeyPrefix(redisDataType, cacheKey);
                bool flag = await _redisDb.KeyDeleteAsync(cacheKey);
                len = flag ? 1 : 0;
            }

            return len;
        }

        #endregion Set

        #region Sorted Set

        public long ZAdd<T>(RedisDataType redisDataType, string cacheKey, Dictionary<T, double> cacheValues)
            where T : notnull
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<SortedSetEntry> param = [];

            foreach (KeyValuePair<T, double> item in cacheValues)
            {
                param.Add(new SortedSetEntry(JsonConvert.SerializeObject(item.Key), item.Value));
            }

            long len = _redisDb.SortedSetAdd(cacheKey, param.ToArray());

            return len;
        }

        public long ZCard(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            long len = _redisDb.SortedSetLength(cacheKey);
            return len;
        }

        public long ZCount(RedisDataType redisDataType, string cacheKey, double min, double max)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            long len = _redisDb.SortedSetLengthByValue(cacheKey, min, max);
            return len;
        }

        public double ZIncrBy(RedisDataType redisDataType, string cacheKey, string field, double val = 1)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            double value = _redisDb.SortedSetIncrement(cacheKey, field, val);
            return value;
        }

        public long ZLexCount(RedisDataType redisDataType, string cacheKey, string min, string max)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            long len = _redisDb.SortedSetLengthByValue(cacheKey, min, max);
            return len;
        }

        public List<T> ZRange<T>(RedisDataType redisDataType, string cacheKey, long start, long stop)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = _redisDb.SortedSetRangeByRank(cacheKey, start, stop);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public long? ZRank<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);

            long? index = _redisDb.SortedSetRank(cacheKey, bytes);

            return index;
        }

        public long ZRem<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<RedisValue> bytes = [];

            foreach (T item in cacheValues)
            {
                bytes.Add(JsonConvert.SerializeObject(item));
            }

            long len = _redisDb.SortedSetRemove(cacheKey, bytes.ToArray());

            return len;
        }

        public double? ZScore<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);

            double? score = _redisDb.SortedSetScore(cacheKey, bytes);

            return score;
        }

        public async Task<long> ZAddAsync<T>(RedisDataType redisDataType, string cacheKey, Dictionary<T, double> cacheValues)
            where T : notnull
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            List<SortedSetEntry> param = [];

            foreach (KeyValuePair<T, double> item in cacheValues)
            {
                param.Add(new SortedSetEntry(JsonConvert.SerializeObject(item.Key), item.Value));
            }

            long len = await _redisDb.SortedSetAddAsync(cacheKey, param.ToArray());

            return len;
        }

        public async Task<long> ZCardAsync(RedisDataType redisDataType, string cacheKey)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            long len = await _redisDb.SortedSetLengthAsync(cacheKey);
            return len;
        }

        public async Task<long> ZCountAsync(RedisDataType redisDataType, string cacheKey, double min, double max)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            long len = await _redisDb.SortedSetLengthByValueAsync(cacheKey, min, max);
            return len;
        }

        public async Task<double> ZIncrByAsync(RedisDataType redisDataType, string cacheKey, string field, double val = 1)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            double value = await _redisDb.SortedSetIncrementAsync(cacheKey, field, val);
            return value;
        }

        public async Task<long> ZLexCountAsync(RedisDataType redisDataType, string cacheKey, string min, string max)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);

            long len = await _redisDb.SortedSetLengthByValueAsync(cacheKey, min, max);
            return len;
        }

        public async Task<List<T>> ZRangeAsync<T>(RedisDataType redisDataType, string cacheKey, long start, long stop)
        {
            List<T> list = [];

            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            RedisValue[] bytes = await _redisDb.SortedSetRangeByRankAsync(cacheKey, start, stop);

            foreach (RedisValue item in bytes)
            {
                list.Add(JsonConvert.DeserializeObject<T>(item));
            }

            return list;
        }

        public async Task<long?> ZRankAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);

            long? index = await _redisDb.SortedSetRankAsync(cacheKey, bytes);

            return index;
        }

        public async Task<long> ZRemAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            List<RedisValue> bytes = [];

            foreach (T item in cacheValues)
            {
                bytes.Add(JsonConvert.SerializeObject(item));
            }

            long len = await _redisDb.SortedSetRemoveAsync(cacheKey, bytes.ToArray());

            return len;
        }

        public async Task<double?> ZScoreAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue)
        {
            cacheKey = AddKeyPrefix(redisDataType, cacheKey);
            string bytes = JsonConvert.SerializeObject(cacheValue);

            double? score = await _redisDb.SortedSetScoreAsync(cacheKey, bytes);

            return score;
        }

        #endregion Sorted Set

        #region Lock

        /// <summary>
        /// 分布式锁 Token。
        /// </summary>
        private static readonly RedisValue LockToken = Environment.MachineName;

        /// <summary>
        /// 获取锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <param name="seconds">过期时间（秒）。</param>
        /// <returns>是否已锁。</returns>
        public bool Lock(RedisDataType redisDataType, string redisKey, int seconds, string token = "")
        {
            redisKey = AddKeyPrefix(redisDataType, "lock:" + redisKey);
            return _redisDb.LockTake(redisKey, string.IsNullOrEmpty(token) ? LockToken : token, TimeSpan.FromSeconds(seconds));
        }

        /// <summary>
        /// 释放锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <returns>是否成功。</returns>
        public bool UnLock(RedisDataType redisDataType, string redisKey, string token = "")
        {
            redisKey = AddKeyPrefix(redisDataType, "lock:" + redisKey);
            return _redisDb.LockRelease(redisKey, string.IsNullOrEmpty(token) ? LockToken : token);
        }

        /// <summary>
        /// 异步获取锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <param name="seconds">过期时间（秒）。</param>
        /// <returns>是否成功。</returns>
        public async Task<bool> LockAsync(RedisDataType redisDataType, string redisKey, int seconds, string token = "")
        {
            redisKey = AddKeyPrefix(redisDataType, "lock:" + redisKey);
            return await _redisDb.LockTakeAsync(redisKey, string.IsNullOrEmpty(token) ? LockToken : token, TimeSpan.FromSeconds(seconds));
        }

        /// <summary>
        /// 异步释放锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <returns>是否成功。</returns>
        public async Task<bool> UnLockAsync(RedisDataType redisDataType, string redisKey, string token = "")
        {
            redisKey = AddKeyPrefix(redisDataType, "lock:" + redisKey);
            return await _redisDb.LockReleaseAsync(redisKey, string.IsNullOrEmpty(token) ? LockToken : token);
        }

        /// <summary>
        /// 异步自旋获取锁
        /// </summary>
        /// <param name="redisDataType"></param>
        /// <param name="redisKey"></param>
        /// <param name="timeoutSeconds"></param>
        /// <returns></returns>
        public async Task<bool> SpinLockAsync(RedisDataType redisDataType, string redisKey, int timeoutSeconds)
        {
            // 生成带前缀的 Redis 键
            redisKey = AddKeyPrefix(redisDataType, "lock:" + redisKey);

            // 计算超时时间
            TimeSpan timeout = TimeSpan.FromSeconds(timeoutSeconds);
            TimeSpan retryInterval = TimeSpan.FromMilliseconds(200);
            DateTime startTime = DateTimeHelper.GetDateTimeNow(DateTime.Now);
            try
            {
                while (DateTimeHelper.GetDateTimeNow(DateTime.Now) - startTime < timeout)
                {
                    // 尝试获取锁
                    if (await _redisDb.LockTakeAsync(redisKey, LockToken, timeout))
                    {
                        // 成功获取锁
                        return true;
                    }

                    // 等待一段时间后重试
                    await Task.Delay(retryInterval);
                }
                // 超时后未能获取锁
                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "RedisService.SpinLockAsync");
                return false;
            }
        }

        public IEnumerable<RedisKey> GetKeys(RedisDataType redisDataType, int dbIndex)
        {
            throw new NotImplementedException();
        }

        #endregion Lock
    }
}