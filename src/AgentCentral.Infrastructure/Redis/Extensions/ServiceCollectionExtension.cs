using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AgentCentral.Infrastructure.Redis.Extensions
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection AddRedis(this IServiceCollection services, IConfigurationSection redisSection)
        {
            services.Configure<RedisOptions>(redisSection);

            return services
                .AddSingleton<IRedisDatabaseFactory, RedisDatabaseFactory>()
                .AddSingleton<IRedisService, RedisService>();
        }
    }
}