using AgentCentral.Infrastructure.Redis.Enum;
using Twilio.Jwt.AccessToken;

namespace AgentCentral.Infrastructure.Redis
{
    public interface IRedisService
    {
        #region Keys

        string AddKeyPrefix(RedisDataType redisDataType, string redisKey);

        bool KeyDel(RedisDataType redisDataType, string cacheKey);

        Task<bool> KeyDelAsync(RedisDataType redisDataType, string cacheKey);

        bool KeyExpire(RedisDataType redisDataType, string cacheKey, int second);

        Task<bool> KeyExpireAsync(RedisDataType redisDataType, string cacheKey, int second);

        bool KeyExists(RedisDataType redisDataType, string cacheKey);

        Task<bool> KeyExistsAsync(RedisDataType redisDataType, string cacheKey);

        long TTL(RedisDataType redisDataType, string cacheKey);

        Task<long> TTLAsync(RedisDataType redisDataType, string cacheKey);

        string[] Keys(string pattern);

        #endregion Keys

        #region String

        bool StringSet(RedisDataType redisDataType, string cacheKey, string cacheValue, TimeSpan? expiration = null);

        bool StringSetWhenNotExists(RedisDataType redisDataType, string cacheKey, string cacheValue, TimeSpan? expiration = null);

        Task<bool> StringSetAsync(RedisDataType redisDataType, string cacheKey, string cacheValue, TimeSpan? expiration = null);

        string StringGet(RedisDataType redisDataType, string cacheKey);

        Task<string> StringGetAsync(RedisDataType redisDataType, string cacheKey);

        public Task<long> StringIncrementAsync(RedisDataType redisDataType, string cacheKey);

        public long StringIncrement(RedisDataType redisDataType, string cacheKey);

        #endregion String

        #region Hashes

        bool HMSet(RedisDataType redisKey, string cacheKey, Dictionary<string, string> vals, TimeSpan? expiration = null);

        Task<bool> HMSetAsync(RedisDataType redisDataType, string cacheKey, Dictionary<string, string> vals, TimeSpan? expiration = null);

        Dictionary<string, string> HMGet(RedisDataType redisKey, string cacheKey, IList<string> fields);

        Task<Dictionary<string, string>> HMGetAsync(RedisDataType redisKey, string cacheKey, IList<string> fields);

        Dictionary<string, string> HGetAll(RedisDataType redisKey, string cacheKey);

        Task<Dictionary<string, string>> HGetAllAsync(RedisDataType redisDataType, string cacheKey);

        bool HSet(RedisDataType redisKey, string cacheKey, string field, string cacheValue);

        Task<bool> HSetAsync(RedisDataType redisKey, string cacheKey, string field, string cacheValue);

        bool HExists(RedisDataType redisKey, string cacheKey, string field);

        Task<bool> HExistsAsync(RedisDataType redisKey, string cacheKey, string field);

        long HDel(RedisDataType redisKey, string cacheKey, IList<string> fields = null);

        Task<long> HDelAsync(RedisDataType redisKey, string cacheKey, IList<string> fields = null);

        Task<long> HDelAsync(RedisDataType redisKey, string cacheKey);

        string HGet(RedisDataType redisKey, string cacheKey, string field);

        Task<string> HGetAsync(RedisDataType redisKey, string cacheKey, string field);

        List<string> HKeys(RedisDataType redisKey, string cacheKey);

        Task<List<string>> HKeysAsync(RedisDataType redisKey, string cacheKey);

        List<string> HVals(RedisDataType redisKey, string cacheKey);

        Task<List<string>> HValsAsync(RedisDataType redisKey, string cacheKey);

        #endregion Hashes

        #region List

        T LIndex<T>(RedisDataType redisDataType, string cacheKey, long index);

        long LLen(RedisDataType redisDataType, string cacheKey);

        T LPop<T>(RedisDataType redisDataType, string cacheKey);

        long LPush<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues);

        List<T> LRange<T>(RedisDataType redisDataType, string cacheKey, long start, long stop);

        long LRem<T>(RedisDataType redisDataType, string cacheKey, long count, T cacheValue);

        bool LSet<T>(RedisDataType redisDataType, string cacheKey, long index, T cacheValue);

        bool LTrim(RedisDataType redisDataType, string cacheKey, long start, long stop);

        long LPushX<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        long LInsertBefore<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue);

        long LInsertAfter<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue);

        long RPushX<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        long RPush<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues);

        T RPop<T>(RedisDataType redisDataType, string cacheKey);

        Task<T> LIndexAsync<T>(RedisDataType redisDataType, string cacheKey, long index);

        Task<long> LLenAsync(RedisDataType redisDataType, string cacheKey);

        Task<T> LPopAsync<T>(RedisDataType redisDataType, string cacheKey);

        Task<long> LPushAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues);

        Task<List<T>> LRangeAsync<T>(RedisDataType redisDataType, string cacheKey, long start, long stop);

        Task<long> LRemAsync<T>(RedisDataType redisDataType, string cacheKey, long count, T cacheValue);

        Task<bool> LSetAsync<T>(RedisDataType redisDataType, string cacheKey, long index, T cacheValue);

        Task<bool> LTrimAsync(RedisDataType redisDataType, string cacheKey, long start, long stop);

        Task<long> LPushXAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        Task<long> LInsertBeforeAsync<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue);

        Task<long> LInsertAfterAsync<T>(RedisDataType redisDataType, string cacheKey, T pivot, T cacheValue);

        Task<long> RPushXAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        Task<long> RPushAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues);

        Task<T> RPopAsync<T>(RedisDataType redisDataType, string cacheKey);

        #endregion List

        #region Set

        long SAdd<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues, TimeSpan? expiration = null);

        long SCard(RedisDataType redisDataType, string cacheKey);

        bool SIsMember<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        List<T> SMembers<T>(RedisDataType redisDataType, string cacheKey);

        T SPop<T>(RedisDataType redisDataType, string cacheKey);

        List<T> SRandMember<T>(RedisDataType redisDataType, string cacheKey, int count = 1);

        long SRem<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues = null);

        Task<long> SAddAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues, TimeSpan? expiration = null);

        Task<long> SCardAsync(RedisDataType redisDataType, string cacheKey);

        Task<bool> SIsMemberAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        Task<List<T>> SMembersAsync<T>(RedisDataType redisDataType, string cacheKey);

        Task<T> SPopAsync<T>(RedisDataType redisDataType, string cacheKey);

        Task<List<T>> SRandMemberAsync<T>(RedisDataType redisDataType, string cacheKey, int count = 1);

        Task<long> SRemAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues = null);

        #endregion Set

        #region Sorted Set

        long ZAdd<T>(RedisDataType redisDataType, string cacheKey, Dictionary<T, double> cacheValues) where T : notnull;

        long ZCard(RedisDataType redisDataType, string cacheKey);

        long ZCount(RedisDataType redisDataType, string cacheKey, double min, double max);

        double ZIncrBy(RedisDataType redisDataType, string cacheKey, string field, double val = 1);

        long ZLexCount(RedisDataType redisDataType, string cacheKey, string min, string max);

        List<T> ZRange<T>(RedisDataType redisDataType, string cacheKey, long start, long stop);

        long? ZRank<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        long ZRem<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues);

        double? ZScore<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        Task<long> ZAddAsync<T>(RedisDataType redisDataType, string cacheKey, Dictionary<T, double> cacheValues) where T : notnull;

        Task<long> ZCardAsync(RedisDataType redisDataType, string cacheKey);

        Task<long> ZCountAsync(RedisDataType redisDataType, string cacheKey, double min, double max);

        Task<double> ZIncrByAsync(RedisDataType redisDataType, string cacheKey, string field, double val = 1);

        Task<long> ZLexCountAsync(RedisDataType redisDataType, string cacheKey, string min, string max);

        Task<List<T>> ZRangeAsync<T>(RedisDataType redisDataType, string cacheKey, long start, long stop);

        Task<long?> ZRankAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        Task<long> ZRemAsync<T>(RedisDataType redisDataType, string cacheKey, IList<T> cacheValues);

        Task<double?> ZScoreAsync<T>(RedisDataType redisDataType, string cacheKey, T cacheValue);

        #endregion Sorted Set

        #region Lock

        /// <summary>
        /// 获取锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <param name="seconds">过期时间（秒）。</param>
        /// <returns>是否已锁。</returns>
        bool Lock(RedisDataType redisDataType, string redisKey, int seconds, string token = "");

        /// <summary>
        /// 释放锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <returns>是否成功。</returns>
        bool UnLock(RedisDataType redisDataType, string redisKey, string token = "");

        /// <summary>
        /// 异步获取锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <param name="seconds">过期时间（秒）。</param>
        /// <returns>是否成功。</returns>
        Task<bool> LockAsync(RedisDataType redisDataType, string redisKey, int seconds, string token = "");

        /// <summary>
        /// 异步释放锁。
        /// </summary>
        /// <param name="key">锁名称。</param>
        /// <returns>是否成功。</returns>
        Task<bool> UnLockAsync(RedisDataType redisDataType, string redisKey, string token = "");

        /// <summary>
        /// 异步自旋获取锁
        /// </summary>
        /// <param name="redisDataType"></param>
        /// <param name="redisKey"></param>
        /// <param name="timeoutSeconds"></param>
        /// <returns></returns>
        Task<bool> SpinLockAsync(RedisDataType redisDataType, string redisKey, int timeoutSeconds);

        #endregion Lock
    }
}