using System.Net;
using Microsoft.Extensions.Options;
using StackExchange.Redis;

namespace AgentCentral.Infrastructure.Redis
{
    public sealed class RedisDatabaseFactory : IRedisDatabaseFactory
    {
        private readonly IOptions<RedisOptions> _options;

        private readonly Lazy<ConnectionMultiplexer> _connectionMultiplexer;

        public RedisDatabaseFactory(IOptions<RedisOptions> options)
        {
            _options = options;
            _connectionMultiplexer = new Lazy<ConnectionMultiplexer>(CreateConnectionMultiplexer);
        }

        private ConnectionMultiplexer CreateConnectionMultiplexer()
        {
            var dbconfig = _options.Value;
            _ = ConfigurationOptions.Parse(dbconfig.ConnectionString);
            return ConnectionMultiplexer.Connect(dbconfig.ConnectionString);
        }

        private List<EndPoint> GetMastersServersEndpoints()
        {
            var masters = new List<EndPoint>();
            foreach (var ep in _connectionMultiplexer.Value.GetEndPoints())
            {
                var server = _connectionMultiplexer.Value.GetServer(ep);
                if (server.IsConnected)
                {
                    if (server.ServerType == ServerType.Cluster)
                    {
                        var clusterConfiguration = server.ClusterConfiguration ?? throw new NullReferenceException(nameof(server.ClusterConfiguration));

                        var nodes = clusterConfiguration.Nodes.Where(n => !n.IsReplica) ?? throw new NullReferenceException(nameof(server.ClusterConfiguration.Nodes));

                        var endpoints = nodes.Select(n => n.EndPoint);
                        if (endpoints is null)
                        {
                            throw new NullReferenceException(nameof(endpoints));
                        }
                        masters.AddRange(endpoints);

                        break;
                    }
                    if (server.ServerType == ServerType.Standalone && !server.IsReplica)
                    {
                        masters.Add(ep);
                        break;
                    }
                }
            }
            return masters;
        }

        public IEnumerable<IServer> GetServers()
        {
            var endpoints = GetMastersServersEndpoints();

            foreach (var endpoint in endpoints)
            {
                yield return _connectionMultiplexer.Value.GetServer(endpoint);
            }
        }

        public IDatabase Create()
        {
            return _connectionMultiplexer.Value.GetDatabase();
        }
    }
}