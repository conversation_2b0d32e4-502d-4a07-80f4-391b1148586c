using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using FluentAssertions;
using AutoMapper;
using AgentCentral.Application.Contracts.RequestModels.AppUsage;
using AgentCentral.Application.Contracts.ResponseModels.AppUsage;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.Application.Contracts.IService.App;
using AgentCentral.Domain.Shared.Models;

namespace AgentCentral.WebApiTests.Services
{
    [TestClass]
    public class AppUsageServiceTests
    {
        private Mock<IAppUsageService> _mockAppUsageService;
        private Mock<IMapper> _mockMapper;
        private Mock<UserContext> _mockUserContext;

        [TestInitialize]
        public void Setup()
        {
            _mockAppUsageService = new Mock<IAppUsageService>();
            _mockMapper = new Mock<IMapper>();
            _mockUserContext = new Mock<UserContext>();

            _mockUserContext.Setup(x => x.UserId).Returns(1);
        }

        [TestMethod]
        public async Task GetByAppIdAsync_WithValidAppId_ShouldReturnUsage()
        {
            // Arrange
            string appId = "app1";
            var usageResponse = new AppUsageResponse
            {
                AppId = appId,
                TotalTokens = 100,
                CostSeconds = 60
            };

            _mockAppUsageService.Setup(x => x.GetByAppIdAsync(appId))
                .ReturnsAsync(usageResponse);

            // Act
            var result = await _mockAppUsageService.Object.GetByAppIdAsync(appId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(usageResponse);
        }

        [TestMethod]
        public async Task GetByAppIdAsync_WithInvalidAppId_ShouldThrowException()
        {
            // Arrange
            string appId = "invalid_app";
            _mockAppUsageService.Setup(x => x.GetByAppIdAsync(appId))
                .ThrowsAsync(new AgentCentralException("AppUsage not found"));

            // Act & Assert
            await _mockAppUsageService.Object.Invoking(x => x.GetByAppIdAsync(appId))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("AppUsage not found");
        }

        [TestMethod]
        public async Task CreateAsync_WithValidRequest_ShouldCreateUsage()
        {
            // Arrange
            string appId = "app1";
            var request = new CreateAppUsageRequest
            {
                AppId = appId,
                TotalTokens = 100,
                CostSeconds = 60
            };
            var usageResponse = new AppUsageResponse
            {
                AppId = appId,
                TotalTokens = 100,
                CostSeconds = 60,
            };

            _mockAppUsageService.Setup(x => x.CreateAsync(request))
                .ReturnsAsync(usageResponse);

            // Act
            var result = await _mockAppUsageService.Object.CreateAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(usageResponse);
        }

        [TestMethod]
        public async Task CreateAsync_WithExistingAppId_ShouldThrowException()
        {
            // Arrange
            string appId = "app1";
            var request = new CreateAppUsageRequest
            {
                AppId = appId,
                TotalTokens = 100,
                CostSeconds = 60
            };

            _mockAppUsageService.Setup(x => x.CreateAsync(request))
                .ThrowsAsync(new AgentCentralException("AppUsage already exists"));

            // Act & Assert
            await _mockAppUsageService.Object.Invoking(x => x.CreateAsync(request))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("AppUsage already exists");
        }

        [TestMethod]
        public async Task UpdateAsync_WithValidRequest_ShouldUpdateUsage()
        {
            // Arrange
            string appId = "app1";
            var request = new UpdateAppUsageRequest
            {
                TotalTokens = 200,
                CostSeconds = 120
            };
            var usageResponse = new AppUsageResponse
            {
                AppId = appId,
                TotalTokens = 200,
                CostSeconds = 120,
            };
            _mockAppUsageService.Setup(x => x.UpdateAsync(appId, request))
                .ReturnsAsync(usageResponse);

            // Act
            var result = await _mockAppUsageService.Object.UpdateAsync(appId, request);

            // Assert
            result.Should().BeEquivalentTo(usageResponse);
        }

        [TestMethod]
        public async Task UpdateAsync_WithInvalidAppId_ShouldThrowException()
        {
            // Arrange
            string appId = "invalid_app";
            var request = new UpdateAppUsageRequest
            {
                TotalTokens = 200,
                CostSeconds = 120
            };

            _mockAppUsageService.Setup(x => x.UpdateAsync(appId, request))
                .ThrowsAsync(new AgentCentralException("AppUsage not found"));

            // Act & Assert
            await _mockAppUsageService.Object.Invoking(x => x.UpdateAsync(appId, request))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("AppUsage not found");
        }

        [TestMethod]
        public async Task DeleteAsync_WithValidAppId_ShouldSoftDeleteUsage()
        {
            // Arrange
            string appId = "app1";
            _mockAppUsageService.Setup(x => x.DeleteAsync(appId))
                .ReturnsAsync(true);

            // Act
            var result = await _mockAppUsageService.Object.DeleteAsync(appId);

            // Assert
            result.Should().BeTrue();
        }

        [TestMethod]
        public async Task DeleteAsync_WithInvalidAppId_ShouldThrowException()
        {
            // Arrange
            string appId = "invalid_app";
            _mockAppUsageService.Setup(x => x.DeleteAsync(appId))
                .ThrowsAsync(new AgentCentralException("AppUsage not found"));

            // Act & Assert
            await _mockAppUsageService.Object.Invoking(x => x.DeleteAsync(appId))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("AppUsage not found");
        }
    }
}