using AgentCentral.Application.Client;
using AgentCentral.Application.Contracts.Dtos.CallLog;
using AgentCentral.Application.Contracts.Options;
using AgentCentral.Application.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace AgentCentral.WebApiTests.Services
{
    public class CallLogServiceTests
    {
        private readonly Mock<CallLogClient> _mockCallLogClient;
        private readonly CallLogService _callLogService;

        public CallLogServiceTests()
        {
            _mockCallLogClient = new Mock<CallLogClient>(
                Mock.Of<IOptionsSnapshot<CallLogOptions>>(),
                Mock.Of<HttpClient>(),
                Mock.Of<IHttpContextAccessor>());

            _callLogService = new CallLogService(_mockCallLogClient.Object);
        }

        [TestMethod]
        public async Task GetCallSupportStatisticsAsync_ShouldReturnStatistics()
        {
            // Arrange
            var expectedStatistics = new CallSupportStatisticsDto
            {
                TotalCalls = 100,
                TotalCallsChangePercent = 10.5,
                CallsToday = 20,
                CallsTodayChangePercent = 5.2,
                EmailsSent = 50,
                EmailsSentChangePercent = 2.1,
                IntentDistribution = new Dictionary<string, double>
                {
                    { "Sales", 0.4 },
                    { "Support", 0.6 }
                }
            };

            _mockCallLogClient.Setup(x => x.GetCallSupportStatisticsAsync())
                .ReturnsAsync(expectedStatistics);

            // Act
            var result = await _callLogService.GetCallSupportStatisticsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedStatistics);
            _mockCallLogClient.Verify(x => x.GetCallSupportStatisticsAsync(), Times.Once);
        }

        [TestMethod]
        public async Task SearchCallLogsAsync_WithValidParams_ShouldReturnPageResult()
        {
            // Arrange
            var searchParams = new CallLogSearchParamsDto
            {
                PageNo = 1,
                PageSize = 10,
                PhoneNumber = "13800138000",
                CustomerName = "张三",
                Company = "测试公司",
                Email = "<EMAIL>",
                IntentType = 1,
                StartDate = "2024-01-01",
                EndDate = "2024-01-31"
            };

            var expectedRecords = new List<CallLogRecordDto>
            {
                new CallLogRecordDto
                {
                    Id = 1,
                    PhoneNumber = "13800138000",
                    CustomerName = "张三",
                    Company = "测试公司",
                    Email = "<EMAIL>",
                    IntentType = 1,
                    Status = 1,
                    StatusLabel = "",
                    CallTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                }
            };

            var expectedPageResult = new CallLogPageDto<List<CallLogRecordDto>>
            {
                CurrentPage = 1,
                PageSize = 10,
                Total = 1,
                Records = expectedRecords
            };

            _mockCallLogClient.Setup(x => x.SearchCallLogsAsync(It.IsAny<CallLogSearchParamsDto>()))
                .ReturnsAsync(expectedPageResult);

            // Act
            var result = await _callLogService.SearchCallLogsAsync(searchParams);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedPageResult);
            _mockCallLogClient.Verify(x => x.SearchCallLogsAsync(It.Is<CallLogSearchParamsDto>(p =>
                p.PageNo == searchParams.PageNo &&
                p.PageSize == searchParams.PageSize &&
                p.PhoneNumber == searchParams.PhoneNumber &&
                p.CustomerName == searchParams.CustomerName &&
                p.Company == searchParams.Company &&
                p.Email == searchParams.Email &&
                p.IntentType == searchParams.IntentType &&
                p.StartDate == searchParams.StartDate &&
                p.EndDate == searchParams.EndDate
            )), Times.Once);
        }

        [TestMethod]
        public async Task SearchCallLogsAsync_WithDefaultParams_ShouldSetDefaultValues()
        {
            // Arrange
            var searchParams = new CallLogSearchParamsDto
            {
                PageNo = 0,
                PageSize = 0
            };

            var expectedPageResult = new CallLogPageDto<List<CallLogRecordDto>>
            {
                CurrentPage = 1,
                PageSize = 10,
                Total = 0,
                Records = new List<CallLogRecordDto>()
            };

            _mockCallLogClient.Setup(x => x.SearchCallLogsAsync(It.IsAny<CallLogSearchParamsDto>()))
                .ReturnsAsync(expectedPageResult);

            // Act
            var result = await _callLogService.SearchCallLogsAsync(searchParams);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedPageResult);
            _mockCallLogClient.Verify(x => x.SearchCallLogsAsync(It.Is<CallLogSearchParamsDto>(p =>
                p.PageNo == 1 &&
                p.PageSize == 10
            )), Times.Once);
        }

        [TestMethod]
        public async Task SearchCallLogsAsync_WithMaxPageSize_ShouldLimitPageSize()
        {
            // Arrange
            var searchParams = new CallLogSearchParamsDto
            {
                PageNo = 1,
                PageSize = 1000
            };

            var expectedPageResult = new CallLogPageDto<List<CallLogRecordDto>>
            {
                CurrentPage = 1,
                PageSize = 100,
                Total = 0,
                Records = new List<CallLogRecordDto>()
            };

            _mockCallLogClient.Setup(x => x.SearchCallLogsAsync(It.IsAny<CallLogSearchParamsDto>()))
                .ReturnsAsync(expectedPageResult);

            // Act
            var result = await _callLogService.SearchCallLogsAsync(searchParams);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedPageResult);
            _mockCallLogClient.Verify(x => x.SearchCallLogsAsync(It.Is<CallLogSearchParamsDto>(p =>
                p.PageNo == 1 &&
                p.PageSize == 100
            )), Times.Once);
        }
    }
}