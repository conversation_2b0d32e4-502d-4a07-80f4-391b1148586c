using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using FluentAssertions;
using AutoMapper;
using Microsoft.Extensions.Logging;
using AgentCentral.Application.Contracts.RequestModels.App;
using AgentCentral.Application.Contracts.ResponseModels.App;
using AgentCentral.Application.Contracts.Dtos.BaseDto;
using AgentCentral.Infrastructure.Exceptions;
using AgentCentral.Application.Services;
using AgentCentral.Application.Contracts.IServices;
using AgentCentral.Domain.Shared.Models;
using AgentCentral.Domain.Shared.Enums;
using AgentCentral.Application.Contracts.Dtos.App;

namespace AgentCentral.WebApiTests.Services
{
    [TestClass]
    public class AppServiceTests
    {
        private Mock<IAppService> _mockAppService;
        private Mock<IMapper> _mockMapper;
        private Mock<ILogger<AppService>> _mockLogger;
        private Mock<UserContext> _mockUserContext;
        private AppService _appService;

        [TestInitialize]
        public void Setup()
        {
            _mockAppService = new Mock<IAppService>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<AppService>>();
            _mockUserContext = new Mock<UserContext>();

            _mockUserContext.Setup(x => x.UserId).Returns(1);
        }

        [TestMethod]
        public async Task GetAllAsync_ShouldReturnAllApps()
        {
            // Arrange
            var appResponses = new List<AppDetailResponse>
            {
                new AppDetailResponse { AppId = "app1", AppName = "Test App 1" },
                new AppDetailResponse { AppId = "app2", AppName = "Test App 2" }
            };

            _mockAppService.Setup(x => x.GetMyAllAsync()).ReturnsAsync(appResponses);

            // Act
            var result = await _mockAppService.Object.GetMyAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(appResponses);
        }

        [TestMethod]
        public async Task GetByIdAsync_WithValidId_ShouldReturnApp()
        {
            // Arrange
            string appId = "1";
            var appResponse = new AppDetailResponse { AppId = "app1", AppName = "Test App" };

            _mockAppService.Setup(x => x.GetByAppIdAsync(appId)).ReturnsAsync(appResponse);

            // Act
            var result = await _mockAppService.Object.GetByAppIdAsync(appId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(appResponse);
        }

        [TestMethod]
        public async Task GetByIdAsync_WithInvalidId_ShouldThrowException()
        {
            // Arrange
            string appId = "1";
            _mockAppService.Setup(x => x.GetByAppIdAsync(appId))
                .ThrowsAsync(new AgentCentralException("App not found"));

            // Act & Assert
            await _mockAppService.Object.Invoking(x => x.GetByAppIdAsync(appId))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("App not found");
        }

        [TestMethod]
        public async Task CreateAsync_WithValidRequest_ShouldCreateApp()
        {
            // Arrange
            var request = new CreateAppRequest { AppId = "app1", AppName = "Test App" };
            var appResponse = new AppDetailResponse
            {
                AppId = "app1",
                AppName = "Test App",
                CreateBy = 1,
                CreateName = "TestUser"
            };

            _mockAppService.Setup(x => x.CreateAsync(request)).ReturnsAsync(appResponse);

            // Act
            var result = await _mockAppService.Object.CreateAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(appResponse);
        }

        [TestMethod]
        public async Task CreateAsync_WithDuplicateAppId_ShouldThrowException()
        {
            // Arrange
            var request = new CreateAppRequest { AppId = "app1", AppName = "Test App" };
            _mockAppService.Setup(x => x.CreateAsync(request))
                .ThrowsAsync(new AgentCentralException("AppId already exists"));

            // Act & Assert
            await _mockAppService.Object.Invoking(x => x.CreateAsync(request))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("AppId already exists");
        }

        [TestMethod]
        public async Task UpdateAsync_WithValidRequest_ShouldUpdateApp()
        {
            // Arrange
            var request = new UpdateAppRequest { AppName = "Updated App" };
            var updatedAppResponse = new AppDetailResponse
            {
                AppId = "appId",
                AppName = "Updated App"
            };

            _mockAppService.Setup(x => x.UpdateAsync(updatedAppResponse.AppId, request)).ReturnsAsync(updatedAppResponse);

            // Act
            var result = await _mockAppService.Object.UpdateAsync(updatedAppResponse.AppId, request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(updatedAppResponse);
        }

        [TestMethod]
        public async Task UpdateAsync_WithInvalidAppId_ShouldThrowException()
        {
            // Arrange
            string appId = "invalid_app";
            var request = new UpdateAppRequest { AppName = "Updated App" };
            _mockAppService.Setup(x => x.UpdateAsync(appId, request))
                .ThrowsAsync(new AgentCentralException("App not found"));

            // Act & Assert
            await _mockAppService.Object.Invoking(x => x.UpdateAsync(appId, request))
                .Should().ThrowAsync<AgentCentralException>()
                .WithMessage("App not found");
        }

        [TestMethod]
        public async Task DeleteAsync_WithValidId_ShouldDeleteApp()
        {
            // Arrange
            string appId = "app1";
            _mockAppService.Setup(x => x.DeleteAsync(appId)).ReturnsAsync(true);

            // Act
            var result = await _mockAppService.Object.DeleteAsync(appId);

            // Assert
            result.Should().BeTrue();
        }

        [TestMethod]
        public async Task GetPageListAsync_ShouldReturnPagedResults()
        {
            // Arrange
            var searchDto = new AppSearchPageDto { PageIndex = 1, PageSize = 10 };
            var appResponses = new List<AppDetailPageResponse>
            {
                new AppDetailPageResponse { AppId = "app1", AppName = "Test App 1" },
                new AppDetailPageResponse { AppId = "app2", AppName = "Test App 2" }
            };
            var pagedResult = new PageModelDto<AppDetailPageResponse>(1, 10, appResponses, 2);

            _mockAppService.Setup(x => x.GetPageListAsync(searchDto, AppQueryModeEnum.All)).ReturnsAsync(pagedResult);

            // Act
            var result = await _mockAppService.Object.GetPageListAsync(searchDto);

            // Assert
            result.Should().NotBeNull();
            result.PageIndex.Should().Be(1);
            result.PageSize.Should().Be(10);
            result.Total.Should().Be(2);
            result.Data.Should().BeEquivalentTo(appResponses);
        }

        [TestMethod]
        public async Task GetPageListAsync_WithSearchCriteria_ShouldFilterResults()
        {
            // Arrange
            var searchDto = new AppSearchPageDto
            {
                PageIndex = 1,
                PageSize = 10,
                SearchText = "Test",
                AppMode = "Mode1",
                IsAsc = true,
            };

            var appResponses = new List<AppDetailPageResponse>
            {
                new AppDetailPageResponse {  AppId = "app1", AppName = "Test App 1" }
            };
            var pagedResult = new PageModelDto<AppDetailPageResponse>(1, 10, appResponses, 1);

            _mockAppService.Setup(x => x.GetPageListAsync(searchDto, AppQueryModeEnum.All)).ReturnsAsync(pagedResult);

            // Act
            var result = await _mockAppService.Object.GetPageListAsync(searchDto);

            // Assert
            result.Should().NotBeNull();
            result.Total.Should().Be(1);
            result.Data.Should().HaveCount(1);
            result.Data.Should().BeEquivalentTo(appResponses);
        }

        [TestMethod]
        public async Task GetReviewPageListAsync_ShouldSortByWorkflowLogApprovalDate()
        {
            // Arrange
            var searchDto = new AppReviewSearchPageDto
            {
                PageIndex = 1,
                PageSize = 10,
                IsAsc = false,
                ReviewStatus = 1
            };

            var appResponses = new List<AppReviewDetailPageResponse>
            {
                new AppReviewDetailPageResponse
                {
                    AppId = "app1",
                    AppName = "Test App 1",
                    CreateTime = DateTime.Now.AddDays(-1)
                },
                new AppReviewDetailPageResponse
                {
                    AppId = "app2",
                    AppName = "Test App 2",
                    CreateTime = DateTime.Now
                }
            };

            var pagedResult = new PageModelDto<AppReviewDetailPageResponse>(1, 10, appResponses, 2);

            _mockAppService.Setup(x => x.GetReviewPageListAsync(It.IsAny<AppReviewSearchPageDto>()))
                .ReturnsAsync(pagedResult);

            // Act
            var result = await _mockAppService.Object.GetReviewPageListAsync(searchDto);

            // Assert
            result.Should().NotBeNull();
            result.Total.Should().Be(2);
            result.Data.Should().HaveCount(2);
            result.Data.Should().BeInDescendingOrder(x => x.CreateTime);
        }

        [TestMethod]
        public async Task GetReviewPageListAsync_WithUnderReviewStatus_ShouldReturnCorrectResults()
        {
            // Arrange
            var searchDto = new AppReviewSearchPageDto
            {
                PageIndex = 1,
                PageSize = 10,
                ReviewStatus = 1,
                Status = AppStatusEnum.UnderReview
            };

            var appResponses = new List<AppReviewDetailPageResponse>
            {
                new AppReviewDetailPageResponse
                {
                    AppId = "app1",
                    AppName = "Test App 1",
                    AppStatus = AppStatusEnum.UnderReview
                }
            };

            var pagedResult = new PageModelDto<AppReviewDetailPageResponse>(1, 10, appResponses, 1);

            _mockAppService.Setup(x => x.GetReviewPageListAsync(It.IsAny<AppReviewSearchPageDto>()))
                .ReturnsAsync(pagedResult);

            // Act
            var result = await _mockAppService.Object.GetReviewPageListAsync(searchDto);

            // Assert
            result.Should().NotBeNull();
            result.Total.Should().Be(1);
            result.Data.Should().HaveCount(1);
            result.Data.First().AppStatus.Should().Be(AppStatusEnum.UnderReview);
        }
    }
}